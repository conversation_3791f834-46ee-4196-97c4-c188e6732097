  # =============================================================================
  # Cell 4: Enhanced Forensic Pipeline Setup with Attire/Privacy Analysis
  # =============================================================================
  import os
  import torch
  import whisper
  import subprocess
  import librosa
  import numpy as np
  from datetime import datetime, timedelta
  from pyannote.audio import Pipeline
  from sklearn.cluster import KMeans
  import base64
  import cv2
  from PIL import Image

  device = "cuda" if torch.cuda.is_available() else "cpu"
  print(f"Using device: {device}")

  LEGAL_TRIGGER_WORDS = [
      "arrest", "detained", "miranda", "rights", "warrant", "search", "seizure",
      "consent", "constitutional", "fourth amendment", "fifth amendment",
      "baker act", "mental health", "crisis", "suicide", "self harm",
      "force", "taser", "pepper spray", "baton", "firearm", "weapon",
      "assault", "battery", "resistance", "compliance", "cooperation",
      "medical", "injury", "pain", "breathing", "unconscious", "responsive",
      "supervisor", "sergeant", "lieutenant", "backup", "ambulance", "ems",
      "lawsuit", "carolina", "palm beach", "officer", "sheriff", "5150",
      "order", "refusal", "psych", "RPO", "sane", "suicidal", "husband",
      "combative", "harold", "hastings", "gun", "shotgun", "welfare", "lucid",
      "hands up", "get down", "stop resisting", "calm down", "relax", "towel",
      "naked", "undressed", "barefoot", "wet", "shower", "bathroom", "cuff", "cuffs"
  ]

  def enhanced_audio_processing_for_difficult_sections(input_path, output_path):
      """Multi-pass audio enhancement for challenging sections"""
      print("🔊 Enhanced audio processing for difficult sections...")

      # Pass 1: Normalize volume and compress dynamic range for distant speakers
      pass1_path = "/content/audio_pass1.wav"
      cmd1 = [
          'ffmpeg', '-y', '-i', input_path,
          '-af',
  'dynaudnorm=p=0.9:s=5,compand=attacks=0.1:decays=0.5:points=-90/-90|-60/-40|-40/-25|-25/-15|-10/-10',
          '-acodec', 'pcm_s16le', '-ar', '16000', '-ac', '1',
          pass1_path
      ]
      subprocess.run(cmd1, capture_output=True)

      # Pass 2: Enhance speech frequencies and reduce background noise
      pass2_path = "/content/audio_pass2.wav"
      cmd2 = [
          'ffmpeg', '-y', '-i', pass1_path,
          '-af', 'highpass=f=80,lowpass=f=8000,equalizer=f=2000:width_type=h:width=1000:g=3',
          '-acodec', 'pcm_s16le',
          pass2_path
      ]
      subprocess.run(cmd2, capture_output=True)

      # Pass 3: Handle loud shouting and volume spikes
      cmd3 = [
          'ffmpeg', '-y', '-i', pass2_path,
          '-af', 'alimiter=level_in=1:level_out=0.8:limit=0.9,volume=1.5',
          '-acodec', 'pcm_s16le',
          output_path
      ]
      subprocess.run(cmd3, capture_output=True)

      print(f"✅ Enhanced audio saved: {output_path}")

  def transcribe_with_maximum_accuracy_enhanced(audio_path):
      """Enhanced Whisper transcription with anti-hallucination settings"""
      print("🎙️ Loading Whisper Large-v3 for maximum accuracy...")

      model = whisper.load_model("large-v3", device=device)

      print("🔄 Transcribing with enhanced settings...")
      result = model.transcribe(
          audio_path,
          language="en",
          word_timestamps=True,
          temperature=0,
          beam_size=5,
          best_of=5,
          condition_on_previous_text=False,
          compression_ratio_threshold=1.8,
          logprob_threshold=-0.5,
          no_speech_threshold=0.4,
          initial_prompt="This is a police body camera recording with multiple speakers including officers,
  civilians, and dispatch. Audio may include shouting, distant speech, and overlapping conversations.",
          suppress_tokens=[50257]
      )

      print(f"✅ Transcription complete: {len(result['text'])} characters")
      return result

  def analyze_video_frames_for_context_enhanced_attire(video_path, skip_seconds=30):
      """FIXED: Enhanced video analysis with specific attire and privacy detection using GPT-4o"""
      print("📹 Analyzing video frames with enhanced attire/privacy detection...")

      frames_dir = "/content/video_frames"
      os.makedirs(frames_dir, exist_ok=True)

      extract_frames_cmd = [
          'ffmpeg', '-y', '-ss', str(skip_seconds), '-i', video_path,
          '-vf', 'fps=1/30', '-q:v', '2', f'{frames_dir}/frame_%04d.jpg'
      ]
      subprocess.run(extract_frames_cmd, capture_output=True)

      frame_files = sorted([f for f in os.listdir(frames_dir) if f.endswith('.jpg')])
      visual_context = []

      print(f"🔍 Analyzing {len(frame_files)} video frames with attire focus...")

      for i, frame_file in enumerate(frame_files):
          frame_path = os.path.join(frames_dir, frame_file)
          timestamp = (i * 30) + skip_seconds

          try:
              with open(frame_path, 'rb') as f:
                  frame_data = base64.b64encode(f.read()).decode()

              response = openai.ChatCompletion.create(
                  model="gpt-4o",  # FIXED: Updated from deprecated gpt-4-vision-preview
                  messages=[
                      {
                          "role": "user",
                          "content": [
                              {
                                  "type": "text",
                                  "text": """Conduct detailed forensic analysis of this police bodycam frame with
  SPECIFIC ATTENTION to clothing and privacy issues:

  1. ATTIRE ANALYSIS (CRITICAL FOR PRIVACY ASSESSMENT):
     - Clothing status: Fully dressed, partially dressed, minimal clothing, towel only, etc.
     - State of dress: Appropriate, inappropriate for public, emergency exit clothing
     - Signs of rushed dressing: Incomplete clothing, towel wrapped, wet appearance
     - Modesty concerns: Areas of body exposed, coverage inadequacy
     - Footwear: Barefoot, shoes, slippers (indicates emergency exit)

  2. PRIVACY & DIGNITY INDICATORS:
     - Public exposure level: Private home vs. public view
     - Bystander presence: Neighbors, crowds, passersby witnessing exposure
     - Recording implications: Subject aware of being filmed in state of undress
     - Weather conditions affecting minimal clothing exposure

  3. EMERGENCY/CRISIS INDICATORS:
     - Wet hair/body (shower interruption)
     - Rushed appearance (hastily grabbed clothing/towel)
     - Bathroom/shower context (wet floors, steam, towels visible)
     - Time pressure indicators (incomplete dressing)

  4. RESTRAINT/HANDCUFFING ANALYSIS:
     - Handcuff application on subject in minimal clothing
     - Positioning: hands behind back while in towel/minimal clothing
     - Dignity concerns during restraint application
     - Cooperative behavior vs. restraint necessity

  5. STANDARD FORENSIC ELEMENTS:
     - Scene setting and location context
     - People positions and actions
     - Equipment and evidence visible
     - Officer positioning relative to undressed subject

  6. CONSTITUTIONAL CONCERNS:
     - 4th Amendment: Privacy expectations in home
     - 8th Amendment: Dignity during detention
     - Public exposure creating humiliation
     - Reasonable accommodation for clothing needs

  Be extremely specific about clothing status, restraint application, and privacy implications. Flag any dignitary
  concerns or inappropriate exposure situations."""
                              },
                              {
                                  "type": "image_url",
                                  "image_url": {
                                      "url": f"data:image/jpeg;base64,{frame_data}"
                                  }
                              }
                          ]
                      }
                  ],
                  max_tokens=600,
                  temperature=0.1
              )

              visual_analysis = response.choices[0].message.content
              visual_context.append({
                  'timestamp': timestamp,
                  'frame': frame_file,
                  'analysis': visual_analysis
              })

              print(f"✅ Enhanced frame analysis: {timestamp//60:02d}:{timestamp%60:02d}")

          except Exception as e:
              print(f"⚠️ Frame analysis failed for {frame_file}: {e}")
              visual_context.append({
                  'timestamp': timestamp,
                  'frame': frame_file,
                  'analysis': f"Visual analysis unavailable: {e}"
              })

      print(f"✅ Enhanced visual context analysis complete: {len(visual_context)} frames")
      return visual_context

  def detect_speaker_overlaps_and_separate_enhanced(audio_path, diarization_result, whisper_result):
      """Enhanced speaker overlap detection with better sensitivity"""
      print("👥 Enhanced speaker overlap detection...")

      overlaps = []

      diar_segments = []
      for turn, _, speaker in diarization_result.itertracks(yield_label=True):
          diar_segments.append({
              'start': turn.start,
              'end': turn.end,
              'speaker': speaker
          })

      for i, seg1 in enumerate(diar_segments):
          for seg2 in diar_segments[i+1:]:
              overlap_start = max(seg1['start'], seg2['start'])
              overlap_end = min(seg1['end'], seg2['end'])

              if overlap_start < overlap_end:
                  duration = overlap_end - overlap_start
                  if duration > 0.3:  # Lowered threshold from 0.5 to catch more overlaps
                      overlaps.append({
                          'start': overlap_start,
                          'end': overlap_end,
                          'duration': duration,
                          'speakers': [seg1['speaker'], seg2['speaker']]
                      })

      print(f"✅ Enhanced overlap detection complete: {len(overlaps)} overlaps found")
      return overlaps

  def combine_transcription_and_speakers_enhanced(whisper_result, diarization_result, overlaps):
      """Enhanced combination with better word-level speaker assignment"""
      print("🔗 Enhanced transcription and speaker combination...")

      enhanced_transcript = []

      for segment in whisper_result['segments']:
          for word_info in segment.get('words', []):
              word_start = word_info['start']
              word_end = word_info['end']
              word_text = word_info['word']
              word_confidence = word_info.get('probability', 0.0)

              speakers = []
              tolerance = 0.1

              for turn, _, speaker in diarization_result.itertracks(yield_label=True):
                  if (turn.start - tolerance) <= word_start <= (turn.end + tolerance):
                      speakers.append(speaker)

              is_overlap = False
              overlap_speakers = []
              for overlap in overlaps:
                  if overlap['start'] <= word_start <= overlap['end']:
                      is_overlap = True
                      overlap_speakers = overlap['speakers']
                      break

              enhanced_transcript.append({
                  'word': word_text,
                  'start': word_start,
                  'end': word_end,
                  'confidence': word_confidence,
                  'speakers': speakers,
                  'overlap': is_overlap,
                  'overlap_speakers': overlap_speakers
              })

      print(f"✅ Enhanced transcript created: {len(enhanced_transcript)} words")
      return enhanced_transcript

  def analyze_privacy_dignity_violations_enhanced(enhanced_transcript, visual_context, skip_seconds=30):
      """Enhanced privacy and dignity analysis with specific attire focus"""
      print("🔒 Enhanced privacy and dignity violations analysis...")

      privacy_violations = []
      dignity_violations = []
      attire_violations = []
      public_exposure_incidents = []

      # Enhanced keywords for clothing/attire situations
      attire_keywords = ['towel', 'naked', 'undressed', 'partially clothed', 'minimal clothing',
                        'barefoot', 'wet', 'shower', 'bathing', 'bathroom', 'rushed', 'incomplete dress']

      emergency_exit_keywords = ['rushed out', 'hurried', 'interrupted', 'grabbed towel',
                                'wet hair', 'steam', 'bathroom door', 'shower interrupted']

      exposure_keywords = ['exposed', 'visible', 'uncovered', 'inappropriate', 'public view',
                          'neighbors seeing', 'crowd watching', 'filming', 'recording']

      handcuff_dignity_keywords = ['handcuffed', 'cuffed', 'restrained', 'hands behind back',
                                  'restraints applied', 'detained']

      # Analyze audio for clothing/exposure references
      for i, word_data in enumerate(enhanced_transcript):
          word_timestamp = word_data['start'] + skip_seconds
          word_text = word_data['word'].lower()
          speakers = word_data['speakers']

          # Check for attire-related violations
          if any(keyword in word_text for keyword in attire_keywords):
              visual_evidence = None
              for ctx in visual_context:
                  if abs(ctx['timestamp'] - word_timestamp) <= 60:
                      visual_evidence = ctx['analysis']
                      break

              attire_violations.append({
                  'timestamp': word_timestamp,
                  'audio_evidence': word_text,
                  'visual_evidence': visual_evidence[:300] if visual_evidence else 'No visual context',
                  'violation_type': 'Attire/Clothing Privacy Concern',
                  'speakers': speakers
              })

          # Check for handcuffing dignity concerns
          if any(keyword in word_text for keyword in handcuff_dignity_keywords):
              # Check if this occurs near attire violations
              attire_context = any(attire_word in enhanced_transcript[max(0, i-10):i+10][j]['word'].lower()
                                 for j in range(len(enhanced_transcript[max(0, i-10):i+10]))
                                 for attire_word in ['towel', 'naked', 'undressed', 'wet'])

              if attire_context:
                  dignity_violations.append({
                      'timestamp': word_timestamp,
                      'audio_evidence': word_text,
                      'violation_type': 'Handcuffing Individual in Minimal Clothing - Dignity Violation',
                      'speakers': speakers,
                      'severity': 'HIGH',
                      'constitutional_concern': '8th Amendment - Cruel and unusual punishment'
                  })

      # Enhanced visual analysis for clothing/exposure
      for ctx in visual_context:
          visual_analysis = ctx['analysis'].lower()

          # Check for clothing-related exposure
          clothing_indicators = ['towel only', 'minimal clothing', 'partially dressed', 'undressed',
                                'wet from shower', 'barefoot', 'rushed dressing', 'incomplete clothing']

          if any(indicator in visual_analysis for indicator in clothing_indicators):
              # Check if handcuffing is involved
              handcuff_indicators = ['handcuff', 'cuff', 'restrain', 'hands behind back']
              is_handcuffed = any(hc_word in visual_analysis for hc_word in handcuff_indicators)

              # Check if in public view
              public_indicators = ['public', 'neighbors', 'crowd', 'street', 'outside', 'porch', 'yard']
              is_public = any(pub_word in visual_analysis for pub_word in public_indicators)

              violation_type = 'Public Exposure - Minimal Clothing' if is_public else 'Private Exposure
  Documentation'

              if is_handcuffed:
                  violation_type += ' + Restraint Applied'

              public_exposure_incidents.append({
                  'timestamp': ctx['timestamp'],
                  'visual_evidence': ctx['analysis'],
                  'violation_type': violation_type,
                  'severity': 'CRITICAL' if (is_public and is_handcuffed) else 'HIGH' if is_public else 'MODERATE',
                  'clothing_status': 'MINIMAL/INADEQUATE',
                  'restraint_status': 'RESTRAINED' if is_handcuffed else 'UNRESTRAINED'
              })

      print(f"✅ Found {len(attire_violations)} attire/clothing violations")
      print(f"✅ Found {len(privacy_violations)} privacy violations")
      print(f"✅ Found {len(dignity_violations)} dignity violations")
      print(f"✅ Found {len(public_exposure_incidents)} public exposure incidents")

      return privacy_violations, dignity_violations, public_exposure_incidents, attire_violations

  def inject_attire_context_annotations(enhanced_transcript, visual_context, skip_seconds=30):
      """Inject specific attire and privacy context annotations"""
      print("💉 Injecting attire and privacy context annotations...")

      attire_annotations = {}

      for i, word_data in enumerate(enhanced_transcript):
          word_timestamp = word_data['start'] + skip_seconds
          word_text = word_data['word'].lower()

          # Find corresponding visual context for this word
          closest_visual = None
          min_time_diff = float('inf')

          for ctx in visual_context:
              time_diff = abs(ctx['timestamp'] - word_timestamp)
              if time_diff < min_time_diff and time_diff < 15:
                  min_time_diff = time_diff
                  closest_visual = ctx

          if closest_visual:
              visual_text = closest_visual['analysis'].lower()

              # Check for specific attire situations
              if any(indicator in visual_text for indicator in ['towel only', 'minimal clothing', 'undressed']):
                  attire_annotations[i] = "*{ATTIRE CONCERN: Subject in minimal clothing/towel only - Privacy
  implications}*"

              elif any(indicator in visual_text for indicator in ['wet from shower', 'rushed from bathroom']):
                  attire_annotations[i] = "*{EMERGENCY EXIT: Subject interrupted during private activity -
  Constitutional privacy concern}*"

              elif any(indicator in visual_text for indicator in ['handcuff', 'restrain']) and any(attire in
  visual_text for attire in ['towel', 'minimal', 'undressed']):
                  attire_annotations[i] = "*{CRITICAL DIGNITY VIOLATION: Restraint applied to subject in minimal
  clothing - 8th Amendment concern}*"

              elif any(indicator in visual_text for indicator in ['public exposure', 'neighbors seeing']):
                  attire_annotations[i] = "*{PUBLIC EXPOSURE: Inappropriate clothing status in public view -
  Dignity violation}*"

              elif any(indicator in visual_text for indicator in ['barefoot', 'incomplete dress']):
                  attire_annotations[i] = "*{RUSHED DRESSING: Emergency exit indicators - Privacy interruption
  documented}*"

      print(f"✅ Attire context annotations prepared: {len(attire_annotations)} annotations")
      return attire_annotations

  def analyze_with_gpt4_forensic_enhanced(transcript_text, speaker_segments, trigger_words, visual_context):
      """Enhanced GPT-4 forensic analysis incorporating both audio and visual data"""
      print("🧠 Running enhanced GPT-4 forensic analysis...")

      # Combine visual context for analysis
      visual_summary = "\n".join([
          f"[{ctx['timestamp']//60:02d}:{ctx['timestamp']%60:02d}] VISUAL: {ctx['analysis']}"
          for ctx in visual_context[:10]
      ])

      system_prompt = """You are a certified forensic audiovisual analyst with 25+ years experience in criminal
  procedure, constitutional law (42 U.S.C. § 1983), and police misconduct analysis. You have served as a
  court-appointed expert witness and specialize in integrated audio-visual evidence analysis.

  Conduct comprehensive forensic analysis incorporating both audio transcript and visual frame analysis for:

  1. CONSTITUTIONAL VIOLATIONS:
     - 4th Amendment (search/seizure without warrant, privacy expectations in home)
     - 5th Amendment (Miranda rights, self-incrimination)
     - 8th Amendment (excessive force, cruel treatment, dignity violations)
     - 14th Amendment (due process, equal protection)

  2. STATUTORY VIOLATIONS:
     - Florida Statutes (Baker Act § 394.463)
     - Arrest authority compliance (Ch. 901)
     - Mental health detention protocols
     - Transport and medical clearance requirements

  3. DIGNITY AND PRIVACY VIOLATIONS:
     - Public exposure of individuals in minimal clothing
     - Restraint application to cooperative subjects in inadequate dress
     - Emergency privacy interruptions (shower, bathroom)
     - Handcuffing subjects in towels or minimal clothing

  4. USE OF FORCE ASSESSMENT:
     - Graham v. Connor standards compliance
     - Proportionality analysis (visual evidence critical)
     - De-escalation attempts/failures
     - Restraint necessity vs. cooperation level

  5. AUDIO-VISUAL CORRELATION:
     - Consistency between spoken actions and visual evidence
     - Body language vs verbal compliance
     - Environmental factors affecting behavior
     - Officer positioning and tactical decisions

  Provide specific timestamps, direct quotes, visual observations, legal significance, and court-admissible
  analysis with integrated audio-visual evidence correlation."""

      user_prompt = f"""
  POLICE BODYCAM INTEGRATED AUDIO-VISUAL ANALYSIS:

  AUDIO TRANSCRIPT (First 8000 characters):
  {transcript_text[:8000]}

  VISUAL FRAME ANALYSIS:
  {visual_summary}

  LEGAL TRIGGER WORDS DETECTED:
  {', '.join(trigger_words)}

  SPEAKER COUNT: {len(set(seg.get('speaker', 'Unknown') for seg in speaker_segments))}

  Provide comprehensive integrated forensic analysis with:
  - Constitutional and statutory violations (cite specific evidence)
  - Critical timeline events with both audio and visual timestamps
  - Dignity and privacy violation analysis with specific attention to restraint of minimally clothed individuals
  - Use of force analysis with visual evidence correlation
  - Risk assessment for legal proceedings
  - Evidence preservation recommendations
  - Audio-visual consistency analysis
  """

      try:
          response = openai.ChatCompletion.create(
              model="gpt-4",
              messages=[
                  {"role": "system", "content": system_prompt},
                  {"role": "user", "content": user_prompt}
              ],
              max_tokens=4000,
              temperature=0.05
          )

          return response.choices[0].message.content

      except Exception as e:
          print(f"❌ GPT-4 analysis failed: {e}")
          return f"GPT-4 analysis unavailable: {e}"

  def inject_contextual_annotations_enhanced(enhanced_transcript):
      """Enhanced contextual legal/psychological annotations"""
      print("💉 Injecting enhanced contextual annotations...")

      annotations = {}

      for i, word_data in enumerate(enhanced_transcript):
          text = word_data.get('word', '').lower()

          # Enhanced legal trigger detection
          if any(word in text for word in ['miranda', 'rights', 'remain silent']):
              annotations[i] = "*{Miranda rights advisement - 5th Amendment constitutional requirement}*"
          elif any(word in text for word in ['force', 'taser', 'weapon', 'gun']):
              annotations[i] = "*{Use of force deployment - Graham v. Connor analysis required}*"
          elif any(word in text for word in ['baker act', 'mental health', 'crisis', '5150']):
              annotations[i] = "*{Mental health detention protocol - Fla. Stat. § 394.463}*"
          elif any(word in text for word in ['search', 'seizure']):
              annotations[i] = "*{4th Amendment search/seizure activity - warrant requirement analysis}*"
          elif any(word in text for word in ['consent', 'permission']):
              annotations[i] = "*{Consent documentation - voluntariness analysis required}*"
          elif any(word in text for word in ['supervisor', 'sergeant', 'lieutenant']):
              annotations[i] = "*{Supervisory involvement - chain of command protocol}*"
          elif any(word in text for word in ['ambulance', 'ems', 'medical', 'injury']):
              annotations[i] = "*{Medical intervention - duty of care assessment}*"
          elif any(word in text for word in ['hands up', 'get down', 'stop']):
              annotations[i] = "*{Compliance directive - officer command analysis}*"
          elif any(word in text for word in ['calm down', 'relax', 'breathe']):
              annotations[i] = "*{De-escalation attempt - crisis intervention technique}*"
          elif any(word in text for word in ['cuff', 'cuffs', 'handcuff']):
              annotations[i] = "*{RESTRAINT APPLICATION: Handcuffing procedure - dignity and necessity analysis
  required}*"
          elif any(word in text for word in ['towel', 'naked', 'undressed']):
              annotations[i] = "*{ATTIRE CONCERN: Minimal clothing status - privacy and dignity implications}*"

      return annotations

  print("✅ Enhanced forensic pipeline functions with attire analysis loaded successfully!")