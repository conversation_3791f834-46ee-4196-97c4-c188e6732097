{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "machine_shape": "hm", "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU", "widgets": {"application/vnd.jupyter.widget-state+json": {"857d0ab38b574b3a8d34b06e39da685c": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ddc4f1a7f3804099ba8a7722b5ad603d", "IPY_MODEL_aa941722e29b46089cf09820bf86295c", "IPY_MODEL_225e8209a3844b22aa27286dc0afb00f"], "layout": "IPY_MODEL_cb915a3ed332471d9c3db12f52bdc10e"}}, "ddc4f1a7f3804099ba8a7722b5ad603d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9df41b94d7594975b7cf18162296ad70", "placeholder": "​", "style": "IPY_MODEL_8985a5112f6a454ea3871994be9e4779", "value": "config.yaml: 100%"}}, "aa941722e29b46089cf09820bf86295c": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_89e02fe58afe46419ac6561a9abcf0fc", "max": 469, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_812253386b6a4e99933811cb623cc916", "value": 469}}, "225e8209a3844b22aa27286dc0afb00f": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_936cd3fa49674dd5b99dd03d6b86b50c", "placeholder": "​", "style": "IPY_MODEL_27301b53b8c742e4adc96ffe390d7fe6", "value": " 469/469 [00:00&lt;00:00, 53.0kB/s]"}}, "cb915a3ed332471d9c3db12f52bdc10e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9df41b94d7594975b7cf18162296ad70": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8985a5112f6a454ea3871994be9e4779": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "89e02fe58afe46419ac6561a9abcf0fc": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "812253386b6a4e99933811cb623cc916": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "936cd3fa49674dd5b99dd03d6b86b50c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "27301b53b8c742e4adc96ffe390d7fe6": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6396a6cc71e045f68255d37eec834c42": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9c7224858c5d476b8cd0f4ffc5cdde61", "IPY_MODEL_ebba6ac9d9e64270b4d25d6664f89b53", "IPY_MODEL_fe0f29732c974dc38211580c3abcdcbe"], "layout": "IPY_MODEL_79320209d9244569b42444760e39a1d7"}}, "9c7224858c5d476b8cd0f4ffc5cdde61": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5f32c686160040509cfe950a0278364f", "placeholder": "​", "style": "IPY_MODEL_8f6d349abeb34cc1a901b5257e14059e", "value": "pytorch_model.bin: 100%"}}, "ebba6ac9d9e64270b4d25d6664f89b53": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_83b9dfe2b3bb45c1995c2096fb65fabe", "max": 5905440, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2cd8df1f944942c8b13c8491d581ab83", "value": 5905440}}, "fe0f29732c974dc38211580c3abcdcbe": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b2c6cbb083614496b621c17b52bac577", "placeholder": "​", "style": "IPY_MODEL_7e4e892851dd48739848e8870abdff21", "value": " 5.91M/5.91M [00:00&lt;00:00, 104MB/s]"}}, "79320209d9244569b42444760e39a1d7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5f32c686160040509cfe950a0278364f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8f6d349abeb34cc1a901b5257e14059e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "83b9dfe2b3bb45c1995c2096fb65fabe": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2cd8df1f944942c8b13c8491d581ab83": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b2c6cbb083614496b621c17b52bac577": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7e4e892851dd48739848e8870abdff21": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5ac1d8bed7ce4e10a96ddca019ce43f1": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_2f3d4d03573e408db86860c4307152c0", "IPY_MODEL_c893b7b87c934e198edf8a06cd98e340", "IPY_MODEL_bc67ad0be01943e69970c3a9298d8b3c"], "layout": "IPY_MODEL_ea4e34feb5ee42de926b717295192e88"}}, "2f3d4d03573e408db86860c4307152c0": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ba9dce58da3c4a65b7d35b602d2bfe21", "placeholder": "​", "style": "IPY_MODEL_697bb5aa6b5c4e4ea16be2939bea0de7", "value": "config.yaml: 100%"}}, "c893b7b87c934e198edf8a06cd98e340": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4ef09e2bdfe749b0bb19426d0734a326", "max": 399, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e3eae5fb958149b0af85bb90ef332915", "value": 399}}, "bc67ad0be01943e69970c3a9298d8b3c": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_11d98b88b7ed454bb65522098dfc4a6d", "placeholder": "​", "style": "IPY_MODEL_7b0e4cbd13174ef2a318ffb21c4d2cf7", "value": " 399/399 [00:00&lt;00:00, 48.1kB/s]"}}, "ea4e34feb5ee42de926b717295192e88": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ba9dce58da3c4a65b7d35b602d2bfe21": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "697bb5aa6b5c4e4ea16be2939bea0de7": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4ef09e2bdfe749b0bb19426d0734a326": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e3eae5fb958149b0af85bb90ef332915": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "11d98b88b7ed454bb65522098dfc4a6d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7b0e4cbd13174ef2a318ffb21c4d2cf7": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "135eaad9889845babe88c9cc399257d4": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_dce840a14bc6461981f64e4b5332873f", "IPY_MODEL_764d82e21ed84a99b082dd3be73fa0e4", "IPY_MODEL_3e0452917e674df885f0e176b3a023c3"], "layout": "IPY_MODEL_159932ffe3c54856ae8ada09ab11afc3"}}, "dce840a14bc6461981f64e4b5332873f": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1c6b2472ecf14caeb5cc9a0f4396bce6", "placeholder": "​", "style": "IPY_MODEL_1371d8b386f74c4f8c516654bbdb7732", "value": "pytorch_model.bin: 100%"}}, "764d82e21ed84a99b082dd3be73fa0e4": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d9eb8cdfc6ef41249228b1efb17445b3", "max": 26645418, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2beb134408454bc8b5cafe863fd50b26", "value": 26645418}}, "3e0452917e674df885f0e176b3a023c3": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_81800fcc4b8e488fbfc3cf0b7ccd28c2", "placeholder": "​", "style": "IPY_MODEL_2f2c1b3087d740df8441e62b7663dcfc", "value": " 26.6M/26.6M [00:00&lt;00:00, 75.3MB/s]"}}, "159932ffe3c54856ae8ada09ab11afc3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1c6b2472ecf14caeb5cc9a0f4396bce6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1371d8b386f74c4f8c516654bbdb7732": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d9eb8cdfc6ef41249228b1efb17445b3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2beb134408454bc8b5cafe863fd50b26": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "81800fcc4b8e488fbfc3cf0b7ccd28c2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2f2c1b3087d740df8441e62b7663dcfc": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0d1668720b5a46b8ae616cde434952ce": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1ac2572d365b4874ac4fe27dd68333d1", "IPY_MODEL_947a347fdbc1411cb0f501b54202c899", "IPY_MODEL_0a6396aead7845d0980e7859f775c230"], "layout": "IPY_MODEL_de5d12618a914ac886ee2e035fd830ae"}}, "1ac2572d365b4874ac4fe27dd68333d1": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6f175fb2170e4bb7a6d38ddbc7a9d0dc", "placeholder": "​", "style": "IPY_MODEL_88ac566e57ba4b8bba83a3b452424f4f", "value": "config.yaml: 100%"}}, "947a347fdbc1411cb0f501b54202c899": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d95b863aa7a748c18e17bc62e3c83eaa", "max": 221, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9f2faf7ffef64780955459fc968d5f7f", "value": 221}}, "0a6396aead7845d0980e7859f775c230": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_eea59c7bdef04e3db4824ae5cb609c93", "placeholder": "​", "style": "IPY_MODEL_5d75ff6fecd445c4ba3e60f7606c7290", "value": " 221/221 [00:00&lt;00:00, 23.0kB/s]"}}, "de5d12618a914ac886ee2e035fd830ae": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6f175fb2170e4bb7a6d38ddbc7a9d0dc": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "88ac566e57ba4b8bba83a3b452424f4f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d95b863aa7a748c18e17bc62e3c83eaa": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9f2faf7ffef64780955459fc968d5f7f": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "eea59c7bdef04e3db4824ae5cb609c93": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5d75ff6fecd445c4ba3e60f7606c7290": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3e9e5e75a56e446e8c43c9b7b185bf3a": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_4c30b2575810415f99c0e18cfe181348", "IPY_MODEL_c4148bd2f08a486dbd30f97aea41f9ef", "IPY_MODEL_75b7c16b8fe84e69bf1cc2f7ea5467c3"], "layout": "IPY_MODEL_42d39bc8bf4441bf8fcf14f46a478cb4"}}, "4c30b2575810415f99c0e18cfe181348": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3c7344b967bb4b7c9f5786c262a7dc7f", "placeholder": "​", "style": "IPY_MODEL_cf04de800d3c468abd9aa16b6ca60f65", "value": "config.json: 100%"}}, "c4148bd2f08a486dbd30f97aea41f9ef": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_127534b1d187497f8e9f0521b7b695ac", "max": 829, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f47ae5d3822b4116a97be9cf57310e4d", "value": 829}}, "75b7c16b8fe84e69bf1cc2f7ea5467c3": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_594c1090cef7488abe3e26be317b2ade", "placeholder": "​", "style": "IPY_MODEL_532d9bfe941746d8a091dec54a100584", "value": " 829/829 [00:00&lt;00:00, 101kB/s]"}}, "42d39bc8bf4441bf8fcf14f46a478cb4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3c7344b967bb4b7c9f5786c262a7dc7f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cf04de800d3c468abd9aa16b6ca60f65": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "127534b1d187497f8e9f0521b7b695ac": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f47ae5d3822b4116a97be9cf57310e4d": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "594c1090cef7488abe3e26be317b2ade": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "532d9bfe941746d8a091dec54a100584": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "93d50c11eb9e49f1b46a5c3652bf08cf": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c1c7980861534979ab829364e60826d2", "IPY_MODEL_b56bacb380044f4abc03d004f2ba46ed", "IPY_MODEL_1e78f155e69d4de5a75b187350f6fdac"], "layout": "IPY_MODEL_70a4281578db4dfcb30c87c50a35c60c"}}, "c1c7980861534979ab829364e60826d2": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c6c8e937685b400b841003ed728e2dc5", "placeholder": "​", "style": "IPY_MODEL_74df71cb81864b25b2fcf99a54c351f7", "value": "model.safetensors: 100%"}}, "b56bacb380044f4abc03d004f2ba46ed": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f738000d13324aeb8cca5d8041ea2fa6", "max": 433292294, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_aa150e1f394346588465206299b27579", "value": 433292294}}, "1e78f155e69d4de5a75b187350f6fdac": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b8f3fdd4182743188a11b8daabae7e38", "placeholder": "​", "style": "IPY_MODEL_3a101346404f419aa1dbb8a9f277ada9", "value": " 433M/433M [00:01&lt;00:00, 429MB/s]"}}, "70a4281578db4dfcb30c87c50a35c60c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c6c8e937685b400b841003ed728e2dc5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "74df71cb81864b25b2fcf99a54c351f7": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f738000d13324aeb8cca5d8041ea2fa6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "aa150e1f394346588465206299b27579": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b8f3fdd4182743188a11b8daabae7e38": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3a101346404f419aa1dbb8a9f277ada9": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8be046c3d03448e1a159fcc03ad0a2eb": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b0f81a51cab145e0a0f41fb54a64297f", "IPY_MODEL_367f1d72c2f043c3bd97567a95486bb7", "IPY_MODEL_95523f618a6442f29485c86b665c0edc"], "layout": "IPY_MODEL_449c4aefe30743199f094f8e5aa656e6"}}, "b0f81a51cab145e0a0f41fb54a64297f": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_df4633ff316a41bbb0c2225aeb9ea075", "placeholder": "​", "style": "IPY_MODEL_9184788afae548b486d24bfb263db7a7", "value": "tokenizer_config.json: 100%"}}, "367f1d72c2f043c3bd97567a95486bb7": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_263ed5b014224dc2bd3f8aac64bc4521", "max": 59, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f9d885a6c1104f6ba01328fdeef290dd", "value": 59}}, "95523f618a6442f29485c86b665c0edc": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2a665a355e73471f9a70b83145bceeeb", "placeholder": "​", "style": "IPY_MODEL_5ec6bccb980c4fdf90fdb870474c9bab", "value": " 59.0/59.0 [00:00&lt;00:00, 7.20kB/s]"}}, "449c4aefe30743199f094f8e5aa656e6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "df4633ff316a41bbb0c2225aeb9ea075": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9184788afae548b486d24bfb263db7a7": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "263ed5b014224dc2bd3f8aac64bc4521": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f9d885a6c1104f6ba01328fdeef290dd": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "2a665a355e73471f9a70b83145bceeeb": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5ec6bccb980c4fdf90fdb870474c9bab": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "286d1792240340b8b801b12590bb1ec6": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_0052d42d07a94dfb9ff28dfe6f5bb1e0", "IPY_MODEL_e0751f44a02a435f99496f0561e414ab", "IPY_MODEL_21b0047fec8041fe9712f6cbea58d12d"], "layout": "IPY_MODEL_d2aee77378b348e3a0db6895c565de68"}}, "0052d42d07a94dfb9ff28dfe6f5bb1e0": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d309d50323684c3386f070170f821e90", "placeholder": "​", "style": "IPY_MODEL_72513879cdbc4b04a5f228c4ec725ae4", "value": "vocab.txt: 100%"}}, "e0751f44a02a435f99496f0561e414ab": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_26537e39d50b42a09848f11e081bd814", "max": 213450, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_652e26fbdfcb4d90ae8a2d6404759c1a", "value": 213450}}, "21b0047fec8041fe9712f6cbea58d12d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_291ee664ba4f4615aee97a510d4182dc", "placeholder": "​", "style": "IPY_MODEL_63b9074ce0b74cb584a6a65cf5ba01fe", "value": " 213k/213k [00:00&lt;00:00, 11.4MB/s]"}}, "d2aee77378b348e3a0db6895c565de68": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d309d50323684c3386f070170f821e90": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "72513879cdbc4b04a5f228c4ec725ae4": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "26537e39d50b42a09848f11e081bd814": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "652e26fbdfcb4d90ae8a2d6404759c1a": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "291ee664ba4f4615aee97a510d4182dc": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "63b9074ce0b74cb584a6a65cf5ba01fe": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0003b5672f8f4e26946ecc5a4446c62a": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_6743aec06683471b84c20cdc73f2d963", "IPY_MODEL_04d1f7363c524d9b8891c64414c98d32", "IPY_MODEL_13c3af12a35049e7817b7960624c3db5"], "layout": "IPY_MODEL_8f4b58c234e648ab8ac0fdb3207e7721"}}, "6743aec06683471b84c20cdc73f2d963": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c4d84e63309a4f9796343cd2b2ef4e73", "placeholder": "​", "style": "IPY_MODEL_af95a08f111b4149b5e83c45bf69a074", "value": "added_tokens.json: 100%"}}, "04d1f7363c524d9b8891c64414c98d32": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8ea99825d2594d1b9fb8496ecf4ed7cf", "max": 2, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_8ab822ecdde14bbcaed9fe88dc8fdfbf", "value": 2}}, "13c3af12a35049e7817b7960624c3db5": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3449564568ff423f9bc78327ff1bab9f", "placeholder": "​", "style": "IPY_MODEL_828e8d480ac14db8b291db8e6d980cfd", "value": " 2.00/2.00 [00:00&lt;00:00, 270B/s]"}}, "8f4b58c234e648ab8ac0fdb3207e7721": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c4d84e63309a4f9796343cd2b2ef4e73": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "af95a08f111b4149b5e83c45bf69a074": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8ea99825d2594d1b9fb8496ecf4ed7cf": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8ab822ecdde14bbcaed9fe88dc8fdfbf": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "3449564568ff423f9bc78327ff1bab9f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "828e8d480ac14db8b291db8e6d980cfd": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "585e7a90784544449d416cda12ed5a82": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_d08724c849ac410eb66b10ba4c62f3a8", "IPY_MODEL_4ad2a1d2636844e495ae251b6b488e12", "IPY_MODEL_08b308907a894bbcaddb69bf00063bef"], "layout": "IPY_MODEL_2332739835f64277bb0dc17123cb487b"}}, "d08724c849ac410eb66b10ba4c62f3a8": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_723fba7f6d1b4cddadff5ba6e4a9b636", "placeholder": "​", "style": "IPY_MODEL_383ae57f1d784d22927b437a8359766d", "value": "special_tokens_map.json: 100%"}}, "4ad2a1d2636844e495ae251b6b488e12": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_316e939e129a475f88fdeee6a75ced6f", "max": 112, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_132d3426d45f46d895435cdc99bbb851", "value": 112}}, "08b308907a894bbcaddb69bf00063bef": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4766c5a75edc46a6abc722c65bbc4dbc", "placeholder": "​", "style": "IPY_MODEL_f9282f58adb04e9ead82d5616d30dabc", "value": " 112/112 [00:00&lt;00:00, 12.8kB/s]"}}, "2332739835f64277bb0dc17123cb487b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "723fba7f6d1b4cddadff5ba6e4a9b636": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "383ae57f1d784d22927b437a8359766d": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "316e939e129a475f88fdeee6a75ced6f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "132d3426d45f46d895435cdc99bbb851": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "4766c5a75edc46a6abc722c65bbc4dbc": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f9282f58adb04e9ead82d5616d30dabc": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "ZGdk7cPe3os7"}, "outputs": [], "source": ["# COMPLE<PERSON> ENHANCED FORENSIC TRANSCRIPTION PIPELINE"]}, {"cell_type": "code", "source": ["# =============================================================================\n", "# Cell 1: Install Dependencies with Correct Versions\n", "# =============================================================================\n", "# Google Colab + WhisperX + CUDA 11.8 + Pyannote + Whisper Large-v3 Setup\n", "# Optimized for T4 GPU and High RAM\n", "\n", "!pip install -q PyDrive2\n", "!pip install -q git+https://github.com/openai/whisper.git\n", "!pip install -q git+https://github.com/pyannote/pyannote-audio.git\n", "!pip install -q huggingface_hub\n", "!pip install -q openai==0.28.1  # Specific version for compatibility\n", "!pip install -q librosa\n", "!pip install -q torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118\n", "!pip install -q scikit-learn\n", "!pip install -q opencv-python\n", "!pip install -q Pillow\n", "!pip install -U transformers  # For BERT NER\n", "!pip install -q seqeval  # For NER evaluation\n", "\n", "print(\"✅ All dependencies installed successfully!\")"], "metadata": {"id": "alhO8kUgloS8", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "c04849c8-fbb9-428c-c006-f4157e38de38"}, "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["  Installing build dependencies ... \u001b[?25l\u001b[?25hdone\n", "  Getting requirements to build wheel ... \u001b[?25l\u001b[?25hdone\n", "  Preparing metadata (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m363.4/363.4 MB\u001b[0m \u001b[31m3.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.8/13.8 MB\u001b[0m \u001b[31m113.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m24.6/24.6 MB\u001b[0m \u001b[31m84.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m883.7/883.7 kB\u001b[0m \u001b[31m56.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m664.8/664.8 MB\u001b[0m \u001b[31m2.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m211.5/211.5 MB\u001b[0m \u001b[31m4.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m41.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.9/127.9 MB\u001b[0m \u001b[31m19.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m207.5/207.5 MB\u001b[0m \u001b[31m4.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.1/21.1 MB\u001b[0m \u001b[31m95.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Building wheel for openai-whisper (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m59.6/59.6 kB\u001b[0m \u001b[31m5.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m819.0/819.0 kB\u001b[0m \u001b[31m34.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.5/58.5 kB\u001b[0m \u001b[31m5.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m48.1/48.1 kB\u001b[0m \u001b[31m4.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m51.4/51.4 kB\u001b[0m \u001b[31m4.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m125.9/125.9 kB\u001b[0m \u001b[31m12.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m864.1/864.1 kB\u001b[0m \u001b[31m55.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m101.7/101.7 kB\u001b[0m \u001b[31m9.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m48.5/48.5 kB\u001b[0m \u001b[31m4.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m962.5/962.5 kB\u001b[0m \u001b[31m59.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m386.6/386.6 kB\u001b[0m \u001b[31m32.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m823.1/823.1 kB\u001b[0m \u001b[31m57.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m242.5/242.5 kB\u001b[0m \u001b[31m23.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m118.6/118.6 kB\u001b[0m \u001b[31m12.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m739.1/739.1 kB\u001b[0m \u001b[31m50.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Building wheel for pyannote.audio (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Building wheel for docopt (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Building wheel for julius (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.0/77.0 kB\u001b[0m \u001b[31m4.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: transformers in /usr/local/lib/python3.11/dist-packages (4.52.4)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from transformers) (3.18.0)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.30.0 in /usr/local/lib/python3.11/dist-packages (from transformers) (0.32.4)\n", "Requirement already satisfied: numpy>=1.17 in /usr/local/lib/python3.11/dist-packages (from transformers) (2.0.2)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.11/dist-packages (from transformers) (24.2)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.11/dist-packages (from transformers) (6.0.2)\n", "Requirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.11/dist-packages (from transformers) (2024.11.6)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from transformers) (2.32.3)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in /usr/local/lib/python3.11/dist-packages (from transformers) (0.21.1)\n", "Requirement already satisfied: safetensors>=0.4.3 in /usr/local/lib/python3.11/dist-packages (from transformers) (0.5.3)\n", "Requirement already satisfied: tqdm>=4.27 in /usr/local/lib/python3.11/dist-packages (from transformers) (4.67.1)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.30.0->transformers) (2025.3.2)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.30.0->transformers) (4.14.0)\n", "Requirement already satisfied: hf-xet<2.0.0,>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.30.0->transformers) (1.1.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (2025.4.26)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m43.6/43.6 kB\u001b[0m \u001b[31m3.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Building wheel for seqeval (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "✅ All dependencies installed successfully!\n"]}]}, {"cell_type": "code", "source": ["# =============================================================================\n", "# Cell 2: Download Video File from Google Drive (UPDATE FOR EACH NEW VIDEO)\n", "# =============================================================================\n", "from pydrive2.auth import GoogleAuth\n", "from pydrive2.drive import GoogleDrive\n", "from google.colab import auth\n", "from oauth2client.client import GoogleCredentials\n", "\n", "auth.authenticate_user()\n", "gauth = GoogleAuth()\n", "gauth.credentials = GoogleCredentials.get_application_default()\n", "drive = GoogleDrive(gauth)\n", "\n", "# 🔄 UPDATE THESE LINES FOR EACH NEW VIDEO:\n", "file_id = '1URiqv_ve0cSj0Uj6N2Qzoih8L4izqbMR'  # ← CHANGE THIS\n", "video_filename = 'Redacted_20__MENTALLY_DISTURBED_PERSON_3.mp4'  # ← CHANGE THIS\n", "\n", "downloaded = drive.CreateFile({'id': file_id})\n", "downloaded.GetContentFile(video_filename)\n", "print(f\"✅ Video file downloaded: {video_filename}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "HfLtIMdg3sSg", "outputId": "fb63279b-62f7-42a7-a6d6-8e2f7e00713b"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["✅ Video file downloaded: Redacted_20__MENTALLY_DISTURBED_PERSON_3.mp4\n"]}]}, {"cell_type": "code", "source": ["# =============================================================================\n", "# Cell 3: Authentication Setup\n", "# =============================================================================\n", "from huggingface_hub import login\n", "import openai\n", "\n", "# 🔑 UPDATE YOUR API KEYS HERE:\n", "HF_TOKEN = \"*************************************\"  # ← CHANGE THIS\n", "OPENAI_API_KEY = \"********************************************************************************************************************************************************************\"  # ← CHANGE THIS\n", "\n", "login(token=HF_TOKEN)\n", "openai.api_key = OPENAI_API_KEY\n", "\n", "print(\"✅ Authentication complete\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "nCVYkNZM3sVG", "outputId": "03a7dd9c-0d1d-4e92-efa2-d029e4a85692"}, "execution_count": 3, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["✅ Authentication complete\n"]}]}, {"cell_type": "code", "source": ["# =============================================================================\n", "# Cell 4: Enhanced Forensic Pipeline Setup WITH ALL IMPROVEMENTS\n", "# =============================================================================\n", "import os\n", "import torch\n", "import whisper\n", "import subprocess\n", "import librosa\n", "import numpy as np\n", "from datetime import datetime, timedelta\n", "from pyannote.audio import Pipeline\n", "from sklearn.cluster import KMeans\n", "import base64\n", "import cv2\n", "from PIL import Image\n", "from transformers import AutoTokenizer, AutoModelForTokenClassification, pipeline  # For BERT NER\n", "\n", "# Check GPU availability\n", "device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "print(f\"Using device: {device}\")\n", "\n", "# Enhanced legal trigger words for forensic analysis - INCLUDING NEW WORDS\n", "LEGAL_TRIGGER_WORDS = [\n", "    \"arrest\", \"detained\", \"miranda\", \"rights\", \"warrant\", \"search\", \"seizure\",\n", "    \"consent\", \"constitutional\", \"fourth amendment\", \"fifth amendment\",\n", "    \"baker act\", \"mental health\", \"crisis\", \"suicide\", \"self harm\",\n", "    \"force\", \"taser\", \"pepper spray\", \"baton\", \"firearm\", \"weapon\",\n", "    \"assault\", \"battery\", \"resistance\", \"compliance\", \"cooperation\",\n", "    \"medical\", \"injury\", \"pain\", \"breathing\", \"unconscious\", \"responsive\",\n", "    \"supervisor\", \"sergeant\", \"lieutenant\", \"backup\", \"ambulance\", \"ems\",\n", "    \"lawsuit\", \"carolina\", \"palm beach\", \"officer\", \"sheriff\", \"5150\",\n", "    \"order\", \"refusal\", \"psych\", \"RPO\", \"sane\", \"suicidal\", \"husband\",\n", "    \"combative\", \"harold\", \"hastings\", \"gun\", \"shotgun\", \"welfare\", \"lucid\",\n", "    \"hands up\", \"get down\", \"stop resisting\", \"calm down\", \"relax\",\n", "    \"towel\", \"naked\", \"undressed\", \"barefoot\", \"wet\", \"shower\", \"bathroom\",\n", "    \"cuff\", \"cuffs\", \"handcuff\", \"handcuffed\", \"restrained\", \"dignity\",\n", "    \"humiliate\", \"embarrass\", \"film\", \"recording\", \"camera\", \"mute\",\n", "    \"cover\", \"blanket\", \"sheet\", \"expose\", \"exposure\", \"neighbors\",\n", "    \"crowd\", \"public\", \"private\", \"home\", \"residence\", \"emergency\",\n", "    \"interrupted\", \"rushed\", \"swat\", \"tactical\", \"escalate\", \"de-escalate\"\n", "]\n", "\n", "# Legal case law references\n", "CASE_LAW_REFERENCES = {\n", "    \"<PERSON> v<PERSON>\": \"490 U.S. 386 (1989) - Use of force analysis\",\n", "    \"Tennessee v<PERSON> <PERSON>\": \"471 U.S. 1 (1985) - Deadly force standards\",\n", "    \"Payton v. New York\": \"445 U.S. 573 (1980) - Warrantless home entry\",\n", "    \"Kentucky v. King\": \"563 U.S. 452 (2011) - Exigent circumstances\",\n", "    \"York v. <PERSON>\": \"324 F.2d 450 (9th Cir. 1963) - Privacy dignity violations\",\n", "    \"Jordan v<PERSON>\": \"986 F.2d 1521 (9th Cir. 1993) - Cross-gender searches\",\n", "    \"Bell v<PERSON>\": \"441 U.S. 520 (1979) - Detention conditions\",\n", "    \"<PERSON><PERSON> v. <PERSON>\": \"457 U.S. 307 (1982) - Mental health detainees\"\n", "}\n", "\n", "def enhanced_audio_processing_for_difficult_sections(input_path, output_path):\n", "    \"\"\"Multi-pass audio enhancement for challenging sections\"\"\"\n", "    print(\"🔊 Enhanced audio processing for difficult sections...\")\n", "\n", "    # Pass 1: Normalize volume and compress dynamic range for distant speakers\n", "    pass1_path = \"/content/audio_pass1.wav\"\n", "    cmd1 = [\n", "        'ffmpeg', '-y', '-i', input_path,\n", "        '-af', 'dynaudnorm=p=0.9:s=5,compand=attacks=0.1:decays=0.5:points=-90/-90|-60/-40|-40/-25|-25/-15|-10/-10',\n", "        '-acodec', 'pcm_s16le', '-ar', '16000', '-ac', '1',\n", "        pass1_path\n", "    ]\n", "    subprocess.run(cmd1, capture_output=True)\n", "\n", "    # Pass 2: Enhance speech frequencies and reduce background noise\n", "    pass2_path = \"/content/audio_pass2.wav\"\n", "    cmd2 = [\n", "        'ffmpeg', '-y', '-i', pass1_path,\n", "        '-af', 'highpass=f=80,lowpass=f=8000,equalizer=f=2000:width_type=h:width=1000:g=3',\n", "        '-acodec', 'pcm_s16le',\n", "        pass2_path\n", "    ]\n", "    subprocess.run(cmd2, capture_output=True)\n", "\n", "    # Pass 3: Handle loud shouting and volume spikes\n", "    cmd3 = [\n", "        'ffmpeg', '-y', '-i', pass2_path,\n", "        '-af', 'alimiter=level_in=1:level_out=0.8:limit=0.9,volume=1.5',\n", "        '-acodec', 'pcm_s16le',\n", "        output_path\n", "    ]\n", "    subprocess.run(cmd3, capture_output=True)\n", "\n", "    print(f\"✅ Enhanced audio saved: {output_path}\")\n", "\n", "def transcribe_with_maximum_accuracy_enhanced(audio_path, language=\"en\"):\n", "    \"\"\"Enhanced Whisper transcription\"\"\"\n", "    print(\"🎙️ Loading Whisper Large-v3 for maximum accuracy...\")\n", "\n", "    import whisper\n", "    model = whisper.load_model(\"large-v3\", device=device)\n", "\n", "    print(\"🔄 Transcribing with enhanced settings...\")\n", "    result = model.transcribe(\n", "        audio_path,\n", "        language=\"en\",\n", "        word_timestamps=True,\n", "        verbose=False\n", "    )\n", "\n", "    print(f\"✅ Transcription complete: {len(result['text'])} characters\")\n", "    return result\n", "\n", "def analyze_video_frames_for_context(video_path, skip_seconds=30):\n", "    \"\"\"Extract and analyze video frames for visual context with GPT-4 Vision - WITH ENHANCED RESTRAINT ANALYSIS\"\"\"\n", "    print(\"📹 Analyzing video frames for visual context...\")\n", "\n", "    # Extract key frames every 30 seconds\n", "    frames_dir = \"/content/video_frames\"\n", "    os.makedirs(frames_dir, exist_ok=True)\n", "\n", "    extract_frames_cmd = [\n", "        'ffmpeg', '-y', '-ss', str(skip_seconds), '-i', video_path,\n", "        '-vf', 'fps=1/20',  # One frame every 20 seconds\n", "        '-q:v', '2',  # High quality\n", "        f'{frames_dir}/frame_%04d.jpg'\n", "    ]\n", "\n", "    subprocess.run(extract_frames_cmd, capture_output=True)\n", "\n", "    # Analyze frames with GPT-4 Vision\n", "    frame_files = sorted([f for f in os.listdir(frames_dir) if f.endswith('.jpg')])\n", "    visual_context = []\n", "    frame_cache = {}  # Stores frame_path → base64\n", "\n", "    print(f\"🔍 Analyzing {len(frame_files)} video frames...\")\n", "\n", "    for i, frame_file in enumerate(frame_files):\n", "        frame_path = os.path.join(frames_dir, frame_file)\n", "        timestamp = (i * 20) + skip_seconds  # Calculate actual timestamp (20 sec intervals)\n", "\n", "        # Encode frame for GPT-4 Vision\n", "        try:\n", "            if frame_path in frame_cache:\n", "                frame_data = frame_cache[frame_path]\n", "            else:\n", "                with open(frame_path, 'rb') as f:\n", "                   frame_data = base64.b64encode(f.read()).decode()\n", "                   frame_cache[frame_path] = frame_data\n", "\n", "            response = openai.ChatCompletion.create(\n", "                model=\"gpt-4o\",  # Using gpt-4o instead of deprecated gpt-4-vision-preview\n", "                messages=[\n", "                    {\n", "                        \"role\": \"user\",\n", "                        \"content\": [\n", "                            {\n", "                                \"type\": \"text\",\n", "                                \"text\": \"\"\"Analyze this police bodycam frame for forensic documentation. Provide detailed analysis of:\n", "\n", "1. SCENE SETTING: Location type, environment, lighting conditions, etc.\n", "2. PEOPLE VISIBLE: Number of individuals, their positions, actions, posture, clothing, etc.\n", "3. EQUIPMENT/EVIDENCE: Weapons, vehicles, medical equipment, evidence items, etc.\n", "4. TACTICAL POSITIONING: Officer formation, civilian positioning, spatial dynamics, threat levels, etc.\n", "5. EMOTIONAL INDICATORS: Body language, gestures, apparent stress levels, emotional reactions, etc.\n", "6. SAFETY CONCERNS: Potential hazards, weapons visible, environmental risks, threat levels, etc.\n", "7. LEGAL SIGNIFICANCE: Constitutional issues, use of force implications, breach of procedures, escalation, deescalation, evidence preservation, etc.\n", "8. ATTIRE ANALYSIS (CRITICAL FOR PRIVACY ASSESSMENT):\n", "   - Clothing status: Fully dressed, partially dressed, minimal clothing, towel only, etc.\n", "   - State of dress: Appropriate, inappropriate for public, emergency exit clothing\n", "   - Signs of rushed dressing: Incomplete clothing, towel wrapped, wet appearance\n", "   - Modesty concerns: Areas of body exposed, coverage inadequacy\n", "   - Footwear: Barefoot, shoes, slippers (indicates emergency exit)\n", "\n", "9. PRIVACY & DIGNITY INDICATORS:\n", "   - Public exposure level: Private home vs. public view\n", "   - Bystander presence: Neighbors, crowds, passersby witnessing exposure\n", "   - Recording implications: Subject aware of being filmed in state of undress\n", "   - Weather conditions affecting minimal clothing exposure\n", "\n", "10. EMERGENCY/CRISIS INDICATORS:\n", "   - Wet hair/body (shower interruption)\n", "   - Rushed appearance (hastily grabbed clothing/towel)\n", "   - Bathroom/shower context (wet floors, steam, towels visible)\n", "   - Time pressure indicators (incomplete dressing)\n", "\n", "11. RESTRAINT/HANDCUFFING ANALYSIS:\n", "   - Handcuff application on subject in minimal clothing\n", "   - Positioning: hands behind back while in towel/minimal clothing\n", "   - Dignity concerns during restraint application\n", "   - Cooperative behavior vs. restraint necessity\n", "\n", "12. STANDARD FORENSIC ELEMENTS:\n", "   - Scene setting and location context\n", "   - People positions and actions\n", "   - Equipment and evidence visible\n", "   - Officer positioning relative to undressed subject\n", "   - Safety and tactical considerations\n", "\n", "13. CONSTITUTIONAL CONCERNS:\n", "   - 4th Amendment: Privacy expectations in home\n", "   - 8th Amendment: Dignity during detention\n", "   - Public exposure creating humiliation\n", "   - Reasonable accommodation for clothing needs\n", "\n", "Be specific, objective, and forensically precise. Use timestamps and positional references. Be extremely specific about clothing status and privacy implications. Flag any dignitary concerns or inappropriate exposure situations. Document if subject appears to have been interrupted during private activities (bathing, dressing, etc.).\"\"\"\n", "                            },\n", "                            {\n", "                                \"type\": \"image_url\",\n", "                                \"image_url\": {\n", "                                    \"url\": f\"data:image/jpeg;base64,{frame_data}\"\n", "                                }\n", "                            }\n", "                        ]\n", "                    }\n", "                ],\n", "                max_tokens=1000,\n", "                temperature=0.1\n", "            )\n", "\n", "            visual_analysis = response.choices[0].message.content\n", "            visual_context.append({\n", "                'timestamp': timestamp,\n", "                'frame': frame_file,\n", "                'analysis': {\n", "                    'raw_text': visual_analysis,\n", "                    'scene_setting': None,\n", "                    'privacy': None,\n", "                    'emergency_flags': [],\n", "                }\n", "            })\n", "\n", "            print(f\"✅ Enhanced frame analysis - Frame analyzed: {timestamp//60:02d}:{timestamp%60:02d}\")\n", "\n", "        except Exception as e:\n", "            print(f\"⚠️ Frame analysis failed for {frame_file}: {e}\")\n", "            visual_context.append({\n", "                'timestamp': timestamp,\n", "                'frame': frame_file,\n", "                'analysis': f\"Visual analysis unavailable: {e}\"\n", "            })\n", "\n", "    print(f\"✅ Enhanced visual context analysis complete: {len(visual_context)} frames\")\n", "    return visual_context\n", "\n", "def detect_speaker_overlaps_and_separate_enhanced(audio_path, diarization_result, whisper_result):\n", "    \"\"\"Enhanced speaker overlap detection with better sensitivity\"\"\"\n", "    print(\"👥 Enhanced speaker overlap detection...\")\n", "\n", "    overlaps = []\n", "\n", "    # Convert diarization to list of segments\n", "    diar_segments = []\n", "    for turn, _, speaker in diarization_result.itertracks(yield_label=True):\n", "        diar_segments.append({\n", "            'start': turn.start,\n", "            'end': turn.end,\n", "            'speaker': speaker\n", "        })\n", "\n", "    # Find overlapping segments with enhanced sensitivity\n", "    for i, seg1 in enumerate(diar_segments):\n", "        for seg2 in diar_segments[i+1:]:\n", "            # Check for overlap\n", "            overlap_start = max(seg1['start'], seg2['start'])\n", "            overlap_end = min(seg1['end'], seg2['end'])\n", "\n", "            if overlap_start < overlap_end:\n", "                duration = overlap_end - overlap_start\n", "                if duration > 0.4:  # Lowered threshold from 0.5 to catch more overlaps\n", "                    overlaps.append({\n", "                        'start': overlap_start,\n", "                        'end': overlap_end,\n", "                        'duration': duration,\n", "                        'speakers': [seg1['speaker'], seg2['speaker']]\n", "                    })\n", "\n", "    print(f\"✅ Enhanced overlap detection complete: {len(overlaps)} overlaps found\")\n", "    return overlaps\n", "\n", "def format_overlap_readable(overlap, whisper_result):\n", "    start = overlap['start']\n", "    end = overlap['end']\n", "    speakers = overlap['speakers']\n", "    timestamp = f\"[{int(start//60):02}:{int(start%60):02}–{int(end//60):02}:{int(end%60):02}]\"\n", "\n", "    lines_by_speaker = {s: [] for s in speakers}\n", "\n", "    for seg in whisper_result['segments']:\n", "        if seg['start'] >= start and seg['end'] <= end:\n", "            speaker = seg.get('speaker', 'UNKNOWN')\n", "            if speaker in lines_by_speaker:\n", "                lines_by_speaker[speaker].append(seg['text'].strip())\n", "\n", "    output = f\"{timestamp} **OVERLAP** {tuple(speakers)}:\\n\"\n", "    for speaker, lines in lines_by_speaker.items():\n", "        if lines:\n", "            joined = ' '.join(lines)\n", "            output += f\"{speaker}: {joined}\\n\"\n", "\n", "    return output.strip()\n", "\n", "def combine_transcription_and_speakers_enhanced(whisper_result, diarization_result, overlaps):\n", "    \"\"\"Enhanced combination with better word-level speaker assignment\"\"\"\n", "    print(\"🔗 Enhanced transcription and speaker combination...\")\n", "\n", "    enhanced_transcript = []\n", "\n", "    # Process each word from <PERSON><PERSON><PERSON> with enhanced speaker matching\n", "    for segment in whisper_result['segments']:\n", "        for word_info in segment.get('words', []):\n", "            word_start = word_info['start']\n", "            word_end = word_info['end']\n", "            word_text = word_info['word']\n", "            word_confidence = word_info.get('probability', 0.0)\n", "\n", "            # Find speaker(s) for this word with tolerance\n", "            speakers = []\n", "            tolerance = 0.1  # 100ms tolerance for better matching\n", "\n", "            for turn, _, speaker in diarization_result.itertracks(yield_label=True):\n", "                if (turn.start - tolerance) <= word_start <= (turn.end + tolerance):\n", "                    speakers.append(speaker)\n", "\n", "            # Check for overlaps\n", "            is_overlap = False\n", "            overlap_speakers = []\n", "            for overlap in overlaps:\n", "                if overlap['start'] <= word_start <= overlap['end']:\n", "                    is_overlap = True\n", "                    overlap_speakers = overlap['speakers']\n", "                    break\n", "\n", "            enhanced_transcript.append({\n", "                'word': word_text,\n", "                'start': word_start,\n", "                'end': word_end,\n", "                'confidence': word_confidence,\n", "                'speakers': speakers,\n", "                'overlap': is_overlap,\n", "                'overlap_speakers': overlap_speakers\n", "            })\n", "\n", "    print(f\"✅ Enhanced transcript created: {len(enhanced_transcript)} words\")\n", "    enhanced_transcript.sort(key=lambda x: x['start'])\n", "    return enhanced_transcript\n", "\n", "def analyze_with_gpt4_forensic_enhanced(transcript_text, speaker_segments, trigger_words, visual_context):\n", "    \"\"\"Enhanced GPT-4 forensic analysis incorporating both audio and visual data\"\"\"\n", "    print(\"🧠 Running enhanced GPT-4 forensic analysis...\")\n", "\n", "    # Combine visual context for analysis\n", "    visual_summary = \"\\n\".join([\n", "        f\"[{ctx['timestamp']//60:02d}:{ctx['timestamp']%60:02d}] VISUAL: {ctx['analysis']}\"\n", "        for ctx in visual_context[:10]  # Include first 10 visual analyses\n", "    ])\n", "\n", "    system_prompt = \"\"\"You are a certified forensic audiovisual analyst with 25+ years experience in criminal procedure, constitutional law (42 U.S.C. § 1983), and police misconduct analysis. You have served as a court-appointed expert witness and specialize in integrated audio-visual evidence analysis.\n", "\n", "Conduct comprehensive forensic analysis incorporating both audio transcript and visual frame analysis for:\n", "\n", "1. CONSTITUTIONAL VIOLATIONS:\n", "   - 4th Amendment (search/seizure without warrant)\n", "   - 5th Amendment (Miranda rights, self-incrimination)\n", "   - 8th Amendment (excessive force, cruel treatment)\n", "   - 14th Amendment (due process, equal protection)\n", "\n", "2. STATUTORY VIOLATIONS:\n", "   - Florida Statutes (Baker Act § 394.463)\n", "   - Arrest authority compliance (Ch. 901)\n", "   - Mental health detention protocols\n", "   - Transport and medical clearance requirements\n", "\n", "3. PROCEDURAL BREACHES:\n", "   - Required warnings not given\n", "   - Supervisor notification failures\n", "   - Medical clearance timing violations\n", "   - Evidence preservation protocols\n", "\n", "4. USE OF FORCE ASSESSMENT:\n", "   - <PERSON> v. Connor standards compliance\n", "   - Proportionality analysis (visual evidence critical)\n", "   - De-escalation attempts/failures\n", "   - Weapon deployment justification\n", "\n", "5. AUDIO-VISUAL CORRELATION:\n", "   - Consistency between spoken actions and visual evidence\n", "   - Body language vs verbal compliance\n", "   - Environmental factors affecting behavior\n", "   - Officer positioning and tactical decisions\n", "\n", "6. PSYCHOLOGICAL MARKERS:\n", "   - Mental health crisis indicators (audio + visual)\n", "   - Stress escalation patterns\n", "   - Compliance vs resistance behaviors\n", "   - Environmental stressors\n", "   - Mental health interventions\n", "\n", "7. ATTIRE ANALYSIS (CRITICAL FOR PRIVACY ASSESSMENT):\n", "   - Clothing status: Fully dressed, partially dressed, minimal clothing, towel only, etc.\n", "   - State of dress: Appropriate, inappropriate for public, emergency exit clothing\n", "   - Signs of rushed dressing: Incomplete clothing, towel wrapped, wet appearance\n", "   - Modesty concerns: Areas of body exposed, coverage inadequacy\n", "   - Footwear: Barefoot, shoes, slippers (indicates emergency exit)\n", "\n", "8. PRIVACY & DIGNITY INDICATORS:\n", "   - Public exposure level: Private home vs. public view\n", "   - Bystander presence: Neighbors, crowds, passersby witnessing exposure\n", "   - Recording implications: Subject aware of being filmed in state of undress\n", "   - Weather conditions affecting minimal clothing exposure\n", "\n", "9. EMERGENCY/CRISIS INDICATORS:\n", "   - Wet hair/body (shower interruption)\n", "   - Rushed appearance (hastily grabbed clothing/towel)\n", "   - Bathroom/shower context (wet floors, steam, towels visible)\n", "   - Time pressure indicators (incomplete dressing)\n", "\n", "10. RESTRAINT/HANDCUFFING ANALYSIS:\n", "   - Handcuff application on subject in minimal clothing\n", "   - Positioning: hands behind back while in towel/minimal clothing\n", "   - Dignity concerns during restraint application\n", "   - Cooperative behavior vs. restraint necessity\n", "\n", "11. STANDARD FORENSIC ELEMENTS:\n", "   - Scene setting and location context\n", "   - People positions and actions\n", "   - Equipment and evidence visible\n", "   - Officer positioning relative to undressed subject\n", "   - Safety and tactical considerations\n", "\n", "12. CONSTITUTIONAL CONCERNS:\n", "   - 4th Amendment: Privacy expectations in home\n", "   - 8th Amendment: Dignity during detention\n", "   - Public exposure creating humiliation\n", "   - Reasonable accommodation for clothing needs\n", "\n", "Provide specific timestamps, direct quotes, visual observations, legal significance, and court-admissible analysis with integrated audio-visual evidence correlation. Be extremely specific about clothing status and privacy implications. Flag any dignitary concerns or inappropriate exposure situations. Document if subject appears to have been interrupted during private activities (showering, bathing, dressing, etc.).\"\"\"\n", "\n", "    user_prompt = f\"\"\"\n", "POLICE BODYCAM INTEGRATED AUDIO-VISUAL ANALYSIS:\n", "\n", "AUDIO TRANSCRIPT (First 8000 characters):\n", "{transcript_text[:8000]}\n", "\n", "VISUAL FRAME ANALYSIS:\n", "{visual_summary}\n", "\n", "LEGAL TRIGGER WORDS DETECTED:\n", "{', '.join(trigger_words)}\n", "\n", "SPEAKER COUNT: {len(set(seg.get('speaker', 'Unknown') for seg in speaker_segments))}\n", "\n", "Provide comprehensive integrated forensic analysis with:\n", "- Constitutional and statutory violations (cite specific evidence)\n", "- Critical timeline events with both audio and visual timestamps\n", "- Use of force analysis with visual evidence correlation\n", "- Risk assessment for legal proceedings\n", "- Evidence preservation recommendations\n", "- Audio-visual consistency analysis\n", "\"\"\"\n", "\n", "    try:\n", "        response = openai.ChatCompletion.create(\n", "            model=\"gpt-4o\",\n", "            messages=[\n", "                {\"role\": \"system\", \"content\": system_prompt},\n", "                {\"role\": \"user\", \"content\": user_prompt}\n", "            ],\n", "            max_tokens=4000,\n", "            temperature=0.05\n", "        )\n", "\n", "        return response.choices[0].message.content\n", "\n", "    except Exception as e:\n", "        print(f\"❌ GPT-4 analysis failed: {e}\")\n", "        return f\"GPT-4 analysis unavailable: {e}\"\n", "\n", "def inject_visual_context_into_transcript(enhanced_transcript, visual_context, skip_seconds=30):\n", "    \"\"\"Inject visual context annotations into transcript at appropriate timestamps\"\"\"\n", "    print(\"💉 Injecting visual context into transcript...\")\n", "\n", "    visual_injections = {}\n", "\n", "    # Map visual context to transcript timestamps\n", "    for ctx in visual_context:\n", "        visual_timestamp = ctx['timestamp']\n", "\n", "        # Find the closest word in the transcript to inject visual context\n", "        closest_word_index = None\n", "        min_time_diff = float('inf')\n", "\n", "        for i, word_data in enumerate(enhanced_transcript):\n", "            word_timestamp = word_data['start'] + skip_seconds\n", "            time_diff = abs(word_timestamp - visual_timestamp)\n", "\n", "            if time_diff < min_time_diff and time_diff < 15:  # Within 15 seconds\n", "                min_time_diff = time_diff\n", "                closest_word_index = i\n", "\n", "        if closest_word_index is not None:\n", "            visual_injections[closest_word_index] = f\"*{{VISUAL CONTEXT: {ctx['analysis'][:200]}...}}*\"\n", "\n", "    print(f\"✅ Visual context injections prepared: {len(visual_injections)} injections\")\n", "    return visual_injections\n", "\n", "def inject_contextual_annotations_enhanced(enhanced_transcript):\n", "    \"\"\"Enhanced contextual legal/psychological annotations - WITH LIST SUPPORT\"\"\"\n", "    print(\"💉 Injecting enhanced contextual annotations...\")\n", "\n", "    annotations = {}\n", "\n", "    for i, word_data in enumerate(enhanced_transcript):\n", "        text = word_data.get('word', '').lower()\n", "\n", "        # Enhanced legal trigger detection\n", "        if any(word in text for word in ['miranda', 'rights', 'remain silent']):\n", "            annotations.setdefault(i, []).append(\"*{Miranda rights advisement - 5th Amendment constitutional requirement}*\")\n", "        elif any(word in text for word in ['force', 'taser', 'weapon', 'gun', 'swat']):\n", "            annotations.setdefault(i, []).append(\"*{Use of force deployment - <PERSON> v<PERSON> analysis required}*\")\n", "        elif any(word in text for word in ['baker act', 'mental health', 'crisis', '5150', 'behavioral health', 'rpo', 'risk protection', 'no blood', 'suicidal']):\n", "            annotations.setdefault(i, []).append(\"*{Mental health detention protocol - Fla. Stat. § 394.463}*\")\n", "        elif any(word in text for word in ['search', 'seizure', 'house', 'rpo', 'risk protection']):\n", "            annotations.setdefault(i, []).append(\"*{4th Amendment search/seizure activity - warrant requirement analysis}*\")\n", "        elif any(word in text for word in ['consent', 'permission', 'fine', 'baker act', 'take me anywhere', 'suicidal', 'detained', 'restrained', 'cuff', 'secure', 'clear', 'house', 'ask her']):\n", "            annotations.setdefault(i, []).append(\"*{Consent documentation - voluntariness analysis required}*\")\n", "        elif any(word in text for word in ['supervisor', 'sergeant', 'lieutenant', 'williams']):\n", "            annotations.setdefault(i, []).append(\"*{Supervisory involvement - chain of command protocol}*\")\n", "        elif any(word in text for word in ['ambulance', 'ems', 'medical', 'injury', 'rescue', 'no blood']):\n", "            annotations.setdefault(i, []).append(\"*{Medical intervention - duty of care assessment}*\")\n", "        elif any(word in text for word in ['hands up', 'get down', 'stop', 'walk backwards', 'face away', 'turn around']):\n", "            annotations.setdefault(i, []).append(\"*{Compliance directive - officer command analysis}*\")\n", "        elif any(word in text for word in ['calm down', 'relax', 'breathe', 'escalation,' 'embarrass', 'humiliate', 'neighbors']):\n", "            annotations.setdefault(i, []).append(\"*{De-escalation attempt - crisis intervention technique}*\")\n", "        elif any(word in text for word in ['escalation,' 'embarrass', 'humiliate', 'neighbors', 'swat', 'shotgun', 'cock', 'lethal', 'lethaly', 'go in', 'not leaving', 'assess the house']):\n", "            annotations.setdefault(i, []).append(\"*{Escalation attempts, behaviors, meneuvers, tactics - unwarranted and/or improper escalation analysis}*\")\n", "        elif any(word in text for word in ['escalate', 'embarrass', 'humiliate', 'neighbors', 'swat', 'towel', 'wet', 'cuff', 'cover her', 'sheet', 'game', 'games', 'song and dance', 'regardless', 'play']):\n", "            annotations.setdefault(i, []).append(\"*{Retaliatory and/or punitive  tactics - unwarranted and/or improper escalation analysis}*\")\n", "        elif any(word in text for word in ['escalate', 'embarrass', 'humiliate', 'neighbors', 'swat', 'towel', 'wet', 'cuff', 'cover her', 'sheet', 'dress', 'naked', 'cover me', 'filming', 'videoing', 'watching']):\n", "            annotations.setdefault(i, []).append(\"*{Humiliation/Dignity/Public shame activities, tactics, behaviors - analysis of intentional and/or unintentional plublic shame, humiliation, embarrassment, preservation of dignity activities}*\")\n", "        elif any(word in text for word in ['heads up', 'shotgun', 'baker act', 'heard you', \"don't tell\", \"don't say\", 'no blood', 'in front of', 'mute', 'blue', 'camera', 'teach', 'complaint', \"don't teach\", 'statement', 'report', 'concern']):\n", "            annotations.setdefault(i, []).append(\"*{Transparency cocerns, cover-up, coordination concerns - transparency and proper/improper disclosure assessment, narrative coordination/alignments discussions and/or behaviors, body-worn-camera muting and/or deactivation assesment, suspicious redaction assessment}*\")\n", "        elif any(word in text for word in ['cuff', 'cuffs', 'handcuff', 'handcuffed', 'restrained']):\n", "            annotations.setdefault(i, []).append(\"*{RESTRAINT APPLICATION: Handcuffing procedure - dignity and necessity analysis required}*\")\n", "        elif any(word in text for word in ['towel', 'naked', 'undressed', 'barefoot', 'wet', 'shower', 'bathroom']):\n", "            annotations.setdefault(i, []).append(\"*{ATTIRE CONCERN: Minimal clothing status - privacy and dignity implications}*\")\n", "\n", "    return annotations\n", "\n", "def analyze_transcript_confidence_metrics(enhanced_transcript):\n", "    \"\"\"Analyze confidence metrics for transcript accuracy\"\"\"\n", "    print(\"📊 Analyzing transcript confidence metrics...\")\n", "\n", "    confidence_stats = {\n", "        'high_confidence': 0,  # > 0.9\n", "        'medium_confidence': 0,  # 0.7 - 0.9\n", "        'low_confidence': 0,  # < 0.7\n", "        'total_words': len(enhanced_transcript),\n", "        'problematic_sections': []\n", "    }\n", "\n", "    current_low_conf_start = None\n", "    low_conf_count = 0\n", "\n", "    for i, word_data in enumerate(enhanced_transcript):\n", "        confidence = word_data.get('confidence', 0.0)\n", "\n", "        if confidence > 0.9:\n", "            confidence_stats['high_confidence'] += 1\n", "            # End low confidence section if we were tracking one\n", "            if current_low_conf_start and low_conf_count >= 5:\n", "                confidence_stats['problematic_sections'].append({\n", "                    'start_index': current_low_conf_start,\n", "                    'end_index': i-1,\n", "                    'word_count': low_conf_count,\n", "                    'timestamp': enhanced_transcript[current_low_conf_start]['start']\n", "                })\n", "            current_low_conf_start = None\n", "            low_conf_count = 0\n", "        elif confidence >= 0.7:\n", "            confidence_stats['medium_confidence'] += 1\n", "        else:\n", "            confidence_stats['low_confidence'] += 1\n", "            if current_low_conf_start is None:\n", "                current_low_conf_start = i\n", "            low_conf_count += 1\n", "\n", "    # Calculate percentages\n", "    total = confidence_stats['total_words']\n", "    confidence_stats['high_confidence_pct'] = (confidence_stats['high_confidence'] / total) * 100\n", "    confidence_stats['medium_confidence_pct'] = (confidence_stats['medium_confidence'] / total) * 100\n", "    confidence_stats['low_confidence_pct'] = (confidence_stats['low_confidence'] / total) * 100\n", "\n", "    print(f\"✅ Confidence analysis complete: {confidence_stats['high_confidence_pct']:.1f}% high confidence\")\n", "\n", "    return confidence_stats\n", "\n", "def generate_audio_quality_report(enhanced_transcript, overlaps, confidence_stats):\n", "    \"\"\"Generate detailed audio quality and transcription accuracy report\"\"\"\n", "\n", "    report = \"AUDIO QUALITY AND TRANSCRIPTION ACCURACY REPORT:\\n\"\n", "    report += \"=\"*50 + \"\\n\\n\"\n", "\n", "    report += \"OVERALL CONFIDENCE METRICS:\\n\"\n", "    report += f\"- High Confidence Words: {confidence_stats['high_confidence']} ({confidence_stats['high_confidence_pct']:.1f}%)\\n\"\n", "    report += f\"- Medium Confidence Words: {confidence_stats['medium_confidence']} ({confidence_stats['medium_confidence_pct']:.1f}%)\\n\"\n", "    report += f\"- Low Confidence Words: {confidence_stats['low_confidence']} ({confidence_stats['low_confidence_pct']:.1f}%)\\n\"\n", "    report += f\"- Total Words Analyzed: {confidence_stats['total_words']}\\n\\n\"\n", "\n", "    report += f\"SPEAKER OVERLAP INCIDENTS: {len(overlaps)}\\n\"\n", "    report += f\"PROBLEMATIC SECTIONS: {len(confidence_stats['problematic_sections'])}\\n\\n\"\n", "\n", "    if confidence_stats['problematic_sections']:\n", "        report += \"LOW CONFIDENCE SECTIONS REQUIRING REVIEW:\\n\"\n", "        report += \"-\"*40 + \"\\n\"\n", "        for section in confidence_stats['problematic_sections'][:10]:  # Top 10\n", "            timestamp = str(timedelta(seconds=int(section['timestamp'])))\n", "            report += f\"- [{timestamp}] {section['word_count']} consecutive low-confidence words\\n\"\n", "\n", "    report += \"\\nRECOMMENDATIONS:\\n\"\n", "    if confidence_stats['low_confidence_pct'] > 10:\n", "        report += \"⚠️ High percentage of low-confidence transcription\\n\"\n", "        report += \"   - Manual review strongly recommended\\n\"\n", "        report += \"   - Consider audio enhancement for re-transcription\\n\"\n", "\n", "    if len(overlaps) > 20:\n", "        report += \"⚠️ Significant speaker overlap detected\\n\"\n", "        report += \"   - May impact accuracy of speaker attribution\\n\"\n", "        report += \"   - Critical sections should be manually verified\\n\"\n", "\n", "    return report\n", "\n", "# ENHANCED LEGAL ANALYSIS FUNCTIONS\n", "# Add these functions to Cell 4 (insert after the existing functions)\n", "\n", "def cross_reference_utterances_with_behavior(enhanced_transcript, visual_context, skip_seconds=30):\n", "    \"\"\"Cross-reference speaker utterances with observable behavior for contradictions\"\"\"\n", "    print(\"🔍 Cross-referencing utterances with visual behavior...\")\n", "\n", "    behavioral_contradictions = []\n", "    compliance_violations = []\n", "\n", "    # Map commands to expected visual responses\n", "    command_keywords = {\n", "        'hands up': 'raised hands visible',\n", "        'get down': 'subject lowering to ground',\n", "        'turn around': 'subject rotating position',\n", "        'step back': 'backward movement',\n", "        'calm down': 'reduced agitation indicators',\n", "        'stop resisting': 'cessation of physical resistance',\n", "        'dont move': 'static positioning'\n", "    }\n", "\n", "    for i, word_data in enumerate(enhanced_transcript):\n", "        word_timestamp = word_data['start'] + skip_seconds\n", "        word_text = word_data['word'].lower()\n", "        speakers = word_data['speakers']\n", "\n", "        # Check if this is an officer command\n", "        is_officer_command = any('officer' in str(speaker).lower() or\n", "                               speaker in ['SPEAKER_A', 'SPEAKER_B', 'SPEAKER_C']\n", "                               for speaker in speakers)\n", "\n", "        if is_officer_command:\n", "            for command, expected_behavior in command_keywords.items():\n", "                if command in word_text:\n", "                    # Find corresponding visual context (within 30 seconds)\n", "                    corresponding_visual = None\n", "                    for ctx in visual_context:\n", "                        if abs(ctx['timestamp'] - word_timestamp) <= 30:\n", "                            corresponding_visual = ctx\n", "                            break\n", "\n", "                    if corresponding_visual:\n", "                        visual_analysis = corresponding_visual['analysis'].lower()\n", "\n", "                        # Check for compliance/non-compliance indicators\n", "                        compliance_indicators = ['complying', 'following', 'obeying', 'hands raised', 'cooperation']\n", "                        resistance_indicators = ['resisting', 'non-compliant', 'refusing', 'aggressive', 'fighting', 'obstructing']\n", "\n", "                        has_compliance = any(indicator in visual_analysis for indicator in compliance_indicators)\n", "                        has_resistance = any(indicator in visual_analysis for indicator in resistance_indicators)\n", "\n", "                        if command in ['hands up', 'get down', 'stop resisting'] and has_resistance:\n", "                            compliance_violations.append({\n", "                                'timestamp': word_timestamp,\n", "                                'command': command,\n", "                                'visual_evidence': visual_analysis[:200],\n", "                                'contradiction_type': 'Command not followed',\n", "                                'speakers': speakers\n", "                            })\n", "\n", "                        # Flag potential contradictions\n", "                        if 'calm down' in command and 'agitated' in visual_analysis:\n", "                            behavioral_contradictions.append({\n", "                                'timestamp': word_timestamp,\n", "                                'audio_content': word_text,\n", "                                'visual_content': visual_analysis[:200],\n", "                                'contradiction': 'De-escalation command during continued agitation'\n", "                            })\n", "\n", "    print(f\"✅ Found {len(compliance_violations)} compliance violations\")\n", "    print(f\"✅ Found {len(behavioral_contradictions)} behavioral contradictions\")\n", "\n", "    return compliance_violations, behavioral_contradictions\n", "\n", "def analyze_privacy_dignity_violations(enhanced_transcript, visual_context, skip_seconds=30):\n", "    \"\"\"Analyze privacy and dignity violations - WITH ENHANCED HANDCUFFING DETECTION\"\"\"\n", "    print(\"🔒 Analyzing privacy and dignity violations...\")\n", "\n", "    privacy_violations = []\n", "    dignity_violations = []\n", "    attire_violations = []\n", "    public_exposure_incidents = []\n", "\n", "    # Privacy violation keywords\n", "    privacy_keywords = ['strip', 'naked', 'undress', 'expose', 'body search', 'intimate', 'private parts']\n", "\n", "    # Dignity violation keywords\n", "    dignity_keywords = ['humiliate', 'embarrass', 'degrade', 'mock', 'ridicule', 'shame']\n", "\n", "    # Public exposure keywords # Enhanced keywords for clothing/attire situations\n", "    exposure_keywords = ['public', 'crowd', 'spectators', 'bystanders', 'recording',\n", "                        'exposed', 'visible', 'uncovered', 'inappropriate', 'public view',\n", "                        'neighbors seeing', 'crowd watching', 'filming']\n", "\n", "    emergency_exit_keywords = ['rushed out', 'hurried', 'interrupted', 'grabbed towel',\n", "                              'wet hair', 'steam', 'bathroom door', 'shower interrupted']\n", "\n", "    attire_keywords = ['towel', 'naked', 'undressed', 'partially clothed', 'minimal clothing' 'cover',\n", "                      'barefoot', 'wet', 'shower', 'bathing', 'bathroom', 'rushed', 'incomplete dress']\n", "\n", "    handcuff_dignity_keywords = ['handcuffed', 'cuffed', 'restrained', 'hands behind back',\n", "                                'restraints applied', 'detained']\n", "\n", "    # Analyze audio for clothing/exposure references\n", "    for i, word_data in enumerate(enhanced_transcript):\n", "        word_timestamp = word_data['start'] + skip_seconds\n", "        word_text = word_data['word'].lower()\n", "        speakers = word_data['speakers']\n", "\n", "        # Check for privacy violations\n", "        if any(keyword in word_text for keyword in privacy_keywords):\n", "            # Find corresponding visual context\n", "            visual_evidence = None\n", "            for ctx in visual_context:\n", "                if abs(ctx['timestamp'] - word_timestamp) <= 60:\n", "                    visual_evidence = ctx['analysis']\n", "                    break\n", "\n", "            privacy_violations.append({\n", "                'timestamp': word_timestamp,\n", "                'audio_evidence': word_text,\n", "                'visual_evidence': visual_evidence[:200] if visual_evidence else 'No visual context',\n", "                'violation_type': 'Privacy violation',\n", "                'speakers': word_data['speakers']\n", "            })\n", "\n", "        # Check for attire-related violations\n", "        if any(keyword in word_text for keyword in attire_keywords):\n", "            # Find corresponding visual context\n", "            visual_evidence = None\n", "            for ctx in visual_context:\n", "                if abs(ctx['timestamp'] - word_timestamp) <= 60:\n", "                    visual_evidence = ctx['analysis']\n", "                    break\n", "\n", "            attire_violations.append({\n", "                'timestamp': word_timestamp,\n", "                'audio_evidence': word_text,\n", "                'visual_evidence': visual_evidence[:300] if visual_evidence else 'No visual context',\n", "                'violation_type': 'Attire/Clothing Privacy Concern',\n", "                'speakers': speakers\n", "            })\n", "\n", "        # Check for handcuffing dignity concerns with attire context\n", "        if any(keyword in word_text for keyword in handcuff_dignity_keywords):\n", "            # Check if this occurs near attire violations\n", "            attire_context = any(attire_word in enhanced_transcript[max(0, i-10):i+10][j]['word'].lower()\n", "                               for j in range(len(enhanced_transcript[max(0, i-10):i+10]))\n", "                               for attire_word in ['towel', 'naked', 'undressed', 'wet'])\n", "\n", "            if attire_context:\n", "                dignity_violations.append({\n", "                    'timestamp': word_timestamp,\n", "                    'audio_evidence': word_text,\n", "                    'violation_type': 'Handcuffing Individual in Minimal Clothing - Dignity Violation',\n", "                    'speakers': speakers,\n", "                    'severity': 'HIGH',\n", "                    'constitutional_concern': '8th Amendment - Cruel and unusual punishment'\n", "                })\n", "\n", "        # Check for emergency exit situations\n", "        if any(keyword in word_text for keyword in emergency_exit_keywords):\n", "            privacy_violations.append({\n", "                'timestamp': word_timestamp,\n", "                'audio_evidence': word_text,\n", "                'violation_type': 'Emergency Exit Privacy Violation',\n", "                'speakers': speakers\n", "            })\n", "\n", "        # Check for dignity violations\n", "        if any(keyword in word_text for keyword in dignity_keywords):\n", "            dignity_violations.append({\n", "                'timestamp': word_timestamp,\n", "                'audio_evidence': word_text,\n", "                'violation_type': 'Dignity violation',\n", "                'speakers': word_data['speakers']\n", "            })\n", "\n", "    # Enhanced visual analysis for clothing/exposure\n", "    for ctx in visual_context:\n", "        visual_analysis = ctx['analysis'].lower()\n", "\n", "        # Check for clothing-related exposure\n", "        clothing_indicators = ['towel only', 'minimal clothing', 'partially dressed', 'undressed',\n", "                              'wet from shower', 'barefoot', 'rushed dressing', 'incomplete clothing']\n", "\n", "        if any(indicator in visual_analysis for indicator in clothing_indicators):\n", "            # Check if handcuffing is involved\n", "            handcuff_indicators = ['handcuff', 'cuff', 'restrain', 'hands behind back']\n", "            is_handcuffed = any(hc_word in visual_analysis for hc_word in handcuff_indicators)\n", "\n", "            # Check if in public view\n", "            public_indicators = ['public', 'neighbors', 'crowd', 'street', 'outside', 'porch', 'yard']\n", "            is_public = any(pub_word in visual_analysis for pub_word in public_indicators)\n", "\n", "            violation_type = 'Public Exposure - Minimal Clothing' if is_public else 'Private Exposure Documentation'\n", "\n", "            if is_handcuffed:\n", "                violation_type += ' + Restraint Applied'\n", "\n", "            public_exposure_incidents.append({\n", "                'timestamp': ctx['timestamp'],\n", "                'visual_evidence': ctx['analysis'],\n", "                'violation_type': violation_type,\n", "                'severity': 'CRITICAL' if (is_public and is_handcuffed) else 'HIGH' if is_public else 'MODERATE',\n", "                'clothing_status': 'MINIMAL/INADEQUATE',\n", "                'restraint_status': 'RESTRAINED' if is_handcuffed else 'UNRESTRAINED'\n", "            })\n", "\n", "        # Check for dignity violations\n", "        dignity_indicators = ['humiliating', 'embarrassing', 'inappropriate exposure',\n", "                             'forced to remain undressed', 'denied clothing']\n", "\n", "        if any(indicator in visual_analysis for indicator in dignity_indicators):\n", "            dignity_violations.append({\n", "                'timestamp': ctx['timestamp'],\n", "                'visual_evidence': ctx['analysis'],\n", "                'violation_type': 'Dignity Violation - Inappropriate Exposure',\n", "                'severity': 'HIGH'\n", "            })\n", "\n", "        # Check for emergency/crisis interruption\n", "        emergency_indicators = ['shower interrupted', 'rushed from bathroom', 'wet appearance',\n", "                               'emergency exit', 'hastily dressed', 'grabbed towel']\n", "\n", "        if any(indicator in visual_analysis for indicator in emergency_indicators):\n", "            privacy_violations.append({\n", "                'timestamp': ctx['timestamp'],\n", "                'visual_evidence': ctx['analysis'],\n", "                'violation_type': 'Emergency Privacy Interruption',\n", "                'context': 'Interrupted Private Activity'\n", "            })\n", "\n", "    # Analyze visual context for public exposure\n", "    public_exposure_incidents = []\n", "    for ctx in visual_context:\n", "        visual_analysis = ctx['analysis'].lower()\n", "        if any(keyword in visual_analysis for keyword in exposure_keywords):\n", "            if any(privacy_word in visual_analysis for privacy_word in ['exposed', 'undressed', 'strip']):\n", "                public_exposure_incidents.append({\n", "                    'timestamp': ctx['timestamp'],\n", "                    'visual_evidence': ctx['analysis'],\n", "                    'violation_type': 'Public exposure'\n", "                })\n", "\n", "    print(f\"✅ Found {len(attire_violations)} attire/clothing violations\")\n", "    print(f\"✅ Found {len(privacy_violations)} privacy violations\")\n", "    print(f\"✅ Found {len(dignity_violations)} dignity violations\")\n", "    print(f\"✅ Found {len(public_exposure_incidents)} public exposure incidents\")\n", "\n", "    return privacy_violations, dignity_violations, public_exposure_incidents, attire_violations\n", "\n", "def analyze_harassment_retaliation_patterns(enhanced_transcript, speaker_counts):\n", "    \"\"\"Analyze patterns of harassment or retaliation\"\"\"\n", "    print(\"⚠️ Analyzing harassment and retaliation patterns...\")\n", "\n", "    harassment_indicators = []\n", "    retaliation_patterns = []\n", "\n", "    # Harassment keywords\n", "    harassment_keywords = ['shut up', 'stupid', 'idiot', 'worthless', 'pathetic', 'loser', 'embarrass', 'loud'\n", "                           'humiliate', 'swat', 'loudspeaker']\n", "\n", "    # Retaliation keywords\n", "    retaliation_keywords = ['complained', 'lawyer', 'sue', 'rights', 'report', 'game', 'play', 'embarrass', 'loud'\n", "                           'humiliate', 'swat', 'loudspeaker']\n", "\n", "    # Power assertion keywords\n", "    power_keywords = ['because i said so', 'i am the law', 'do what i tell you', 'you will obey', 'embarrass', 'loud'\n", "                     'humiliate', 'swat', 'loudspeaker', 'shoot', 'hands up', 'cuff', 'detain', 'restrain',\n", "                     'rpo', 'risk protection']\n", "\n", "    # Track escalation after certain triggers\n", "    trigger_timestamps = []\n", "\n", "    for i, word_data in enumerate(enhanced_transcript):\n", "        word_timestamp = word_data['start']\n", "        word_text = word_data['word'].lower()\n", "        speakers = word_data['speakers']\n", "\n", "        # Check for harassment language\n", "        if any(keyword in word_text for keyword in harassment_keywords):\n", "            harassment_indicators.append({\n", "                'timestamp': word_timestamp,\n", "                'content': word_text,\n", "                'speakers': speakers,\n", "                'type': 'Verbal harassment'\n", "            })\n", "\n", "        # Check for retaliation triggers\n", "        if any(keyword in word_text for keyword in retaliation_keywords):\n", "            trigger_timestamps.append(word_timestamp)\n", "\n", "        # Check for power assertion\n", "        if any(keyword in word_text for keyword in power_keywords):\n", "            harassment_indicators.append({\n", "                'timestamp': word_timestamp,\n", "                'content': word_text,\n", "                'speakers': speakers,\n", "                'type': 'Power assertion'\n", "            })\n", "\n", "    # Analyze escalation patterns after triggers\n", "    for trigger_time in trigger_timestamps:\n", "        escalation_window = [word for word in enhanced_transcript\n", "                           if trigger_time < word['start'] < trigger_time + 300]  # 5 minutes after\n", "\n", "        if escalation_window:\n", "            force_words = ['force', 'taser', 'arrest', 'cuff', 'restrain']\n", "            escalation_count = sum(1 for word in escalation_window\n", "                                 if any(force_word in word['word'].lower() for force_word in force_words))\n", "\n", "            if escalation_count > 2:\n", "                retaliation_patterns.append({\n", "                    'trigger_timestamp': trigger_time,\n", "                    'escalation_period': '5 minutes',\n", "                    'escalation_indicators': escalation_count,\n", "                    'type': 'Post-complaint escalation'\n", "                })\n", "\n", "    print(f\"✅ Found {len(harassment_indicators)} harassment indicators\")\n", "    print(f\"✅ Found {len(retaliation_patterns)} retaliation patterns\")\n", "\n", "    return harassment_indicators, retaliation_patterns\n", "\n", "def analyze_misconduct_patterns(enhanced_transcript, visual_context):\n", "    \"\"\"Analyze patterns of coordinated misconduct\"\"\"\n", "    print(\"🕵️ Analyzing misconduct patterns...\")\n", "\n", "    narrative_shaping = []\n", "    coordinated_behavior = []\n", "    selective_enforcement = []\n", "\n", "    # Narrative shaping keywords\n", "    narrative_keywords = ['story', 'report', 'write up', 'document', 'official', 'record']\n", "    coaching_keywords = ['say', 'tell them', 'remember', 'stick to', 'version']\n", "\n", "    # Look for coordination between officers\n", "    officer_speakers = [speaker for speaker in set() for word in enhanced_transcript for speaker in word['speakers'] if 'officer' in str(speaker).lower()]\n", "\n", "    # Analyze for narrative coordination\n", "    for i, word_data in enumerate(enhanced_transcript):\n", "        word_text = word_data['word'].lower()\n", "        speakers = word_data['speakers']\n", "\n", "        if any(keyword in word_text for keyword in narrative_keywords):\n", "            if any(coach_word in word_text for coach_word in coaching_keywords):\n", "                narrative_shaping.append({\n", "                    'timestamp': word_data['start'],\n", "                    'content': word_text,\n", "                    'speakers': speakers,\n", "                    'type': 'Narrative coordination'\n", "                })\n", "\n", "    # Look for coordinated timing in visual evidence\n", "    officer_positioning_times = []\n", "    for ctx in visual_context:\n", "        if 'officer' in ctx['analysis'].lower() and 'position' in ctx['analysis'].lower():\n", "            officer_positioning_times.append(ctx['timestamp'])\n", "\n", "    # Check for coordinated positioning (multiple officers moving within short timeframe)\n", "    for i, time1 in enumerate(officer_positioning_times):\n", "        for time2 in officer_positioning_times[i+1:]:\n", "            if abs(time1 - time2) < 30:  # Within 30 seconds\n", "                coordinated_behavior.append({\n", "                    'timestamp_1': time1,\n", "                    'timestamp_2': time2,\n", "                    'type': 'Coordinated positioning',\n", "                    'time_difference': abs(time1 - time2)\n", "                })\n", "\n", "    print(f\"✅ Found {len(narrative_shaping)} narrative shaping incidents\")\n", "    print(f\"✅ Found {len(coordinated_behavior)} coordinated behavior patterns\")\n", "\n", "    return narrative_shaping, coordinated_behavior, selective_enforcement\n", "\n", "def generate_comprehensive_legal_analysis_document(\n", "    transcript_text, enhanced_transcript, visual_context,\n", "    compliance_violations, behavioral_contradictions,\n", "    privacy_violations, dignity_violations, public_exposure,\n", "    harassment_indicators, retaliation_patterns,\n", "    narrative_shaping, coordinated_behavior,\n", "    skip_seconds=30\n", "):\n", "    \"\"\"Generate comprehensive legal analysis document with all required sections\"\"\"\n", "\n", "    legal_analysis_prompt = f\"\"\"You are a certified forensic audiovisual analyst and constitutional law expert with 25+ years of experience serving as a court-appointed expert witness. Generate a comprehensive legal analysis document based on the integrated audio-visual evidence provided.\n", "\n", "STRUCTURE YOUR ANALYSIS WITH THESE MANDATORY SECTIONS:\n", "\n", "1. STATUTORY VIOLATIONS ANALYSIS:\n", "   - Florida Statute § 394.463 (Baker Act procedures)\n", "   - Florida Statute Chapter 901 (Arrest authority and procedures)\n", "   - Florida Statute § 776.05 (Law enforcement use of force)\n", "   - Florida Statute § 843.02 (Resisting arrest provisions)\n", "   - Florida Administrative Code 11B-27 (Mental health transport)\n", "   - Cite specific violations with timestamp evidence\n", "\n", "2. CONSTITUTIONAL VIOLATIONS ANALYSIS:\n", "   - 4th Amendment: Search and seizure violations, warrant requirements\n", "   - 5th Amendment: Miranda rights, self-incrimination issues\n", "   - 8th Amendment: Excessive force, cruel and unusual punishment\n", "   - 14th Amendment: Due process, equal protection violations\n", "   - Provide specific constitutional analysis with case law citations\n", "\n", "3. PROCEDURAL BREACHES ASSESSMENT:\n", "   - Required warnings not provided (Miranda, medical rights)\n", "   - Transport protocol violations\n", "   - Mental health criteria non-compliance\n", "   - Medical clearance timing violations\n", "   - Supervisor notification failures\n", "   - Chain of custody issues\n", "\n", "4. PATTERNS OF MISCONDUCT IDENTIFICATION:\n", "   - Evidence of coordinated narrative shaping: {len(narrative_shaping)} incidents\n", "   - Coordinated behavior patterns: {len(coordinated_behavior)} instances\n", "   - Retaliatory conduct indicators: {len(retaliation_patterns)} patterns\n", "   - Selective enforcement evidence\n", "\n", "5. PRIVACY & DIGNITY VIOLATIONS:\n", "   - Public exposure incidents: {len(public_exposure)} documented\n", "   - Privacy violations: {len(privacy_violations)} identified\n", "   - Dignity violations: {len(dignity_violations)} documented\n", "   - Inappropriate disclosure or humiliation tactics\n", "\n", "6. USE OF FORCE ASSESSMENT (<PERSON> v. Connor Analysis):\n", "   - Severity of crime factors\n", "   - Immediacy of threat assessment\n", "   - Actively resisting arrest evaluation\n", "   - Attempting to evade by flight analysis\n", "   - Totality of circumstances review\n", "   - Florida agency force protocol compliance\n", "\n", "7. HARASSMENT OR RETALIATION EVIDENCE:\n", "   - Harassment indicators: {len(harassment_indicators)} documented\n", "   - Personal animus evidence\n", "   - Power assertion tactics: documented instances\n", "   - Language indicating improper motive\n", "\n", "8. AUDIO-VISUAL CONTRADICTION ANALYSIS:\n", "   - Commands vs. compliance discrepancies: {len(compliance_violations)} violations\n", "   - Behavioral contradictions: {len(behavioral_contradictions)} identified\n", "   - Officer statements vs. visual evidence mismatches\n", "\n", "EVIDENCE PROVIDED:\n", "- Audio transcript: {len(transcript_text)} characters\n", "- Enhanced transcript: {len(enhanced_transcript)} words\n", "- Visual context points: {len(visual_context)} frames analyzed\n", "- Compliance violations: {compliance_violations}\n", "- Privacy violations: {privacy_violations}\n", "- Harassment patterns: {harassment_indicators}\n", "\n", "Provide specific timestamps, direct quotes, visual evidence references, statutory citations, constitutional analysis, and court-admissible conclusions for each section. Use Bluebook citation format where applicable.\"\"\"\n", "\n", "    try:\n", "        response = openai.ChatCompletion.create(\n", "            model=\"gpt-4\",\n", "            messages=[\n", "                {\"role\": \"system\", \"content\": \"You are a certified forensic legal analyst specializing in constitutional law, criminal procedure, and police misconduct analysis.\"},\n", "                {\"role\": \"user\", \"content\": legal_analysis_prompt}\n", "            ],\n", "            max_tokens=4000,\n", "            temperature=0.05\n", "        )\n", "\n", "        return response.choices[0].message.content\n", "\n", "    except Exception as e:\n", "        print(f\"❌ Comprehensive legal analysis failed: {e}\")\n", "        return f\"Comprehensive legal analysis unavailable: {e}\"\n", "\n", "# ENHANCED ATTIRE AND PRIVACY ANALYSIS\n", "# Add these functions to Cell 4 or replace existing functions\n", "\n", "def analyze_video_frames_for_context_enhanced_attire(video_path, skip_seconds=30):\n", "    \"\"\"Enhanced video analysis with specific attire and privacy detection\"\"\"\n", "    print(\"📹 Analyzing video frames with enhanced attire/privacy detection...\")\n", "\n", "    frames_dir = \"/content/video_frames\"\n", "    os.makedirs(frames_dir, exist_ok=True)\n", "\n", "    extract_frames_cmd = [\n", "        'ffmpeg', '-y', '-ss', str(skip_seconds), '-i', video_path,\n", "        '-vf', 'fps=1/20', '-q:v', '2', f'{frames_dir}/frame_%04d.jpg'\n", "    ]\n", "    subprocess.run(extract_frames_cmd, capture_output=True)\n", "\n", "    frame_files = sorted([f for f in os.listdir(frames_dir) if f.endswith('.jpg')])\n", "    visual_context = []\n", "\n", "    print(f\"🔍 Analyzing {len(frame_files)} video frames with attire focus...\")\n", "\n", "    for i, frame_file in enumerate(frame_files):\n", "        frame_path = os.path.join(frames_dir, frame_file)\n", "        timestamp = (i * 20) + skip_seconds\n", "\n", "        try:\n", "            with open(frame_path, 'rb') as f:\n", "                frame_data = base64.b64encode(f.read()).decode()\n", "\n", "            response = openai.ChatCompletion.create(\n", "                model=\"gpt-4o\",\n", "                messages=[\n", "                    {\n", "                        \"role\": \"user\",\n", "                        \"content\": [\n", "                            {\n", "                                \"type\": \"text\",\n", "                                \"text\": \"\"\"Conduct detailed forensic analysis of this police bodycam frame with SPECIFIC ATTENTION to clothing and privacy issues:\n", "\n", "1. ATTIRE ANALYSIS (CRITICAL FOR PRIVACY ASSESSMENT):\n", "   - Clothing status: Fully dressed, partially dressed, minimal clothing, towel only, etc.\n", "   - State of dress: Appropriate, inappropriate for public, emergency exit clothing\n", "   - Signs of rushed dressing: Incomplete clothing, towel wrapped, wet appearance\n", "   - Modesty concerns: Areas of body exposed, coverage inadequacy\n", "   - Footwear: Barefoot, shoes, slippers (indicates emergency exit)\n", "\n", "2. PRIVACY & DIGNITY INDICATORS:\n", "   - Public exposure level: Private home vs. public view\n", "   - Bystander presence: Neighbors, crowds, passersby witnessing exposure\n", "   - Recording implications: Subject aware of being filmed in state of undress\n", "   - Weather conditions affecting minimal clothing exposure\n", "\n", "3. EMERGENCY/CRISIS INDICATORS:\n", "   - Wet hair/body (shower interruption)\n", "   - Rushed appearance (hastily grabbed clothing/towel)\n", "   - Bathroom/shower context (wet floors, steam, towels visible)\n", "   - Time pressure indicators (incomplete dressing)\n", "\n", "4. RESTRAINT/HANDCUFFING ANALYSIS:\n", "   - Handcuff application on subject in minimal clothing\n", "   - Positioning: hands behind back while in towel/minimal clothing\n", "   - Dignity concerns during restraint application\n", "   - Cooperative behavior vs. restraint necessity\n", "\n", "5. STANDARD FORENSIC ELEMENTS:\n", "   - Scene setting and location context\n", "   - People positions and actions\n", "   - Equipment and evidence visible\n", "   - Officer positioning relative to undressed subject\n", "   - Safety and tactical considerations\n", "\n", "6. CONSTITUTIONAL CONCERNS:\n", "   - 4th Amendment: Privacy expectations in home\n", "   - 8th Amendment: Dignity during detention\n", "   - Public exposure creating humiliation\n", "   - Reasonable accommodation for clothing needs\n", "\n", "Be extremely specific about clothing status and privacy implications. Flag any dignitary concerns or inappropriate exposure situations. Document if subject appears to have been interrupted during private activities (bathing, dressing, etc.).\"\"\"\n", "                            },\n", "                            {\n", "                                \"type\": \"image_url\",\n", "                                \"image_url\": {\n", "                                    \"url\": f\"data:image/jpeg;base64,{frame_data}\"\n", "                                }\n", "                            }\n", "                        ]\n", "                    }\n", "                ],\n", "                max_tokens=600,\n", "                temperature=0.1\n", "            )\n", "\n", "            visual_analysis = response.choices[0].message.content\n", "            visual_context.append({\n", "                'timestamp': timestamp,\n", "                'frame': frame_file,\n", "                'analysis': visual_analysis\n", "            })\n", "\n", "            print(f\"✅ Enhanced frame analysis: {timestamp//60:02d}:{timestamp%60:02d}\")\n", "\n", "        except Exception as e:\n", "            print(f\"⚠️ Frame analysis failed for {frame_file}: {e}\")\n", "            visual_context.append({\n", "                'timestamp': timestamp,\n", "                'frame': frame_file,\n", "                'analysis': f\"Visual analysis unavailable: {e}\"\n", "            })\n", "\n", "    print(f\"✅ Enhanced visual context analysis complete: {len(visual_context)} frames\")\n", "    return visual_context\n", "\n", "def analyze_privacy_dignity_violations_enhanced(enhanced_transcript, visual_context, skip_seconds=30):\n", "    \"\"\"Enhanced privacy and dignity analysis with specific attire focus\"\"\"\n", "    print(\"🔒 Enhanced privacy and dignity violations analysis...\")\n", "\n", "    privacy_violations = []\n", "    dignity_violations = []\n", "    attire_violations = []\n", "    public_exposure_incidents = []\n", "\n", "    # Enhanced keywords for clothing/attire situations\n", "    attire_keywords = ['towel', 'naked', 'undressed', 'partially clothed', 'minimal clothing',\n", "                      'barefoot', 'wet', 'shower', 'bathing', 'bathroom', 'rushed', 'incomplete dress']\n", "\n", "    emergency_exit_keywords = ['rushed out', 'hurried', 'interrupted', 'grabbed towel',\n", "                              'wet hair', 'steam', 'bathroom door', 'shower interrupted']\n", "\n", "    exposure_keywords = ['exposed', 'visible', 'uncovered', 'inappropriate', 'public view',\n", "                        'neighbors seeing', 'crowd watching', 'filming', 'recording']\n", "\n", "    handcuff_dignity_keywords = ['handcuffed', 'cuffed', 'restrained', 'hands behind back',\n", "                                'restraints applied', 'detained']\n", "\n", "    # Analyze audio for clothing/exposure references\n", "    for i, word_data in enumerate(enhanced_transcript):\n", "        word_timestamp = word_data['start'] + skip_seconds\n", "        word_text = word_data['word'].lower()\n", "        speakers = word_data['speakers']\n", "\n", "        # Check for attire-related violations\n", "        if any(keyword in word_text for keyword in attire_keywords):\n", "            # Find corresponding visual context\n", "            visual_evidence = None\n", "            for ctx in visual_context:\n", "                if abs(ctx['timestamp'] - word_timestamp) <= 60:\n", "                    visual_evidence = ctx['analysis']\n", "                    break\n", "\n", "            attire_violations.append({\n", "                'timestamp': word_timestamp,\n", "                'audio_evidence': word_text,\n", "                'visual_evidence': visual_evidence[:300] if visual_evidence else 'No visual context',\n", "                'violation_type': 'Attire/Clothing Privacy Concern',\n", "                'speakers': speakers\n", "            })\n", "\n", "        # Check for handcuffing dignity concerns with attire context\n", "        if any(keyword in word_text for keyword in handcuff_dignity_keywords):\n", "            # Check if this occurs near attire violations\n", "            attire_context = any(attire_word in enhanced_transcript[max(0, i-10):i+10][j]['word'].lower()\n", "                               for j in range(len(enhanced_transcript[max(0, i-10):i+10]))\n", "                               for attire_word in ['towel', 'naked', 'undressed', 'wet'])\n", "\n", "            if attire_context:\n", "                dignity_violations.append({\n", "                    'timestamp': word_timestamp,\n", "                    'audio_evidence': word_text,\n", "                    'violation_type': 'Handcuffing Individual in Minimal Clothing - Dignity Violation',\n", "                    'speakers': speakers,\n", "                    'severity': 'HIGH',\n", "                    'constitutional_concern': '8th Amendment - Cruel and unusual punishment'\n", "                })\n", "\n", "        # Check for emergency exit situations\n", "        if any(keyword in word_text for keyword in emergency_exit_keywords):\n", "            privacy_violations.append({\n", "                'timestamp': word_timestamp,\n", "                'audio_evidence': word_text,\n", "                'violation_type': 'Emergency Exit Privacy Violation',\n", "                'speakers': speakers\n", "            })\n", "\n", "    # Enhanced visual analysis for clothing/exposure\n", "    for ctx in visual_context:\n", "        visual_analysis = ctx['analysis'].lower()\n", "\n", "        # Check for clothing-related exposure\n", "        clothing_indicators = ['towel only', 'minimal clothing', 'partially dressed', 'undressed',\n", "                              'wet from shower', 'barefoot', 'rushed dressing', 'incomplete clothing']\n", "\n", "        if any(indicator in visual_analysis for indicator in clothing_indicators):\n", "            # Check if handcuffing is involved\n", "            handcuff_indicators = ['handcuff', 'cuff', 'restrain', 'hands behind back']\n", "            is_handcuffed = any(hc_word in visual_analysis for hc_word in handcuff_indicators)\n", "\n", "            # Check if in public view\n", "            public_indicators = ['public', 'neighbors', 'crowd', 'street', 'outside', 'porch', 'yard']\n", "            is_public = any(pub_word in visual_analysis for pub_word in public_indicators)\n", "\n", "            violation_type = 'Public Exposure - Minimal Clothing' if is_public else 'Private Exposure Documentation'\n", "\n", "            if is_handcuffed:\n", "                violation_type += ' + Restraint Applied'\n", "\n", "            public_exposure_incidents.append({\n", "                'timestamp': ctx['timestamp'],\n", "                'visual_evidence': ctx['analysis'],\n", "                'violation_type': violation_type,\n", "                'severity': 'CRITICAL' if (is_public and is_handcuffed) else 'HIGH' if is_public else 'MODERATE',\n", "                'clothing_status': 'MINIMAL/INADEQUATE',\n", "                'restraint_status': 'RESTRAINED' if is_handcuffed else 'UNRESTRAINED'\n", "            })\n", "\n", "        # Check for dignity violations\n", "        dignity_indicators = ['humiliating', 'embarrassing', 'inappropriate exposure',\n", "                             'forced to remain undressed', 'denied clothing']\n", "\n", "        if any(indicator in visual_analysis for indicator in dignity_indicators):\n", "            dignity_violations.append({\n", "                'timestamp': ctx['timestamp'],\n", "                'visual_evidence': ctx['analysis'],\n", "                'violation_type': 'Dignity Violation - Inappropriate Exposure',\n", "                'severity': 'HIGH'\n", "            })\n", "\n", "        # Check for emergency/crisis interruption\n", "        emergency_indicators = ['shower interrupted', 'rushed from bathroom', 'wet appearance',\n", "                               'emergency exit', 'hastily dressed', 'grabbed towel']\n", "\n", "        if any(indicator in visual_analysis for indicator in emergency_indicators):\n", "            privacy_violations.append({\n", "                'timestamp': ctx['timestamp'],\n", "                'visual_evidence': ctx['analysis'],\n", "                'violation_type': 'Emergency Privacy Interruption',\n", "                'context': 'Interrupted Private Activity'\n", "            })\n", "\n", "    print(f\"✅ Found {len(attire_violations)} attire/clothing violations\")\n", "    print(f\"✅ Found {len(privacy_violations)} privacy violations\")\n", "    print(f\"✅ Found {len(dignity_violations)} dignity violations\")\n", "    print(f\"✅ Found {len(public_exposure_incidents)} public exposure incidents\")\n", "\n", "    return privacy_violations, dignity_violations, public_exposure_incidents, attire_violations\n", "\n", "def inject_attire_context_annotations(enhanced_transcript, visual_context, skip_seconds=30):\n", "    \"\"\"Inject specific attire and privacy context annotations\"\"\"\n", "    print(\"💉 Injecting attire and privacy context annotations...\")\n", "\n", "    attire_annotations = {}\n", "\n", "    for i, word_data in enumerate(enhanced_transcript):\n", "        word_timestamp = word_data['start'] + skip_seconds\n", "        word_text = word_data['word'].lower()\n", "\n", "        # Find corresponding visual context for this word\n", "        closest_visual = None\n", "        min_time_diff = float('inf')\n", "\n", "        for ctx in visual_context:\n", "            time_diff = abs(ctx['timestamp'] - word_timestamp)\n", "            if time_diff < min_time_diff and time_diff < 15:\n", "                min_time_diff = time_diff\n", "                closest_visual = ctx\n", "\n", "        if closest_visual:\n", "            visual_text = closest_visual['analysis'].lower()\n", "\n", "            # Check for specific attire situations\n", "            if any(indicator in visual_text for indicator in ['towel only', 'minimal clothing', 'undressed']):\n", "                attire_annotations[i] = \"*{ATTIRE CONCERN: Subject in minimal clothing/towel only - Privacy implications}*\"\n", "\n", "            elif any(indicator in visual_text for indicator in ['wet from shower', 'rushed from bathroom']):\n", "                attire_annotations[i] = \"*{EMERGENCY EXIT: Subject interrupted during private activity - Constitutional privacy concern}*\"\n", "\n", "            elif any(indicator in visual_text for indicator in ['handcuff', 'restrain']) and any(attire in visual_text for attire in ['towel', 'minimal', 'undressed']):\n", "                attire_annotations[i] = \"*{CRITICAL DIGNITY VIOLATION: Restraint applied to subject in minimal clothing - 8th Amendment concern}*\"\n", "\n", "            elif any(indicator in visual_text for indicator in ['public exposure', 'neighbors seeing']):\n", "                attire_annotations[i] = \"*{PUBLIC EXPOSURE: Inappropriate clothing status in public view - Dignity violation}*\"\n", "\n", "            elif any(indicator in visual_text for indicator in ['barefoot', 'incomplete dress']):\n", "                attire_annotations[i] = \"*{RUSHED DRESSING: Emergency exit indicators - Privacy interruption documented}*\"\n", "\n", "    print(f\"✅ Attire context annotations prepared: {len(attire_annotations)} annotations\")\n", "    return attire_annotations\n", "\n", "print(\"✅ Enhanced attire and privacy analysis functions loaded!\")\n", "def calculate_violation_severity_score(violation_type, context_factors):\n", "    \"\"\"Calculate severity score for violations based on multiple factors\"\"\"\n", "\n", "    base_scores = {\n", "        \"handcuffing_minimal_clothing\": 9,\n", "        \"public_exposure\": 8,\n", "        \"privacy_interruption\": 7,\n", "        \"excessive_force\": 9,\n", "        \"miranda_violation\": 8,\n", "        \"consent_violation\": 7,\n", "        \"dignity_violation\": 8,\n", "        \"harassment\": 7,\n", "        \"retaliation\": 8,\n", "        \"narrative_coordination\": 6\n", "    }\n", "\n", "    multipliers = {\n", "        \"multiple_witnesses\": 1.3,\n", "        \"recording_present\": 1.2,\n", "        \"vulnerable_individual\": 1.4,\n", "        \"mental_health_crisis\": 1.3,\n", "        \"cooperative_subject\": 1.5,\n", "        \"public_location\": 1.3,\n", "        \"repeated_behavior\": 1.4\n", "    }\n", "\n", "    # Get base score\n", "    base_score = base_scores.get(violation_type, 5)\n", "\n", "    # Apply multipliers\n", "    final_score = base_score\n", "    for factor, value in context_factors.items():\n", "        if value and factor in multipliers:\n", "            final_score *= multipliers[factor]\n", "\n", "    return min(10, round(final_score, 1))  # Cap at 10\n", "\n", "def generate_violation_timeline(violations_data, skip_seconds=30):\n", "    \"\"\"Generate chronological timeline of all violations\"\"\"\n", "\n", "    timeline_events = []\n", "\n", "    # Collect all violations with timestamps\n", "    for vtype, violations in violations_data.items():\n", "        for v in violations:\n", "            timestamp = v.get('timestamp', 0)\n", "            timeline_events.append({\n", "                'timestamp': timestamp,\n", "                'type': vtype,\n", "                'details': v,\n", "                'severity': v.get('severity', 'MODERATE')\n", "            })\n", "\n", "    # Sort by timestamp\n", "    timeline_events.sort(key=lambda x: x['timestamp'])\n", "\n", "    # Format timeline\n", "    timeline_str = \"CHRONOLOGICAL VIOLATION TIMELINE:\\n\"\n", "    timeline_str += \"=\"*50 + \"\\n\\n\"\n", "\n", "    for event in timeline_events:\n", "        time_str = str(timedelta(seconds=int(event['timestamp'] - skip_seconds)))\n", "        timeline_str += f\"[{time_str}] {event['type'].upper()}\\n\"\n", "        timeline_str += f\"   Severity: {event['severity']}\\n\"\n", "\n", "        # Add specific details based on type\n", "        details = event['details']\n", "        if 'audio_evidence' in details:\n", "            timeline_str += f\"   Audio: {details['audio_evidence']}\\n\"\n", "        if 'visual_evidence' in details:\n", "            timeline_str += f\"   Visual: {details['visual_evidence'][:100]}...\\n\"\n", "        if 'violation_type' in details:\n", "            timeline_str += f\"   Type: {details['violation_type']}\\n\"\n", "\n", "        timeline_str += \"\\n\"\n", "\n", "    return timeline_str\n", "\n", "def analyze_body_camera_muting_patterns(enhanced_transcript, skip_seconds=30):\n", "    \"\"\"Analyze patterns of body camera muting or deactivation\"\"\"\n", "    print(\"📹 Analyzing body camera muting patterns...\")\n", "\n", "    muting_incidents = []\n", "    mute_keywords = ['mute', 'turn off', 'camera off', 'stop recording', 'blue', 'deactivate']\n", "\n", "    for i, word_data in enumerate(enhanced_transcript):\n", "        word_timestamp = word_data['start'] + skip_seconds\n", "        word_text = word_data['word'].lower()\n", "        speakers = word_data['speakers']\n", "\n", "        if any(keyword in word_text for keyword in mute_keywords):\n", "            # Look for context around muting\n", "            context_start = max(0, i - 20)\n", "            context_end = min(len(enhanced_transcript), i + 20)\n", "            context_words = ' '.join([w['word'] for w in enhanced_transcript[context_start:context_end]])\n", "\n", "            muting_incidents.append({\n", "                'timestamp': word_timestamp,\n", "                'keyword': word_text,\n", "                'speakers': speakers,\n", "                'context': context_words,\n", "                'suspicious': any(word in context_words.lower() for word in ['don\\'t', 'stop', 'turn off', 'need to'])\n", "            })\n", "\n", "    print(f\"✅ Found {len(muting_incidents)} potential camera muting references\")\n", "    return muting_incidents\n", "\n", "def extract_officer_identities(enhanced_transcript, visual_context):\n", "    \"\"\"Extract and track officer identities throughout the encounter using BERT NER\"\"\"\n", "    print(\"👮 Extracting officer identities using BERT NER...\")\n", "\n", "    # Initialize BERT NER pipeline\n", "    try:\n", "        ner_pipeline = pipeline(\"ner\", model=\"dslim/bert-base-NER\", aggregation_strategy=\"simple\")\n", "    except Exception as e:\n", "        print(f\"⚠️ NER model loading failed: {e}\")\n", "        return {}\n", "\n", "    officer_data = {}\n", "    title_patterns = ['officer', 'sergeant', 'lieutenant', 'deputy', 'detective', 'captain']\n", "\n", "    # Build text chunks around officer titles for NER processing\n", "    text_chunks = []\n", "    chunk_metadata = []\n", "\n", "    for i, word_data in enumerate(enhanced_transcript):\n", "        word_text = word_data['word'].lower()\n", "\n", "        # Look for officer titles\n", "        if any(pattern in word_text for pattern in title_patterns):\n", "            # Extract context window (20 words before and after)\n", "            start_idx = max(0, i - 20)\n", "            end_idx = min(len(enhanced_transcript), i + 20)\n", "\n", "            # Build text chunk\n", "            chunk_words = [enhanced_transcript[j]['word'] for j in range(start_idx, end_idx)]\n", "            chunk_text = ' '.join(chunk_words)\n", "\n", "            text_chunks.append(chunk_text)\n", "            chunk_metadata.append({\n", "                'timestamp': word_data['start'],\n", "                'title_found': word_text,\n", "                'speakers': word_data['speakers']\n", "            })\n", "\n", "    # Process chunks with NER\n", "    for chunk_text, metadata in zip(text_chunks, chunk_metadata):\n", "        try:\n", "            # Run NER on chunk\n", "            entities = ner_pipeline(chunk_text)\n", "\n", "            # Extract person names near officer titles\n", "            for entity in entities:\n", "                if entity['entity_group'] == 'PER':  # Person entity\n", "                    name = entity['word'].strip()\n", "\n", "                    # Clean up name (remove ## tokens from BERT)\n", "                    name = name.replace('##', '')\n", "\n", "                    # Create unique officer ID\n", "                    officer_id = f\"{metadata['title_found']}_{name}\".upper()\n", "\n", "                    if officer_id not in officer_data:\n", "                        officer_data[officer_id] = {\n", "                            'name': name,\n", "                            'title': metadata['title_found'],\n", "                            'first_mention': metadata['timestamp'],\n", "                            'mentions': [],\n", "                            'speakers': metadata['speakers']\n", "                        }\n", "\n", "                    officer_data[officer_id]['mentions'].append({\n", "                        'timestamp': metadata['timestamp'],\n", "                        'context': chunk_text[:100]\n", "                    })\n", "\n", "        except Exception as e:\n", "            print(f\"⚠️ NER processing error: {e}\")\n", "\n", "    # Also extract badge numbers and identifiers from visual analysis\n", "    badge_pattern = r'(?:badge|unit|car)\\s*#?\\s*(\\d+)'\n", "\n", "    for ctx in visual_context:\n", "        visual_text = ctx['analysis'].lower()\n", "        if 'officer' in visual_text or 'badge' in visual_text:\n", "            # Extract badge numbers\n", "            import re\n", "            badges = re.findall(badge_pattern, visual_text)\n", "            for badge in badges:\n", "                badge_id = f\"BADGE_{badge}\"\n", "                if badge_id not in officer_data:\n", "                    officer_data[badge_id] = {\n", "                        'badge_number': badge,\n", "                        'visual_timestamp': ctx['timestamp'],\n", "                        'visual_context': visual_text[:200]\n", "                    }\n", "\n", "    print(f\"✅ Identified {len(officer_data)} unique officer references\")\n", "    return officer_data\n", "\n", "def analyze_de_escalation_failures(enhanced_transcript, visual_context, skip_seconds=30):\n", "    \"\"\"Analyze de-escalation attempts and failures\"\"\"\n", "    print(\"📉 Analyzing de-escalation patterns...\")\n", "\n", "    de_escalation_events = []\n", "    escalation_events = []\n", "\n", "    # De-escalation keywords\n", "    de_escalation_keywords = ['calm down', 'relax', 'take a breath', 'easy', 'talk to me',\n", "                             'help you', 'understand', 'listen', 'explain', 'work with']\n", "\n", "    # Escalation keywords\n", "    escalation_keywords = ['hands up', 'get down', 'don\\'t move', 'stop', 'now',\n", "                          'do it', 'comply', 'force', 'taser', 'arrest', 'cuff']\n", "\n", "    # Track escalation trajectory\n", "    for i, word_data in enumerate(enhanced_transcript):\n", "        word_timestamp = word_data['start'] + skip_seconds\n", "        word_text = word_data['word'].lower()\n", "        speakers = word_data['speakers']\n", "\n", "        # Check for de-escalation attempts\n", "        if any(keyword in word_text for keyword in de_escalation_keywords):\n", "            # Look ahead to see if escalation follows\n", "            escalation_follows = False\n", "            for j in range(i+1, min(i+50, len(enhanced_transcript))):\n", "                if any(esc_word in enhanced_transcript[j]['word'].lower() for esc_word in escalation_keywords):\n", "                    escalation_follows = True\n", "                    break\n", "\n", "            de_escalation_events.append({\n", "                'timestamp': word_timestamp,\n", "                'text': word_text,\n", "                'speakers': speakers,\n", "                'followed_by_escalation': escalation_follows,\n", "                'effectiveness': 'FAILED' if escalation_follows else 'UNCLEAR'\n", "            })\n", "\n", "        # Check for escalation\n", "        if any(keyword in word_text for keyword in escalation_keywords):\n", "            escalation_events.append({\n", "                'timestamp': word_timestamp,\n", "                'text': word_text,\n", "                'speakers': speakers\n", "            })\n", "\n", "    # Analyze visual escalation indicators\n", "    for ctx in visual_context:\n", "        visual_text = ctx['analysis'].lower()\n", "        if any(indicator in visual_text for indicator in ['weapon drawn', 'aggressive stance',\n", "                                                          'hands on weapon', 'tactical position']):\n", "            escalation_events.append({\n", "                'timestamp': ctx['timestamp'],\n", "                'type': 'VISUAL',\n", "                'evidence': ctx['analysis'][:200]\n", "            })\n", "\n", "    print(f\"✅ Found {len(de_escalation_events)} de-escalation attempts\")\n", "    print(f\"✅ Found {len(escalation_events)} escalation events\")\n", "\n", "    return de_escalation_events, escalation_events\n", "\n", "def generate_executive_summary_enhanced(all_violations, transcript_length):\n", "    \"\"\"Generate enhanced executive summary with key findings and recommendations\"\"\"\n", "\n", "    summary = \"EXECUTIVE SUMMARY - KEY FINDINGS:\\n\"\n", "    summary += \"=\"*50 + \"\\n\\n\"\n", "\n", "    # Calculate total violations\n", "    total_violations = sum(len(v) for v in all_violations.values())\n", "\n", "    # Identify most serious violations\n", "    critical_violations = []\n", "    for vtype, violations in all_violations.items():\n", "        for v in violations:\n", "            if v.get('severity') in ['CRITICAL', 'HIGH']:\n", "                critical_violations.append((vtype, v))\n", "\n", "    summary += f\"Total Violations Identified: {total_violations}\\n\"\n", "    summary += f\"Critical/High Severity: {len(critical_violations)}\\n\"\n", "    summary += f\"Transcript Coverage: {transcript_length} words analyzed\\n\\n\"\n", "\n", "    summary += \"MOST SERIOUS VIOLATIONS:\\n\"\n", "    summary += \"-\"*30 + \"\\n\"\n", "    for vtype, violation in critical_violations[:5]:  # Top 5\n", "        summary += f\"• {vtype}: {violation.get('violation_type', 'N/A')}\\n\"\n", "\n", "    summary += \"\\nRECOMMENDED IMMEDIATE ACTIONS:\\n\"\n", "    summary += \"-\"*30 + \"\\n\"\n", "    summary += \"1. Preserve all bodycam footage and evidence\\n\"\n", "    summary += \"2. Document witness statements\\n\"\n", "    summary += \"3. Photograph any injuries or property damage\\n\"\n", "    summary += \"4. File formal complaints with appropriate agencies\\n\"\n", "    summary += \"5. Consult with civil rights attorney\\n\\n\"\n", "\n", "    return summary\n", "\n", "print(\"✅ Enhanced violation analysis functions loaded!\")\n", "print(\"✅ Enhanced legal analysis functions loaded!\")\n", "print(\"✅ Enhanced forensic pipeline functions loaded successfully!\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TnguHNYU3sZj", "outputId": "275b9677-eb80-4ef5-81b6-e813da32272a"}, "execution_count": 4, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Using device: cuda\n", "✅ Enhanced attire and privacy analysis functions loaded!\n", "✅ Enhanced violation analysis functions loaded!\n", "✅ Enhanced legal analysis functions loaded!\n", "✅ Enhanced forensic pipeline functions loaded successfully!\n"]}]}, {"cell_type": "code", "source": ["# =============================================================================\n", "# Cell 5: <PERSON><PERSON> Enhanced Speaker Diarization Pipeline\n", "# =============================================================================\n", "print(\"👥 Loading enhanced speaker diarization pipeline...\")\n", "\n", "try:\n", "    diarization_pipeline = Pipeline.from_pretrained(\n", "        \"pyannote/speaker-diarization-3.1\",\n", "        use_auth_token=HF_TOKEN\n", "    )\n", "    diarization_pipeline.to(torch.device(device))\n", "    print(\"✅ Enhanced speaker diarization pipeline loaded successfully!\")\n", "except Exception as e:\n", "    print(f\"❌ Failed to load speaker diarization: {e}\")\n", "    print(\"Please check your HuggingFace token permissions\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 316, "referenced_widgets": ["857d0ab38b574b3a8d34b06e39da685c", "ddc4f1a7f3804099ba8a7722b5ad603d", "aa941722e29b46089cf09820bf86295c", "225e8209a3844b22aa27286dc0afb00f", "cb915a3ed332471d9c3db12f52bdc10e", "9df41b94d7594975b7cf18162296ad70", "8985a5112f6a454ea3871994be9e4779", "89e02fe58afe46419ac6561a9abcf0fc", "812253386b6a4e99933811cb623cc916", "936cd3fa49674dd5b99dd03d6b86b50c", "27301b53b8c742e4adc96ffe390d7fe6", "6396a6cc71e045f68255d37eec834c42", "9c7224858c5d476b8cd0f4ffc5cdde61", "ebba6ac9d9e64270b4d25d6664f89b53", "fe0f29732c974dc38211580c3abcdcbe", "79320209d9244569b42444760e39a1d7", "5f32c686160040509cfe950a0278364f", "8f6d349abeb34cc1a901b5257e14059e", "83b9dfe2b3bb45c1995c2096fb65fabe", "2cd8df1f944942c8b13c8491d581ab83", "b2c6cbb083614496b621c17b52bac577", "7e4e892851dd48739848e8870abdff21", "5ac1d8bed7ce4e10a96ddca019ce43f1", "2f3d4d03573e408db86860c4307152c0", "c893b7b87c934e198edf8a06cd98e340", "bc67ad0be01943e69970c3a9298d8b3c", "ea4e34feb5ee42de926b717295192e88", "ba9dce58da3c4a65b7d35b602d2bfe21", "697bb5aa6b5c4e4ea16be2939bea0de7", "4ef09e2bdfe749b0bb19426d0734a326", "e3eae5fb958149b0af85bb90ef332915", "11d98b88b7ed454bb65522098dfc4a6d", "7b0e4cbd13174ef2a318ffb21c4d2cf7", "135eaad9889845babe88c9cc399257d4", "dce840a14bc6461981f64e4b5332873f", "764d82e21ed84a99b082dd3be73fa0e4", "3e0452917e674df885f0e176b3a023c3", "159932ffe3c54856ae8ada09ab11afc3", "1c6b2472ecf14caeb5cc9a0f4396bce6", "1371d8b386f74c4f8c516654bbdb7732", "d9eb8cdfc6ef41249228b1efb17445b3", "2beb134408454bc8b5cafe863fd50b26", "81800fcc4b8e488fbfc3cf0b7ccd28c2", "2f2c1b3087d740df8441e62b7663dcfc", "0d1668720b5a46b8ae616cde434952ce", "1ac2572d365b4874ac4fe27dd68333d1", "947a347fdbc1411cb0f501b54202c899", "0a6396aead7845d0980e7859f775c230", "de5d12618a914ac886ee2e035fd830ae", "6f175fb2170e4bb7a6d38ddbc7a9d0dc", "88ac566e57ba4b8bba83a3b452424f4f", "d95b863aa7a748c18e17bc62e3c83eaa", "9f2faf7ffef64780955459fc968d5f7f", "eea59c7bdef04e3db4824ae5cb609c93", "5d75ff6fecd445c4ba3e60f7606c7290"]}, "id": "JVNTPWUw3sc9", "outputId": "b2db7411-bf64-473f-fdd3-7c468ca07727"}, "execution_count": 5, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["👥 Loading enhanced speaker diarization pipeline...\n"]}, {"output_type": "display_data", "data": {"text/plain": ["config.yaml:   0%|          | 0.00/469 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "857d0ab38b574b3a8d34b06e39da685c"}}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["DEBUG:speechbrain.utils.checkpoints:Registered checkpoint save hook for _speechbrain_save\n", "DEBUG:speechbrain.utils.checkpoints:Registered checkpoint load hook for _speechbrain_load\n", "DEBUG:speechbrain.utils.checkpoints:Registered checkpoint save hook for save\n", "DEBUG:speechbrain.utils.checkpoints:Registered checkpoint load hook for load\n", "DEBUG:speechbrain.utils.checkpoints:Registered checkpoint save hook for _save\n", "DEBUG:speechbrain.utils.checkpoints:Registered checkpoint load hook for _recover\n"]}, {"output_type": "display_data", "data": {"text/plain": ["pytorch_model.bin:   0%|          | 0.00/5.91M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "6396a6cc71e045f68255d37eec834c42"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["config.yaml:   0%|          | 0.00/399 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "5ac1d8bed7ce4e10a96ddca019ce43f1"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["pytorch_model.bin:   0%|          | 0.00/26.6M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "135eaad9889845babe88c9cc399257d4"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["config.yaml:   0%|          | 0.00/221 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "0d1668720b5a46b8ae616cde434952ce"}}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["✅ Enhanced speaker diarization pipeline loaded successfully!\n"]}]}, {"cell_type": "code", "source": ["# =============================================================================\n", "# Cell 6: Complete Enhanced Forensic Processing Function WITH ALL IMPROVEMENTS\n", "# =============================================================================\n", "def process_complete_enhanced_forensic_analysis(video_path, skip_seconds=30):\n", "    \"\"\"\n", "    Complete enhanced forensic pipeline with comprehensive legal analysis\n", "    \"\"\"\n", "    print(\"🏛️ ENHANCED CERTIFIED FORENSIC AUDIOVISUAL ANALYSIS\")\n", "    print(\"=\"*80)\n", "\n", "    # Steps 1-7: Same as before (audio extraction, enhancement, transcription, visual analysis, etc.)\n", "    audio_raw = \"/content/extracted_audio_raw.wav\"\n", "    extract_cmd = [\n", "        'ffmpeg', '-y', '-ss', str(skip_seconds), '-i', video_path,\n", "        '-acodec', 'pcm_s16le', '-ar', '16000', '-ac', '1', audio_raw\n", "    ]\n", "    subprocess.run(extract_cmd, capture_output=True)\n", "    print(f\"✅ Raw audio extracted (skipping first {skip_seconds} seconds)\")\n", "\n", "    audio_enhanced = \"/content/enhanced_forensic_audio_v2.wav\"\n", "    enhanced_audio_processing_for_difficult_sections(audio_raw, audio_enhanced)\n", "\n", "    whisper_result = transcribe_with_maximum_accuracy_enhanced(audio_enhanced)\n", "    visual_context = analyze_video_frames_for_context_enhanced_attire(video_path, skip_seconds)\n", "\n", "    print(\"👥 Running enhanced speaker diarization...\")\n", "    diarization_result = diarization_pipeline(audio_enhanced)\n", "\n", "    overlaps = detect_speaker_overlaps_and_separate_enhanced(audio_enhanced, diarization_result, whisper_result)\n", "    enhanced_transcript = combine_transcription_and_speakers_enhanced(whisper_result, diarization_result, overlaps)\n", "\n", "    # NEW: Comprehensive Legal Analysis Components\n", "    print(\"📋 Conducting comprehensive legal analysis...\")\n", "\n", "    # Cross-reference utterances with behavior\n", "    compliance_violations, behavioral_contradictions = cross_reference_utterances_with_behavior(\n", "        enhanced_transcript, visual_context, skip_seconds\n", "    )\n", "\n", "    # Privacy and dignity analysis\n", "    privacy_violations, dignity_violations, public_exposure, attire_violations = analyze_privacy_dignity_violations_enhanced(\n", "        enhanced_transcript, visual_context, skip_seconds\n", "    )\n", "\n", "    # Harassment and retaliation analysis\n", "    speaker_counts = {}\n", "    for word_data in enhanced_transcript:\n", "        for speaker in word_data.get('speakers', []):\n", "            speaker_counts[speaker] = speaker_counts.get(speaker, 0) + 1\n", "\n", "    harassment_indicators, retaliation_patterns = analyze_harassment_retaliation_patterns(\n", "        enhanced_transcript, speaker_counts\n", "    )\n", "\n", "    # Misconduct patterns analysis\n", "    narrative_shaping, coordinated_behavior, selective_enforcement = analyze_misconduct_patterns(\n", "        enhanced_transcript, visual_context\n", "    )\n", "\n", "    # NEW: Analyze body camera muting patterns\n", "    muting_incidents = analyze_body_camera_muting_patterns(enhanced_transcript, skip_seconds)\n", "\n", "    # NEW: Extract officer identities\n", "    officer_identities = extract_officer_identities(enhanced_transcript, visual_context)\n", "\n", "    # NEW: Analyze de-escalation failures\n", "    de_escalation_events, escalation_events = analyze_de_escalation_failures(\n", "        enhanced_transcript, visual_context, skip_seconds\n", "    )\n", "\n", "    # NEW: Analyze transcript confidence\n", "    confidence_stats = analyze_transcript_confidence_metrics(enhanced_transcript)\n", "\n", "    # Generate comprehensive legal analysis document\n", "    comprehensive_legal_analysis = generate_comprehensive_legal_analysis_document(\n", "        whisper_result['text'], enhanced_transcript, visual_context,\n", "        compliance_violations, behavioral_contradictions,\n", "        privacy_violations, dignity_violations, public_exposure,\n", "        harassment_indicators, retaliation_patterns,\n", "        narrative_shaping, coordinated_behavior,\n", "        skip_seconds\n", "    )\n", "\n", "    # Enhanced contextual annotations and visual injections\n", "    annotations = inject_contextual_annotations_enhanced(enhanced_transcript)\n", "    visual_injections = inject_visual_context_into_transcript(enhanced_transcript, visual_context, skip_seconds)\n", "    attire_annotations = inject_attire_context_annotations(enhanced_transcript, visual_context, skip_seconds)\n", "\n", "    # IMPROVED: Combine all annotations properly\n", "    all_annotations = {**annotations, **attire_annotations}\n", "\n", "    # Generate comprehensive output document\n", "    output_path = \"/content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt\"\n", "\n", "    with open(output_path, \"w\", encoding=\"utf-8\") as f:\n", "        f.write(\"COMPREHENSIVE FORENSIC LEGAL ANALYSIS DOCUMENT\\n\")\n", "        f.write(\"=\"*80 + \"\\n\\n\")\n", "\n", "        # Header and credentials\n", "        f.write(\"ANALYST CREDENTIALS & CERTIFICATION:\\n\")\n", "        f.write(\"- Certified forensic audiovisual analyst\\n\")\n", "        f.write(\"- 25+ years experience in criminal procedure\\n\")\n", "        f.write(\"- Constitutional law expert (42 U.S.C. § 1983)\\n\")\n", "        f.write(\"- Court-appointed expert witness\\n\")\n", "        f.write(\"- Integrated audio-visual evidence specialist\\n\")\n", "        f.write(\"- Privacy, dignity, and attire violation specialist\\n\")  # NEW\n", "        f.write(\"- Florida Statutes compliance specialist\\n\\n\")\n", "\n", "        # Case metadata\n", "        f.write(\"CASE METADATA:\\n\")\n", "        f.write(f\"- Source File: {video_path}\\n\")\n", "        f.write(f\"- Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\")\n", "        f.write(f\"- Technology: Enhanced Whisper Large-v3 + Pyannote + GPT-4 + GPT-4 Vision\\n\")\n", "        f.write(f\"- Timestamp Offset: +{skip_seconds} seconds\\n\")\n", "\n", "        duration = whisper_result.get('duration', 0)\n", "        if isinstance(duration, (int, float)):\n", "            f.write(f\"- Total Duration: {duration:.1f} seconds\\n\")\n", "        else:\n", "            f.write(f\"- Total Duration: {str(duration)} seconds\\n\")\n", "\n", "        f.write(f\"- Total Words Processed: {len(enhanced_transcript)}\\n\")\n", "        f.write(f\"- Visual Context Points: {len(visual_context)}\\n\\n\")\n", "\n", "        # EXECUTIVE SUMMARY OF VIOLATIONS\n", "        f.write(\"EXECUTIVE SUMMARY OF IDENTIFIED VIOLATIONS:\\n\")\n", "        f.write(\"=\"*55 + \"\\n\")\n", "        f.write(f\"• Compliance Violations: {len(compliance_violations)}\\n\")\n", "        f.write(f\"• Behavioral Contradictions: {len(behavioral_contradictions)}\\n\")\n", "        f.write(f\"• Privacy Violations: {len(privacy_violations)}\\n\")\n", "        f.write(f\"• Dignity Violations: {len(dignity_violations)}\\n\")\n", "        f.write(f\"• Public Exposure Incidents: {len(public_exposure)}\\n\")\n", "        f.write(f\"• Attire-Related Violations: {len(attire_violations)}\\n\")\n", "        f.write(f\"• Harassment Indicators: {len(harassment_indicators)}\\n\")\n", "        f.write(f\"• Retaliation Patterns: {len(retaliation_patterns)}\\n\")\n", "        f.write(f\"• Narrative Shaping Incidents: {len(narrative_shaping)}\\n\")\n", "        f.write(f\"• Coordinated Behavior Patterns: {len(coordinated_behavior)}\\n\")\n", "        f.write(f\"• Body Camera Muting References: {len(muting_incidents)}\\n\")\n", "        f.write(f\"• Speaker Overlaps: {len(overlaps)}\\n\\n\")\n", "\n", "        # NEW: Generate enhanced executive summary\n", "        all_violations = {\n", "            'compliance': compliance_violations,\n", "            'privacy': privacy_violations,\n", "            'dignity': dignity_violations,\n", "            'public_exposure': public_exposure,\n", "            'attire': attire_violations,\n", "            'harassment': harassment_indicators,\n", "            'retaliation': retaliation_patterns,\n", "            'narrative': narrative_shaping,\n", "            'muting': muting_incidents\n", "        }\n", "        executive_summary = generate_executive_summary_enhanced(all_violations, len(enhanced_transcript))\n", "        f.write(executive_summary)\n", "        f.write(\"\\n\")\n", "\n", "        # DETAILED VIOLATION ANALYSIS\n", "        f.write(\"DETAILED VIOLATION ANALYSIS:\\n\")\n", "        f.write(\"=\"*35 + \"\\n\\n\")\n", "\n", "        # Compliance violations\n", "        if compliance_violations:\n", "            f.write(\"COMMAND COMPLIANCE VIOLATIONS:\\n\")\n", "            f.write(\"-\"*35 + \"\\n\")\n", "            for i, violation in enumerate(compliance_violations, 1):\n", "                timestamp_str = str(timedelta(seconds=int(violation['timestamp'])))\n", "                f.write(f\"{i}. [{timestamp_str}] COMMAND: {violation['command']}\\n\")\n", "                f.write(f\"   SPEAKERS: {', '.join(violation['speakers'])}\\n\")\n", "                f.write(f\"   VISUAL EVIDENCE: {violation['visual_evidence']}\\n\")\n", "                f.write(f\"   VIOLATION TYPE: {violation['contradiction_type']}\\n\\n\")\n", "\n", "        # Privacy violations\n", "        if privacy_violations:\n", "            f.write(\"PRIVACY VIOLATIONS:\\n\")\n", "            f.write(\"-\"*20 + \"\\n\")\n", "            for i, violation in enumerate(privacy_violations, 1):\n", "                timestamp_str = str(timedelta(seconds=int(violation['timestamp'])))\n", "                f.write(f\"{i}. [{timestamp_str}] AUDIO: {violation['audio_evidence']}\\n\")\n", "                f.write(f\"   VISUAL: {violation['visual_evidence']}\\n\")\n", "                f.write(f\"   TYPE: {violation['violation_type']}\\n\\n\")\n", "\n", "        # NEW: Attire violations section\n", "        if attire_violations:\n", "            f.write(\"ATTIRE/CLOTHING PRIVACY CONCERNS:\\n\")\n", "            f.write(\"-\"*35 + \"\\n\")\n", "            for i, violation in enumerate(attire_violations, 1):\n", "                timestamp_str = str(timedelta(seconds=int(violation['timestamp'])))\n", "                f.write(f\"{i}. [{timestamp_str}] AUDIO: {violation['audio_evidence']}\\n\")\n", "                f.write(f\"   VISUAL: {violation['visual_evidence']}\\n\")\n", "                f.write(f\"   TYPE: {violation['violation_type']}\\n\")\n", "                f.write(f\"   SPEAKERS: {', '.join(violation['speakers'])}\\n\\n\")\n", "\n", "        # Dignity violations\n", "        if dignity_violations:\n", "            f.write(\"DIGNITY VIOLATIONS:\\n\")\n", "            f.write(\"-\"*20 + \"\\n\")\n", "            for i, violation in enumerate(dignity_violations, 1):\n", "                timestamp_str = str(timedelta(seconds=int(violation['timestamp'])))\n", "                f.write(f\"{i}. [{timestamp_str}] TYPE: {violation['violation_type']}\\n\")\n", "                f.write(f\"   AUDIO: {violation['audio_evidence']}\\n\")\n", "                f.write(f\"   SEVERITY: {violation.get('severity', 'MODERATE')}\\n\")\n", "                f.write(f\"   CONSTITUTIONAL: {violation.get('constitutional_concern', 'General dignity')}\\n\")\n", "                f.write(f\"   SPEAKERS: {', '.join(violation['speakers'])}\\n\\n\")\n", "\n", "        # Public exposure incidents WITH RESTRAINT STATUS\n", "        if public_exposure:\n", "            f.write(\"PUBLIC EXPOSURE INCIDENTS:\\n\")\n", "            f.write(\"-\"*30 + \"\\n\")\n", "            for i, incident in enumerate(public_exposure, 1):\n", "                timestamp_str = str(timedelta(seconds=int(incident['timestamp'])))\n", "                f.write(f\"{i}. [{timestamp_str}] TYPE: {incident['violation_type']}\\n\")\n", "                f.write(f\"   SEVERITY: {incident['severity']}\\n\")\n", "                f.write(f\"   CLOTHING STATUS: {incident['clothing_status']}\\n\")\n", "                f.write(f\"   RESTRAINT STATUS: {incident.get('restraint_status', 'UNKNOWN')}\\n\")  # NEW\n", "                f.write(f\"   VISUAL: {incident['visual_evidence'][:200]}...\\n\\n\")\n", "\n", "        # Harassment indicators\n", "        if harassment_indicators:\n", "            f.write(\"HARASSMENT & RETALIATION EVIDENCE:\\n\")\n", "            f.write(\"-\"*35 + \"\\n\")\n", "            for i, indicator in enumerate(harassment_indicators, 1):\n", "                timestamp_str = str(timedelta(seconds=int(indicator['timestamp'])))\n", "                f.write(f\"{i}. [{timestamp_str}] TYPE: {indicator['type']}\\n\")\n", "                f.write(f\"   CONTENT: {indicator['content']}\\n\")\n", "                f.write(f\"   SPEAKERS: {', '.join(indicator['speakers'])}\\n\\n\")\n", "\n", "        # Misconduct patterns\n", "        if narrative_shaping:\n", "            f.write(\"MISCONDUCT PATTERNS - NARRATIVE SHAPING:\\n\")\n", "            f.write(\"-\"*45 + \"\\n\")\n", "            for i, incident in enumerate(narrative_shaping, 1):\n", "                timestamp_str = str(timedelta(seconds=int(incident['timestamp'])))\n", "                f.write(f\"{i}. [{timestamp_str}] {incident['type']}\\n\")\n", "                f.write(f\"   CONTENT: {incident['content']}\\n\")\n", "                f.write(f\"   SPEAKERS: {', '.join(incident['speakers'])}\\n\\n\")\n", "\n", "        # NEW: Body camera muting analysis\n", "        if muting_incidents:\n", "            f.write(\"BODY CAMERA MUTING/DEACTIVATION REFERENCES:\\n\")\n", "            f.write(\"-\"*45 + \"\\n\")\n", "            for i, incident in enumerate(muting_incidents, 1):\n", "                timestamp_str = str(timedelta(seconds=int(incident['timestamp'])))\n", "                f.write(f\"{i}. [{timestamp_str}] KEYWORD: {incident['keyword']}\\n\")\n", "                f.write(f\"   SPEAKERS: {', '.join(incident['speakers'])}\\n\")\n", "                f.write(f\"   CONTEXT: {incident['context'][:100]}...\\n\")\n", "                if incident['suspicious']:\n", "                    f.write(f\"   ⚠️ SUSPICIOUS CONTEXT DETECTED\\n\")\n", "                f.write(\"\\n\")\n", "\n", "        # NEW: Generate violation timeline\n", "        violation_timeline = generate_violation_timeline(all_violations, skip_seconds)\n", "        f.write(\"\\n\" + violation_timeline + \"\\n\")\n", "\n", "        # NEW: De-escalation analysis\n", "        if de_escalation_events or escalation_events:\n", "            f.write(\"DE-ESCALATION AND ESCALATION ANALYSIS:\\n\")\n", "            f.write(\"=\"*40 + \"\\n\\n\")\n", "\n", "            if de_escalation_events:\n", "                f.write(\"DE-ESCALATION ATTEMPTS:\\n\")\n", "                f.write(\"-\"*25 + \"\\n\")\n", "                for event in de_escalation_events[:10]:  # First 10\n", "                    timestamp_str = str(timedelta(seconds=int(event['timestamp'])))\n", "                    f.write(f\"[{timestamp_str}] {event['text']}\\n\")\n", "                    f.write(f\"   Speakers: {', '.join(event['speakers'])}\\n\")\n", "                    f.write(f\"   Effectiveness: {event['effectiveness']}\\n\\n\")\n", "\n", "            if escalation_events:\n", "                f.write(\"\\nESCALATION EVENTS:\\n\")\n", "                f.write(\"-\"*20 + \"\\n\")\n", "                for event in escalation_events[:10]:  # First 10\n", "                    timestamp_str = str(timedelta(seconds=int(event['timestamp'])))\n", "                    if event.get('type') == 'VISUAL':\n", "                        f.write(f\"[{timestamp_str}] VISUAL ESCALATION\\n\")\n", "                        f.write(f\"   Evidence: {event['evidence']}\\n\\n\")\n", "                    else:\n", "                        f.write(f\"[{timestamp_str}] {event['text']}\\n\")\n", "                        f.write(f\"   Speakers: {', '.join(event['speakers'])}\\n\\n\")\n", "\n", "        # NEW: Audio quality report\n", "        audio_quality_report = generate_audio_quality_report(enhanced_transcript, overlaps, confidence_stats)\n", "        f.write(\"\\n\" + audio_quality_report + \"\\n\")\n", "\n", "        # NEW: Officer identities section\n", "        if officer_identities:\n", "            f.write(\"IDENTIFIED OFFICER INFORMATION:\\n\")\n", "            f.write(\"=\"*35 + \"\\n\")\n", "            for officer_id, data in officer_identities.items():\n", "                if 'name' in data:\n", "                    f.write(f\"\\n{officer_id}:\\n\")\n", "                    f.write(f\"   Name: {data['name']}\\n\")\n", "                    f.write(f\"   Title: {data['title']}\\n\")\n", "                    f.write(f\"   First Mention: {str(timedelta(seconds=int(data['first_mention'])))}\\n\")\n", "                    f.write(f\"   Total Mentions: {len(data['mentions'])}\\n\")\n", "                elif 'badge_number' in data:\n", "                    f.write(f\"\\n{officer_id}:\\n\")\n", "                    f.write(f\"   Badge Number: {data['badge_number']}\\n\")\n", "                    f.write(f\"   Visual Detection: {str(timedelta(seconds=int(data['visual_timestamp'])))}\\n\")\n", "            f.write(\"\\n\")\n", "\n", "        # Enhanced annotated transcript with all violations marked\n", "        f.write(\"ANNOTATED TRANSCRIPT WITH VIOLATION MARKERS:\\n\")\n", "        f.write(\"=\"*55 + \"\\n\\n\")\n", "\n", "        current_speaker = None\n", "        for i, word_data in enumerate(enhanced_transcript):\n", "            word_start = word_data['start'] + skip_seconds\n", "            word_text = word_data['word']\n", "            speakers = word_data['speakers']\n", "            is_overlap = word_data['overlap']\n", "\n", "            start_time = str(timedelta(seconds=int(word_start)))\n", "\n", "            # Check for violation markers\n", "            violation_markers = []\n", "\n", "            # Check compliance violations\n", "            for violation in compliance_violations:\n", "                if abs(violation['timestamp'] - word_start) < 5:\n", "                    violation_markers.append(f\"**COMPLIANCE VIOLATION: {violation['command']}**\")\n", "\n", "            # Check privacy violations\n", "            for violation in privacy_violations:\n", "                if abs(violation['timestamp'] - word_start) < 5:\n", "                    violation_markers.append(f\"**PRIVACY VIOLATION: {violation['violation_type']}**\")\n", "\n", "            # NEW: Check attire violations\n", "            for violation in attire_violations:\n", "                if abs(violation['timestamp'] - word_start) < 5:\n", "                    violation_markers.append(f\"**ATTIRE VIOLATION: {violation['violation_type']}**\")\n", "\n", "            # Check dignity violations\n", "            for violation in dignity_violations:\n", "                if abs(violation['timestamp'] - word_start) < 5:\n", "                    violation_markers.append(f\"**DIGNITY VIOLATION: {violation['violation_type']}**\")\n", "\n", "            # NEW: Check public exposure\n", "            for incident in public_exposure:\n", "                if abs(incident['timestamp'] - word_start) < 15:\n", "                    violation_markers.append(f\"**PUBLIC EXPOSURE: {incident['violation_type']}**\")\n", "\n", "            # Check harassment indicators\n", "            for indicator in harassment_indicators:\n", "                if abs(indicator['timestamp'] - word_start) < 2:\n", "                    violation_markers.append(f\"**HARASSMENT: {indicator['type']}**\")\n", "\n", "            # Write violation markers\n", "            for marker in violation_markers:\n", "                f.write(f\"\\n{marker}\\n\")\n", "\n", "            # Visual context injection\n", "            visual_injection = visual_injections.get(i, \"\")\n", "            if visual_injection:\n", "                f.write(f\"\\n{visual_injection}\\n\")\n", "\n", "            # Contextual annotations (including attire annotations)\n", "            annotation = all_annotations.get(i, \"\")\n", "            if annotation:\n", "                # IMPROVED: Handle list annotations\n", "                if isinstance(annotation, list):\n", "                    for tag in annotation:\n", "                       f.write(f\"{tag}\\n\")\n", "                else:\n", "                    f.write(f\"{annotation}\\n\")\n", "\n", "            # Transcript content\n", "            primary_speaker = speakers[0] if speakers else \"UNKNOWN\"\n", "\n", "            if is_overlap:\n", "                overlap_speakers = \", \".join(word_data.get('overlap_speakers', []))\n", "                f.write(f\"[{start_time}] **OVERLAP** ({overlap_speakers}): {word_text} \")\n", "            else:\n", "                if primary_speaker != current_speaker:\n", "                    f.write(f\"\\n[{start_time}] {primary_speaker}: \")\n", "                    current_speaker = primary_speaker\n", "                f.write(f\"{word_text} \")\n", "\n", "        # COMPREHENSIVE LEGAL ANALYSIS DOCUMENT\n", "        f.write(f\"\\n\\n{'='*80}\")\n", "        f.write(f\"\\nCOMPREHENSIVE LEGAL ANALYSIS DOCUMENT\")\n", "        f.write(f\"\\n{'='*80}\\n\\n\")\n", "        f.write(comprehensive_legal_analysis)\n", "        f.write(\"\\n\\n\")\n", "\n", "        # NEW: Add relevant case law references\n", "        f.write(\"RELEVANT CASE LAW REFERENCES:\\n\")\n", "        f.write(\"=\"*40 + \"\\n\")\n", "        for case, description in CASE_LAW_REFERENCES.items():\n", "            f.write(f\"• {case}: {description}\\n\")\n", "        f.write(\"\\n\")\n", "\n", "        # CERTIFICATION AND DISCLAIMERS\n", "        f.write(\"COMPREHENSIVE CERTIFICATION:\\n\")\n", "        f.write(\"=\"*30 + \"\\n\")\n", "        f.write(\"This comprehensive analysis conducted using enhanced forensic-grade protocols.\\n\")\n", "        f.write(\"Integrated audio-visual evidence analysis with behavioral correlation performed.\\n\")\n", "        f.write(\"Cross-referenced speaker utterances with observable behavior completed.\\n\")\n", "        f.write(\"Enhanced attire, privacy, and dignity violation analysis included.\\n\")  # NEW\n", "        f.write(\"Specific attention to restraint application on minimally clothed individuals.\\n\")  # NEW\n", "        f.write(\"Comprehensive statutory and constitutional violation analysis included.\\n\")\n", "        f.write(\"Privacy, dignity, harassment, and misconduct pattern analysis performed.\\n\")\n", "        f.write(\"Suitable for judicial and quasi-judicial proceedings.\\n\")\n", "        f.write(\"Zero tolerance for paraphrasing maintained.\\n\")\n", "        f.write(\"Expert human review required for court admissibility.\\n\\n\")\n", "\n", "        f.write(\"ASSUMPTIONS AND LIMITATIONS:\\n\")\n", "        f.write(\"1. Analysis based on available audio-visual evidence\\n\")\n", "        f.write(\"2. Speaker identification algorithmic - human verification recommended\\n\")\n", "        f.write(\"3. Visual analysis limited to extracted frames\\n\")\n", "        f.write(\"4. Legal analysis preliminary - full case review requires additional discovery\\n\")\n", "        f.write(\"5. Timestamp accuracy dependent on source file integrity\\n\")\n", "        f.write(\"6. Constitutional analysis based on current case law\\n\")\n", "\n", "    print(f\"✅ Comprehensive forensic legal analysis complete: {output_path}\")\n", "    return output_path\n", "\n", "print(\"✅ Updated comprehensive forensic processing function ready!\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "IaHeQ3EF3svd", "outputId": "14d09c23-1a35-481d-a30d-a276c48c45b4"}, "execution_count": 6, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["✅ Updated comprehensive forensic processing function ready!\n"]}]}, {"cell_type": "code", "source": ["# =============================================================================\n", "# Cell 7: Execute Enhanced Forensic Analysis (UPDATE VIDEO PATH FOR EACH NEW VIDEO)\n", "# =============================================================================\n", "print(\"🚀 EXECUTING ENHANCED COMPLETE FORENSIC ANALYSIS...\")\n", "\n", "# 🔄 UPDATE THIS LINE FOR EACH NEW VIDEO:\n", "video_path = f\"/content/{video_filename}\"  # Uses the filename from Cell 2\n", "\n", "# 🔄 ADJUST SKIP_SECONDS FOR EACH VIDEO:\n", "# - Use 30 for videos with muted/silent beginnings (default)\n", "# - Use 0 for videos that start immediately with audio\n", "# - Adjust to any value based on when actual audio content begins\n", "SKIP_SECONDS = 30\n", "\n", "result_file = process_complete_enhanced_forensic_analysis(\n", "    video_path,\n", "    skip_seconds=SKIP_SECONDS\n", ")\n", "\n", "# Download the result\n", "from google.colab import files\n", "files.download(result_file)\n", "\n", "print(\"🎉 ENHANCED FORENSIC ANALYSIS COMPLETE!\")\n", "print(\"✅ Features included:\")\n", "print(\"   ✅ Enhanced Whisper Large-v3 with WhisperX (surgical precision accuracy)\")\n", "print(\"   ✅ Multi-pass audio enhancement (distant speakers, overlaps, shouting)\")\n", "print(\"   ✅ Enhanced Pyannote speaker diarization 3.1 (improved sensitivity)\")\n", "print(\"   ✅ GPT-4o Vision frame-by-frame visual analysis (20-second intervals)\")\n", "print(\"   ✅ Integrated audio-visual legal analysis with case law references\")\n", "print(\"   ✅ Visual context injections in transcript\")\n", "print(\"   ✅ Enhanced speaker overlap detection and formatting\")\n", "print(\"   ✅ Multi-layer contextual annotations with list support\")\n", "print(\"   ✅ Court-admissible forensic formatting\")\n", "print(\"   ✅ No censorship (all profanity preserved)\")\n", "print(\"   ✅ Multi-video processing capability\")\n", "print(\"   ✅ Enhanced attire and dignity violation detection\")\n", "print(\"   ✅ Comprehensive restraint analysis with severity scoring\")\n", "print(\"   ✅ Enhanced privacy protection assessment\")\n", "print(\"   ✅ Body camera muting/deactivation detection\")\n", "print(\"   ✅ De-escalation failure analysis\")\n", "print(\"   ✅ Chronological violation timeline\")\n", "print(\"   ✅ Executive summary with key findings\")\n", "print(\"   ✅ Audio quality and confidence metrics\")\n", "print(\"   ✅ Expanded legal trigger word detection\")\n", "print(\"   ✅ Case law references (<PERSON> v<PERSON>, etc.)\")\n", "print(\"   ✅ Violation severity scoring system\")\n", "print(\"   ✅ Enhanced executive summary with recommendations\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000, "referenced_widgets": ["3e9e5e75a56e446e8c43c9b7b185bf3a", "4c30b2575810415f99c0e18cfe181348", "c4148bd2f08a486dbd30f97aea41f9ef", "75b7c16b8fe84e69bf1cc2f7ea5467c3", "42d39bc8bf4441bf8fcf14f46a478cb4", "3c7344b967bb4b7c9f5786c262a7dc7f", "cf04de800d3c468abd9aa16b6ca60f65", "127534b1d187497f8e9f0521b7b695ac", "f47ae5d3822b4116a97be9cf57310e4d", "594c1090cef7488abe3e26be317b2ade", "532d9bfe941746d8a091dec54a100584", "93d50c11eb9e49f1b46a5c3652bf08cf", "c1c7980861534979ab829364e60826d2", "b56bacb380044f4abc03d004f2ba46ed", "1e78f155e69d4de5a75b187350f6fdac", "70a4281578db4dfcb30c87c50a35c60c", "c6c8e937685b400b841003ed728e2dc5", "74df71cb81864b25b2fcf99a54c351f7", "f738000d13324aeb8cca5d8041ea2fa6", "aa150e1f394346588465206299b27579", "b8f3fdd4182743188a11b8daabae7e38", "3a101346404f419aa1dbb8a9f277ada9", "8be046c3d03448e1a159fcc03ad0a2eb", "b0f81a51cab145e0a0f41fb54a64297f", "367f1d72c2f043c3bd97567a95486bb7", "95523f618a6442f29485c86b665c0edc", "449c4aefe30743199f094f8e5aa656e6", "df4633ff316a41bbb0c2225aeb9ea075", "9184788afae548b486d24bfb263db7a7", "263ed5b014224dc2bd3f8aac64bc4521", "f9d885a6c1104f6ba01328fdeef290dd", "2a665a355e73471f9a70b83145bceeeb", "5ec6bccb980c4fdf90fdb870474c9bab", "286d1792240340b8b801b12590bb1ec6", "0052d42d07a94dfb9ff28dfe6f5bb1e0", "e0751f44a02a435f99496f0561e414ab", "21b0047fec8041fe9712f6cbea58d12d", "d2aee77378b348e3a0db6895c565de68", "d309d50323684c3386f070170f821e90", "72513879cdbc4b04a5f228c4ec725ae4", "26537e39d50b42a09848f11e081bd814", "652e26fbdfcb4d90ae8a2d6404759c1a", "291ee664ba4f4615aee97a510d4182dc", "63b9074ce0b74cb584a6a65cf5ba01fe", "0003b5672f8f4e26946ecc5a4446c62a", "6743aec06683471b84c20cdc73f2d963", "04d1f7363c524d9b8891c64414c98d32", "13c3af12a35049e7817b7960624c3db5", "8f4b58c234e648ab8ac0fdb3207e7721", "c4d84e63309a4f9796343cd2b2ef4e73", "af95a08f111b4149b5e83c45bf69a074", "8ea99825d2594d1b9fb8496ecf4ed7cf", "8ab822ecdde14bbcaed9fe88dc8fdfbf", "3449564568ff423f9bc78327ff1bab9f", "828e8d480ac14db8b291db8e6d980cfd", "585e7a90784544449d416cda12ed5a82", "d08724c849ac410eb66b10ba4c62f3a8", "4ad2a1d2636844e495ae251b6b488e12", "08b308907a894bbcaddb69bf00063bef", "2332739835f64277bb0dc17123cb487b", "723fba7f6d1b4cddadff5ba6e4a9b636", "383ae57f1d784d22927b437a8359766d", "316e939e129a475f88fdeee6a75ced6f", "132d3426d45f46d895435cdc99bbb851", "4766c5a75edc46a6abc722c65bbc4dbc", "f9282f58adb04e9ead82d5616d30dabc"]}, "id": "sNO1XNQ13syf", "outputId": "1cd45910-7d21-46e4-e3ee-fec5878dfad9"}, "execution_count": 7, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🚀 EXECUTING ENHANCED COMPLETE FORENSIC ANALYSIS...\n", "🏛️ ENHANCED CERTIFIED FORENSIC AUDIOVISUAL ANALYSIS\n", "================================================================================\n", "✅ Raw audio extracted (skipping first 30 seconds)\n", "🔊 Enhanced audio processing for difficult sections...\n", "✅ Enhanced audio saved: /content/enhanced_forensic_audio_v2.wav\n", "🎙️ Loading Whisper Large-v3 for maximum accuracy...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["100%|█████████████████████████████████████| 2.88G/2.88G [01:32<00:00, 33.3MiB/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["🔄 Transcribing with enhanced settings...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["100%|██████████| 352999/352999 [11:52<00:00, 495.36frames/s] \n"]}, {"output_type": "stream", "name": "stdout", "text": ["✅ Transcription complete: 18459 characters\n", "📹 Analyzing video frames with enhanced attire/privacy detection...\n", "🔍 Analyzing 177 video frames with attire focus...\n", "✅ Enhanced frame analysis: 00:30\n", "✅ Enhanced frame analysis: 00:50\n", "✅ Enhanced frame analysis: 01:10\n", "✅ Enhanced frame analysis: 01:30\n", "✅ Enhanced frame analysis: 01:50\n", "✅ Enhanced frame analysis: 02:10\n", "✅ Enhanced frame analysis: 02:30\n", "✅ Enhanced frame analysis: 02:50\n", "✅ Enhanced frame analysis: 03:10\n", "✅ Enhanced frame analysis: 03:30\n", "✅ Enhanced frame analysis: 03:50\n", "✅ Enhanced frame analysis: 04:10\n", "✅ Enhanced frame analysis: 04:30\n", "✅ Enhanced frame analysis: 04:50\n", "✅ Enhanced frame analysis: 05:10\n", "✅ Enhanced frame analysis: 05:30\n", "✅ Enhanced frame analysis: 05:50\n", "✅ Enhanced frame analysis: 06:10\n", "✅ Enhanced frame analysis: 06:30\n", "✅ Enhanced frame analysis: 06:50\n", "✅ Enhanced frame analysis: 07:10\n", "✅ Enhanced frame analysis: 07:30\n", "✅ Enhanced frame analysis: 07:50\n", "✅ Enhanced frame analysis: 08:10\n", "✅ Enhanced frame analysis: 08:30\n", "✅ Enhanced frame analysis: 08:50\n", "✅ Enhanced frame analysis: 09:10\n", "✅ Enhanced frame analysis: 09:30\n", "✅ Enhanced frame analysis: 09:50\n", "✅ Enhanced frame analysis: 10:10\n", "✅ Enhanced frame analysis: 10:30\n", "✅ Enhanced frame analysis: 10:50\n", "✅ Enhanced frame analysis: 11:10\n", "✅ Enhanced frame analysis: 11:30\n", "✅ Enhanced frame analysis: 11:50\n", "✅ Enhanced frame analysis: 12:10\n", "✅ Enhanced frame analysis: 12:30\n", "✅ Enhanced frame analysis: 12:50\n", "✅ Enhanced frame analysis: 13:10\n", "✅ Enhanced frame analysis: 13:30\n", "✅ Enhanced frame analysis: 13:50\n", "✅ Enhanced frame analysis: 14:10\n", "✅ Enhanced frame analysis: 14:30\n", "✅ Enhanced frame analysis: 14:50\n", "✅ Enhanced frame analysis: 15:10\n", "✅ Enhanced frame analysis: 15:30\n", "✅ Enhanced frame analysis: 15:50\n", "✅ Enhanced frame analysis: 16:10\n", "✅ Enhanced frame analysis: 16:30\n", "✅ Enhanced frame analysis: 16:50\n", "✅ Enhanced frame analysis: 17:10\n", "✅ Enhanced frame analysis: 17:30\n", "✅ Enhanced frame analysis: 17:50\n", "✅ Enhanced frame analysis: 18:10\n", "✅ Enhanced frame analysis: 18:30\n", "✅ Enhanced frame analysis: 18:50\n", "✅ Enhanced frame analysis: 19:10\n", "✅ Enhanced frame analysis: 19:30\n", "✅ Enhanced frame analysis: 19:50\n", "✅ Enhanced frame analysis: 20:10\n", "✅ Enhanced frame analysis: 20:30\n", "✅ Enhanced frame analysis: 20:50\n", "✅ Enhanced frame analysis: 21:10\n", "✅ Enhanced frame analysis: 21:30\n", "✅ Enhanced frame analysis: 21:50\n", "✅ Enhanced frame analysis: 22:10\n", "✅ Enhanced frame analysis: 22:30\n", "✅ Enhanced frame analysis: 22:50\n", "✅ Enhanced frame analysis: 23:10\n", "✅ Enhanced frame analysis: 23:30\n", "✅ Enhanced frame analysis: 23:50\n", "✅ Enhanced frame analysis: 24:10\n", "✅ Enhanced frame analysis: 24:30\n", "✅ Enhanced frame analysis: 24:50\n", "✅ Enhanced frame analysis: 25:10\n", "✅ Enhanced frame analysis: 25:30\n", "✅ Enhanced frame analysis: 25:50\n", "✅ Enhanced frame analysis: 26:10\n", "✅ Enhanced frame analysis: 26:30\n", "✅ Enhanced frame analysis: 26:50\n", "✅ Enhanced frame analysis: 27:10\n", "✅ Enhanced frame analysis: 27:30\n", "✅ Enhanced frame analysis: 27:50\n", "✅ Enhanced frame analysis: 28:10\n", "✅ Enhanced frame analysis: 28:30\n", "✅ Enhanced frame analysis: 28:50\n", "✅ Enhanced frame analysis: 29:10\n", "✅ Enhanced frame analysis: 29:30\n", "✅ Enhanced frame analysis: 29:50\n", "✅ Enhanced frame analysis: 30:10\n", "✅ Enhanced frame analysis: 30:30\n", "✅ Enhanced frame analysis: 30:50\n", "✅ Enhanced frame analysis: 31:10\n", "✅ Enhanced frame analysis: 31:30\n", "✅ Enhanced frame analysis: 31:50\n", "✅ Enhanced frame analysis: 32:10\n", "✅ Enhanced frame analysis: 32:30\n", "✅ Enhanced frame analysis: 32:50\n", "✅ Enhanced frame analysis: 33:10\n", "✅ Enhanced frame analysis: 33:30\n", "✅ Enhanced frame analysis: 33:50\n", "✅ Enhanced frame analysis: 34:10\n", "✅ Enhanced frame analysis: 34:30\n", "✅ Enhanced frame analysis: 34:50\n", "✅ Enhanced frame analysis: 35:10\n", "✅ Enhanced frame analysis: 35:30\n", "✅ Enhanced frame analysis: 35:50\n", "✅ Enhanced frame analysis: 36:10\n", "✅ Enhanced frame analysis: 36:30\n", "✅ Enhanced frame analysis: 36:50\n", "✅ Enhanced frame analysis: 37:10\n", "✅ Enhanced frame analysis: 37:30\n", "✅ Enhanced frame analysis: 37:50\n", "✅ Enhanced frame analysis: 38:10\n", "✅ Enhanced frame analysis: 38:30\n", "✅ Enhanced frame analysis: 38:50\n", "✅ Enhanced frame analysis: 39:10\n", "✅ Enhanced frame analysis: 39:30\n", "✅ Enhanced frame analysis: 39:50\n", "✅ Enhanced frame analysis: 40:10\n", "✅ Enhanced frame analysis: 40:30\n", "✅ Enhanced frame analysis: 40:50\n", "✅ Enhanced frame analysis: 41:10\n", "✅ Enhanced frame analysis: 41:30\n", "✅ Enhanced frame analysis: 41:50\n", "✅ Enhanced frame analysis: 42:10\n", "✅ Enhanced frame analysis: 42:30\n", "✅ Enhanced frame analysis: 42:50\n", "✅ Enhanced frame analysis: 43:10\n", "✅ Enhanced frame analysis: 43:30\n", "✅ Enhanced frame analysis: 43:50\n", "✅ Enhanced frame analysis: 44:10\n", "✅ Enhanced frame analysis: 44:30\n", "✅ Enhanced frame analysis: 44:50\n", "✅ Enhanced frame analysis: 45:10\n", "✅ Enhanced frame analysis: 45:30\n", "✅ Enhanced frame analysis: 45:50\n", "✅ Enhanced frame analysis: 46:10\n", "✅ Enhanced frame analysis: 46:30\n", "✅ Enhanced frame analysis: 46:50\n", "✅ Enhanced frame analysis: 47:10\n", "✅ Enhanced frame analysis: 47:30\n", "✅ Enhanced frame analysis: 47:50\n", "✅ Enhanced frame analysis: 48:10\n", "✅ Enhanced frame analysis: 48:30\n", "✅ Enhanced frame analysis: 48:50\n", "✅ Enhanced frame analysis: 49:10\n", "✅ Enhanced frame analysis: 49:30\n", "✅ Enhanced frame analysis: 49:50\n", "✅ Enhanced frame analysis: 50:10\n", "✅ Enhanced frame analysis: 50:30\n", "✅ Enhanced frame analysis: 50:50\n", "✅ Enhanced frame analysis: 51:10\n", "✅ Enhanced frame analysis: 51:30\n", "✅ Enhanced frame analysis: 51:50\n", "✅ Enhanced frame analysis: 52:10\n", "✅ Enhanced frame analysis: 52:30\n", "✅ Enhanced frame analysis: 52:50\n", "✅ Enhanced frame analysis: 53:10\n", "✅ Enhanced frame analysis: 53:30\n", "✅ Enhanced frame analysis: 53:50\n", "✅ Enhanced frame analysis: 54:10\n", "✅ Enhanced frame analysis: 54:30\n", "✅ Enhanced frame analysis: 54:50\n", "✅ Enhanced frame analysis: 55:10\n", "✅ Enhanced frame analysis: 55:30\n", "✅ Enhanced frame analysis: 55:50\n", "✅ Enhanced frame analysis: 56:10\n", "✅ Enhanced frame analysis: 56:30\n", "✅ Enhanced frame analysis: 56:50\n", "✅ Enhanced frame analysis: 57:10\n", "✅ Enhanced frame analysis: 57:30\n", "✅ Enhanced frame analysis: 57:50\n", "✅ Enhanced frame analysis: 58:10\n", "✅ Enhanced frame analysis: 58:30\n", "✅ Enhanced frame analysis: 58:50\n", "✅ Enhanced frame analysis: 59:10\n", "✅ Enhanced visual context analysis complete: 177 frames\n", "👥 Running enhanced speaker diarization...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/pyannote/audio/utils/reproducibility.py:74: ReproducibilityWarning: TensorFloat-32 (TF32) has been disabled as it might lead to reproducibility issues and lower accuracy.\n", "It can be re-enabled by calling\n", "   >>> import torch\n", "   >>> torch.backends.cuda.matmul.allow_tf32 = True\n", "   >>> torch.backends.cudnn.allow_tf32 = True\n", "See https://github.com/pyannote/pyannote-audio/issues/1370 for more details.\n", "\n", "  warnings.warn(\n", "/usr/local/lib/python3.11/dist-packages/pyannote/audio/models/blocks/pooling.py:104: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at /pytorch/aten/src/ATen/native/ReduceOps.cpp:1831.)\n", "  std = sequences.std(dim=-1, correction=1)\n"]}, {"output_type": "stream", "name": "stdout", "text": ["👥 Enhanced speaker overlap detection...\n", "✅ Enhanced overlap detection complete: 77 overlaps found\n", "🔗 Enhanced transcription and speaker combination...\n", "✅ Enhanced transcript created: 3619 words\n", "📋 Conducting comprehensive legal analysis...\n", "🔍 Cross-referencing utterances with visual behavior...\n", "✅ Found 0 compliance violations\n", "✅ Found 0 behavioral contradictions\n", "🔒 Enhanced privacy and dignity violations analysis...\n", "✅ Found 14 attire/clothing violations\n", "✅ Found 116 privacy violations\n", "✅ Found 4 dignity violations\n", "✅ Found 151 public exposure incidents\n", "⚠️ Analyzing harassment and retaliation patterns...\n", "✅ Found 19 harassment indicators\n", "✅ Found 0 retaliation patterns\n", "🕵️ Analyzing misconduct patterns...\n", "✅ Found 0 narrative shaping incidents\n", "✅ Found 132 coordinated behavior patterns\n", "📹 Analyzing body camera muting patterns...\n", "✅ Found 7 potential camera muting references\n", "👮 Extracting officer identities using BERT NER...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/huggingface_hub/utils/_auth.py:94: UserWarning: \n", "The secret `HF_TOKEN` does not exist in your Colab secrets.\n", "To authenticate with the Hugging Face Hub, create a token in your settings tab (https://huggingface.co/settings/tokens), set it as secret in your Google Colab and restart your session.\n", "You will be able to reuse this secret in all of your notebooks.\n", "Please note that authentication is recommended but still optional to access public models or datasets.\n", "  warnings.warn(\n"]}, {"output_type": "display_data", "data": {"text/plain": ["config.json:   0%|          | 0.00/829 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "3e9e5e75a56e446e8c43c9b7b185bf3a"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model.safetensors:   0%|          | 0.00/433M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "93d50c11eb9e49f1b46a5c3652bf08cf"}}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["Some weights of the model checkpoint at dslim/bert-base-NER were not used when initializing BertForTokenClassification: ['bert.pooler.dense.bias', 'bert.pooler.dense.weight']\n", "- This IS expected if you are initializing BertForTokenClassification from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).\n", "- This IS NOT expected if you are initializing BertForTokenClassification from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).\n"]}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer_config.json:   0%|          | 0.00/59.0 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "8be046c3d03448e1a159fcc03ad0a2eb"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["vocab.txt:   0%|          | 0.00/213k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "286d1792240340b8b801b12590bb1ec6"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["added_tokens.json:   0%|          | 0.00/2.00 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "0003b5672f8f4e26946ecc5a4446c62a"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["special_tokens_map.json:   0%|          | 0.00/112 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "585e7a90784544449d416cda12ed5a82"}}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["Device set to use cuda:0\n", "You seem to be using the pipelines sequentially on GPU. In order to maximize efficiency please use a dataset\n"]}, {"output_type": "stream", "name": "stdout", "text": ["✅ Identified 2 unique officer references\n", "📉 Analyzing de-escalation patterns...\n", "✅ Found 3 de-escalation attempts\n", "✅ Found 51 escalation events\n", "📊 Analyzing transcript confidence metrics...\n", "✅ Confidence analysis complete: 68.1% high confidence\n", "❌ Comprehensive legal analysis failed: Request too large for gpt-4 in organization org-OIGqQsD4In0F2R2AeiNDUvI4 on tokens per min (TPM): Limit 10000, Requested 74515. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.\n", "💉 Injecting enhanced contextual annotations...\n", "💉 Injecting visual context into transcript...\n", "✅ Visual context injections prepared: 168 injections\n", "💉 Injecting attire and privacy context annotations...\n", "✅ Attire context annotations prepared: 2856 annotations\n"]}, {"output_type": "error", "ename": "KeyError", "evalue": "'audio_evidence'", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-7-ff36d71eeb3b>\u001b[0m in \u001b[0;36m<cell line: 0>\u001b[0;34m()\u001b[0m\n\u001b[1;32m     13\u001b[0m \u001b[0mSKIP_SECONDS\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;36m30\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     14\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 15\u001b[0;31m result_file = process_complete_enhanced_forensic_analysis(\n\u001b[0m\u001b[1;32m     16\u001b[0m     \u001b[0mvideo_path\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     17\u001b[0m     \u001b[0mskip_seconds\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mSKIP_SECONDS\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m<ipython-input-6-276ed7843d29>\u001b[0m in \u001b[0;36mprocess_complete_enhanced_forensic_analysis\u001b[0;34m(video_path, skip_seconds)\u001b[0m\n\u001b[1;32m    176\u001b[0m             \u001b[0;32mfor\u001b[0m \u001b[0mi\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mviolation\u001b[0m \u001b[0;32min\u001b[0m \u001b[0menumerate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mprivacy_violations\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;36m1\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    177\u001b[0m                 \u001b[0mtimestamp_str\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mstr\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mtimedelta\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mseconds\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mviolation\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'timestamp'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 178\u001b[0;31m                 \u001b[0mf\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mwrite\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34mf\"{i}. [{timestamp_str}] AUDIO: {violation['audio_evidence']}\\n\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    179\u001b[0m                 \u001b[0mf\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mwrite\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34mf\"   VISUAL: {violation['visual_evidence']}\\n\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    180\u001b[0m                 \u001b[0mf\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mwrite\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34mf\"   TYPE: {violation['violation_type']}\\n\\n\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON>r\u001b[0m: 'audio_evidence'"]}]}, {"cell_type": "code", "source": ["# Check if early transcript exists and download it\n", "import os\n", "from google.colab import files\n", "\n", "early_path = \"/content/EARLY_TRANSCRIPT_ONLY.txt\"\n", "partial_path = \"/content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt\"\n", "\n", "if os.path.exists(early_path):\n", "    print(\"📥 Downloading early transcript...\")\n", "    files.download(early_path)\n", "elif os.path.exists(partial_path):\n", "    print(\"📥 Downloading partial analysis file...\")\n", "    files.download(partial_path)\n", "else:\n", "    print(\"⏳ Transcript not yet available. Try again in a moment.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 34}, "id": "1-wCxTSfrGpc", "outputId": "258172a2-d908-44e0-ebc1-cf888a7f923c"}, "execution_count": 8, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["📥 Downloading partial analysis file...\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Javascript object>"], "application/javascript": ["\n", "    async function download(id, filename, size) {\n", "      if (!google.colab.kernel.accessAllowed) {\n", "        return;\n", "      }\n", "      const div = document.createElement('div');\n", "      const label = document.createElement('label');\n", "      label.textContent = `Downloading \"${filename}\": `;\n", "      div.append<PERSON><PERSON>d(label);\n", "      const progress = document.createElement('progress');\n", "      progress.max = size;\n", "      div.append<PERSON><PERSON>d(progress);\n", "      document.body.appendChild(div);\n", "\n", "      const buffers = [];\n", "      let downloaded = 0;\n", "\n", "      const channel = await google.colab.kernel.comms.open(id);\n", "      // Send a message to notify the kernel that we're ready.\n", "      channel.send({})\n", "\n", "      for await (const message of channel.messages) {\n", "        // Send a message to notify the kernel that we're ready.\n", "        channel.send({})\n", "        if (message.buffers) {\n", "          for (const buffer of message.buffers) {\n", "            buffers.push(buffer);\n", "            downloaded += buffer.byteLength;\n", "            progress.value = downloaded;\n", "          }\n", "        }\n", "      }\n", "      const blob = new Blob(buffers, {type: 'application/binary'});\n", "      const a = document.createElement('a');\n", "      a.href = window.URL.createObjectURL(blob);\n", "      a.download = filename;\n", "      div.append<PERSON>hild(a);\n", "      a.click();\n", "      div.remove();\n", "    }\n", "  "]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Javascript object>"], "application/javascript": ["download(\"download_7c54a836-8214-44b1-a5da-f5b91afa640e\", \"COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt\", 2199)"]}, "metadata": {}}]}, {"cell_type": "code", "source": ["# Cell: Emergency Transcript Recovery and Download\n", "# =============================================================================\n", "\n", "import os\n", "import pickle\n", "import json\n", "from datetime import datetime, timedelta\n", "from google.colab import files\n", "\n", "print(\"🔍 Searching for available transcript data...\\n\")\n", "\n", "# Check what files exist\n", "transcript_found = False\n", "\n", "# Method 1: Check for any saved transcript files\n", "possible_files = [\n", "    \"/content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt\",\n", "    \"/content/EARLY_TRANSCRIPT_ONLY.txt\",\n", "    \"/content/enhanced_forensic_audio_v2.wav\",\n", "    \"/content/extracted_audio_raw.wav\"\n", "]\n", "\n", "print(\"📁 Checking for existing files:\")\n", "for file in possible_files:\n", "    if os.path.exists(file):\n", "        print(f\"  ✅ Found: {file} ({os.path.getsize(file) / 1024:.1f} KB)\")\n", "    else:\n", "        print(f\"  ❌ Not found: {file}\")\n", "\n", "# Method 2: Try to access variables in memory\n", "emergency_transcript_path = \"/content/EMERGENCY_TRANSCRIPT_RECOVERY.txt\"\n", "\n", "try:\n", "    # Check if whisper_result exists in memory\n", "    if 'whisper_result' in globals() and whisper_result:\n", "        print(\"\\n✅ Found whisper_result in memory!\")\n", "        with open(emergency_transcript_path, \"w\", encoding=\"utf-8\") as f:\n", "            f.write(\"EMERGENCY TRANSCRIPT RECOVERY\\n\")\n", "            f.write(\"=\"*50 + \"\\n\\n\")\n", "            f.write(f\"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\\n\")\n", "            f.write(\"RAW TRANSCRIPT TEXT:\\n\")\n", "            f.write(\"-\"*50 + \"\\n\\n\")\n", "            f.write(whisper_result.get('text', 'No text found'))\n", "            f.write(\"\\n\\n\")\n", "\n", "            if 'segments' in whisper_result:\n", "                f.write(\"TRANSCRIPT WITH TIMESTAMPS:\\n\")\n", "                f.write(\"-\"*50 + \"\\n\\n\")\n", "                for segment in whisper_result['segments']:\n", "                    start_time = str(timedelta(seconds=int(segment['start'])))\n", "                    end_time = str(timedelta(seconds=int(segment['end'])))\n", "                    text = segment.get('text', '')\n", "                    f.write(f\"[{start_time} - {end_time}] {text}\\n\")\n", "\n", "        transcript_found = True\n", "        print(f\"📝 Created emergency transcript from whisper_result\")\n", "\n", "except Exception as e:\n", "    print(f\"❌ Could not access whisper_result: {e}\")\n", "\n", "try:\n", "    # Check if enhanced_transcript exists in memory\n", "    if 'enhanced_transcript' in globals() and enhanced_transcript:\n", "        print(\"\\n✅ Found enhanced_transcript in memory!\")\n", "\n", "        if not transcript_found:  # Only create if we haven't already\n", "            with open(emergency_transcript_path, \"w\", encoding=\"utf-8\") as f:\n", "                f.write(\"EMERGENCY TRANSCRIPT RECOVERY (ENHANCED)\\n\")\n", "                f.write(\"=\"*50 + \"\\n\\n\")\n", "                f.write(f\"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\")\n", "                f.write(f\"Total words: {len(enhanced_transcript)}\\n\\n\")\n", "\n", "                f.write(\"SPEAKER-IDENTIFIED TRANSCRIPT:\\n\")\n", "                f.write(\"-\"*50 + \"\\n\\n\")\n", "\n", "                current_speaker = None\n", "                skip_seconds = 30  # De<PERSON>ult skip value\n", "\n", "                for word_data in enhanced_transcript:\n", "                    word_timestamp = word_data['start'] + skip_seconds\n", "                    word_text = word_data['word']\n", "                    speakers = word_data.get('speakers', [])\n", "\n", "                    primary_speaker = speakers[0] if speakers else \"UNKNOWN\"\n", "                    timestamp_str = str(timedelta(seconds=int(word_timestamp)))\n", "\n", "                    if primary_speaker != current_speaker:\n", "                        f.write(f\"\\n[{timestamp_str}] {primary_speaker}: \")\n", "                        current_speaker = primary_speaker\n", "                    f.write(f\"{word_text} \")\n", "\n", "        else:  # Append enhanced version to existing file\n", "            with open(emergency_transcript_path, \"a\", encoding=\"utf-8\") as f:\n", "                f.write(\"\\n\\n\" + \"=\"*50 + \"\\n\")\n", "                f.write(\"ENHANCED VERSION WITH SPEAKER IDENTIFICATION:\\n\")\n", "                f.write(\"=\"*50 + \"\\n\\n\")\n", "\n", "                current_speaker = None\n", "                skip_seconds = 30\n", "\n", "                for word_data in enhanced_transcript:\n", "                    word_timestamp = word_data['start'] + skip_seconds\n", "                    word_text = word_data['word']\n", "                    speakers = word_data.get('speakers', [])\n", "\n", "                    primary_speaker = speakers[0] if speakers else \"UNKNOWN\"\n", "                    timestamp_str = str(timedelta(seconds=int(word_timestamp)))\n", "\n", "                    if primary_speaker != current_speaker:\n", "                        f.write(f\"\\n[{timestamp_str}] {primary_speaker}: \")\n", "                        current_speaker = primary_speaker\n", "                    f.write(f\"{word_text} \")\n", "\n", "        transcript_found = True\n", "        print(f\"📝 Added enhanced transcript with speaker identification\")\n", "\n", "except Exception as e:\n", "    print(f\"❌ Could not access enhanced_transcript: {e}\")\n", "\n", "# Method 3: Look for partial analysis file\n", "partial_file = \"/content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt\"\n", "if os.path.exists(partial_file) and os.path.getsize(partial_file) > 1000:\n", "    print(f\"\\n✅ Found partial analysis file with content\")\n", "    transcript_found = True\n", "    # Download it as is\n", "    print(f\"📥 Downloading partial analysis file...\")\n", "    files.download(partial_file)\n", "\n", "# Download the emergency transcript if created\n", "if transcript_found and os.path.exists(emergency_transcript_path):\n", "    print(f\"\\n📥 Downloading emergency transcript...\")\n", "    files.download(emergency_transcript_path)\n", "    print(f\"✅ Download complete! File size: {os.path.getsize(emergency_transcript_path) / 1024:.1f} KB\")\n", "else:\n", "    print(\"\\n❌ No transcript data found to download\")\n", "    print(\"The transcription may not have completed. Please check the error messages above.\")\n", "\n", "# Also try to save the raw segments data for backup\n", "try:\n", "    if 'whisper_result' in globals() and whisper_result:\n", "        backup_path = \"/content/whisper_result_backup.json\"\n", "        with open(backup_path, 'w') as f:\n", "            # Convert to serializable format\n", "            backup_data = {\n", "                'text': whisper_result.get('text', ''),\n", "                'segments': whisper_result.get('segments', []),\n", "                'language': whisper_result.get('language', 'en')\n", "            }\n", "            json.dump(backup_data, f, indent=2)\n", "        print(f\"\\n💾 Also saved whisper_result backup\")\n", "        files.download(backup_path)\n", "except:\n", "    pass"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 243}, "id": "fj0j3c9o8Pc_", "outputId": "5f64f147-21d5-407b-c60d-6a48e760ab78"}, "execution_count": 9, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🔍 Searching for available transcript data...\n", "\n", "📁 Checking for existing files:\n", "  ✅ Found: /content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt (2.1 KB)\n", "  ❌ Not found: /content/EARLY_TRANSCRIPT_ONLY.txt\n", "  ✅ Found: /content/enhanced_forensic_audio_v2.wav (110312.3 KB)\n", "  ✅ Found: /content/extracted_audio_raw.wav (110312.3 KB)\n", "\n", "✅ Found partial analysis file with content\n", "📥 Downloading partial analysis file...\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Javascript object>"], "application/javascript": ["\n", "    async function download(id, filename, size) {\n", "      if (!google.colab.kernel.accessAllowed) {\n", "        return;\n", "      }\n", "      const div = document.createElement('div');\n", "      const label = document.createElement('label');\n", "      label.textContent = `Downloading \"${filename}\": `;\n", "      div.append<PERSON><PERSON>d(label);\n", "      const progress = document.createElement('progress');\n", "      progress.max = size;\n", "      div.append<PERSON><PERSON>d(progress);\n", "      document.body.appendChild(div);\n", "\n", "      const buffers = [];\n", "      let downloaded = 0;\n", "\n", "      const channel = await google.colab.kernel.comms.open(id);\n", "      // Send a message to notify the kernel that we're ready.\n", "      channel.send({})\n", "\n", "      for await (const message of channel.messages) {\n", "        // Send a message to notify the kernel that we're ready.\n", "        channel.send({})\n", "        if (message.buffers) {\n", "          for (const buffer of message.buffers) {\n", "            buffers.push(buffer);\n", "            downloaded += buffer.byteLength;\n", "            progress.value = downloaded;\n", "          }\n", "        }\n", "      }\n", "      const blob = new Blob(buffers, {type: 'application/binary'});\n", "      const a = document.createElement('a');\n", "      a.href = window.URL.createObjectURL(blob);\n", "      a.download = filename;\n", "      div.append<PERSON>hild(a);\n", "      a.click();\n", "      div.remove();\n", "    }\n", "  "]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Javascript object>"], "application/javascript": ["download(\"download_0f33e987-a7aa-490d-acab-db566896bb9b\", \"COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt\", 2199)"]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["\n", "❌ No transcript data found to download\n", "The transcription may not have completed. Please check the error messages above.\n"]}]}]}