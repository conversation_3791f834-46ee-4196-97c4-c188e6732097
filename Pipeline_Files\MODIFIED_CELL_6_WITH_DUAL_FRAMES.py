# MODIFIED CELL 6: Process with BOTH Frame Sets (354 frames total)
# ================================================================
# This version extracts NEW frames offset by 10 seconds AND uses your existing frames
# Giving you double temporal resolution (frames every 10 seconds)

def process_complete_enhanced_forensic_analysis_dual_frames(video_path, skip_seconds=30):
    """
    Enhanced pipeline that uses BOTH frame sets:
    1. Your existing 177 frames (at 0:30, 0:50, 1:10, etc.)
    2. New 177 frames offset by 10s (at 0:40, 1:00, 1:20, etc.)
    Total: ~354 frames for double temporal resolution
    """
    import os
    import json
    import hashlib
    from datetime import datetime, timedelta
    from google.colab import files
    
    print("🏛️ ENHANCED FORENSIC ANALYSIS WITH DUAL FRAME SETS")
    print("="*80)
    print(f"Video: {video_path}")
    print(f"Skip seconds: {skip_seconds}")
    print("📸 Using BOTH existing and new frames for maximum coverage")
    print("="*80 + "\n")
    
    # Step 1: Extract and enhance audio (same as before)
    print("🎵 Step 1: Extracting and enhancing audio...")
    audio_raw = "/content/extracted_audio_raw.wav"
    audio_enhanced = "/content/enhanced_forensic_audio_v2.wav"
    
    extract_cmd = [
        'ffmpeg', '-y', '-ss', str(skip_seconds), '-i', video_path,
        '-acodec', 'pcm_s16le', '-ar', '16000', '-ac', '1', audio_raw
    ]
    subprocess.run(extract_cmd, capture_output=True)
    
    enhanced_audio_processing_for_difficult_sections(audio_raw, audio_enhanced)
    
    # Step 2: Transcribe with Whisper
    print("\n📝 Step 2: Transcribing with Whisper Large-v3...")
    whisper_result = transcribe_with_maximum_accuracy_enhanced(audio_enhanced)
    print(f"✅ Transcription complete: {len(whisper_result['segments'])} segments")
    
    # Save raw whisper result
    whisper_path = "/content/whisper_transcription.json"
    with open(whisper_path, 'w') as f:
        json.dump(whisper_result, f, indent=2)
    print("📥 Downloading raw transcription...")
    files.download(whisper_path)
    
    # Step 3: Speaker diarization
    print("\n👥 Step 3: Performing speaker diarization...")
    diarization_result = diarization_pipeline(audio_enhanced)
    
    # Step 4: Detect overlaps
    overlaps = detect_speaker_overlaps_and_separate_enhanced(
        audio_enhanced, diarization_result, whisper_result
    )
    
    # Step 5: Combine transcription with speakers
    print("\n🔗 Step 5: Combining transcription with speaker identification...")
    enhanced_transcript = combine_transcription_and_speakers_enhanced(
        whisper_result, diarization_result, overlaps
    )
    
    # Save early transcript
    print("\n💾 Saving speaker-identified transcript...")
    early_transcript_path = "/content/SPEAKER_IDENTIFIED_TRANSCRIPT.txt"
    
    with open(early_transcript_path, "w", encoding="utf-8") as f:
        f.write("SPEAKER-IDENTIFIED FORENSIC TRANSCRIPT\n")
        f.write("="*60 + "\n\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Video: {os.path.basename(video_path)}\n")
        f.write(f"Skip seconds: {skip_seconds}\n")
        f.write(f"Total words: {len(enhanced_transcript)}\n\n")
        
        f.write("TRANSCRIPT:\n")
        f.write("-"*60 + "\n\n")
        
        current_speaker = None
        for word_data in enhanced_transcript:
            word_timestamp = word_data['start'] + skip_seconds
            word_text = word_data['word']
            speakers = word_data.get('speakers', [])
            confidence = word_data.get('confidence', 0.0)
            
            primary_speaker = speakers[0] if speakers else "UNKNOWN"
            timestamp_str = str(timedelta(seconds=int(word_timestamp)))
            
            if primary_speaker != current_speaker:
                f.write(f"\n[{timestamp_str}] {primary_speaker}: ")
                current_speaker = primary_speaker
            
            if confidence < 0.7:
                f.write(f"[{word_text}?] ")
            else:
                f.write(f"{word_text} ")
    
    print("📥 Downloading speaker-identified transcript...")
    files.download(early_transcript_path)
    
    # Step 6A: Extract NEW frames with 10-second offset
    print("\n🎥 Step 6A: Extracting NEW frames (offset by 10 seconds)...")
    
    # Modified frame extraction function with offset
    def extract_frames_with_offset(video_path, skip_seconds, offset_seconds):
        frames_dir = "/content/video_frames_new"
        os.makedirs(frames_dir, exist_ok=True)
        
        print(f"📸 Extracting frames starting at {skip_seconds + offset_seconds} seconds...")
        
        extract_frames_cmd = [
            'ffmpeg', '-y', '-ss', str(skip_seconds + offset_seconds), '-i', video_path,
            '-vf', 'fps=1/20',  # One frame every 20 seconds
            '-q:v', '2',
            '-vsync', 'vfr',
            f'{frames_dir}/frame_new_%04d.jpg'
        ]
        
        subprocess.run(extract_frames_cmd, capture_output=True)
        
        frame_files = sorted([f for f in os.listdir(frames_dir) if f.endswith('.jpg')])
        print(f"✅ Extracted {len(frame_files)} NEW frames")
        
        # Analyze these frames
        visual_context_new = []
        
        for i, frame_file in enumerate(frame_files):
            frame_path = os.path.join(frames_dir, frame_file)
            # Actual timestamp includes the offset
            actual_timestamp = (i * 20) + skip_seconds + offset_seconds
            
            try:
                with open(frame_path, 'rb') as f:
                    frame_data = base64.b64encode(f.read()).decode()
                
                response = openai.ChatCompletion.create(
                    model="gpt-4o",
                    messages=[
                        {
                            "role": "system",
                            "content": "You are performing FORENSIC-GRADE analysis. This means highest standards of detail and accuracy."
                        },
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": "Perform FORENSIC-GRADE analysis of this police bodycam frame. Focus on clothing status, restraints, dignity concerns."
                                },
                                {
                                    "type": "image_url",
                                    "image_url": {"url": f"data:image/jpeg;base64,{frame_data}"}
                                }
                            ]
                        }
                    ],
                    max_tokens=800,
                    temperature=0.1
                )
                
                visual_context_new.append({
                    'timestamp': actual_timestamp,
                    'frame': frame_file,
                    'source': 'new_offset',
                    'analysis': response.choices[0].message.content
                })
                
                print(f"✅ NEW frame {i+1}/{len(frame_files)} analyzed - Time: {actual_timestamp//60:02d}:{actual_timestamp%60:02d}")
                time.sleep(1)
                
            except Exception as e:
                print(f"⚠️ Frame {frame_file} analysis failed: {e}")
                visual_context_new.append({
                    'timestamp': actual_timestamp,
                    'frame': frame_file,
                    'source': 'new_offset',
                    'analysis': f"Analysis failed: {str(e)}"
                })
        
        return visual_context_new
    
    # Extract and analyze NEW frames
    visual_context_new = extract_frames_with_offset(video_path, skip_seconds, 10)
    
    # Step 6B: Analyze EXISTING frames from uploaded zip
    print("\n🎥 Step 6B: Analyzing your EXISTING frames...")
    
    # Check if frames.zip was uploaded
    if os.path.exists('/content/frames.zip'):
        print("✅ Found uploaded frames.zip")
        visual_context_existing = reuse_existing_frames_for_analysis('/content/frames.zip', skip_seconds)
    else:
        print("⚠️ No frames.zip found - using only new frames")
        visual_context_existing = []
    
    # Step 6C: Combine both frame sets
    print("\n🔗 Step 6C: Combining frame analyses...")
    visual_context_combined = visual_context_new + visual_context_existing
    
    # Sort by timestamp for chronological order
    visual_context_combined.sort(key=lambda x: x['timestamp'])
    
    print(f"✅ COMBINED ANALYSIS: {len(visual_context_combined)} total frames")
    print(f"   - New frames: {len(visual_context_new)}")
    print(f"   - Existing frames: {len(visual_context_existing)}")
    print(f"   - Temporal resolution: Every ~10 seconds")
    
    # Save combined visual analysis
    combined_path = "/content/combined_visual_analysis.json"
    with open(combined_path, 'w') as f:
        json.dump(visual_context_combined, f, indent=2)
    print("\n📥 Downloading combined visual analysis...")
    files.download(combined_path)
    
    # Step 7: Inject visual context (now with double the frames!)
    print("\n💉 Step 7: Creating visual context injections (enhanced resolution)...")
    visual_injections = inject_visual_context_into_transcript(
        enhanced_transcript, visual_context_combined, skip_seconds
    )
    print(f"✅ Created {len(visual_injections)} visual injections")
    
    # Step 8: Create transcript chunks
    print("\n📄 Step 8: Creating transcript chunks for analysis...")
    chunks = process_transcript_chunks_with_rate_limiting(
        enhanced_transcript, skip_seconds
    )
    
    # Step 9: Quick violation scan
    print("\n🔍 Step 9: Quick violation scan...")
    violations_summary = {
        'privacy': 0,
        'dignity': 0,
        'force': 0,
        'procedure': 0
    }
    
    for word_data in enhanced_transcript:
        word_lower = word_data['word'].lower()
        if any(term in word_lower for term in ['towel', 'naked', 'undressed']):
            violations_summary['privacy'] += 1
        if any(term in word_lower for term in ['cuff', 'handcuff', 'restrain']):
            violations_summary['force'] += 1
    
    # Step 10: Analyze chunks with GPT-4
    print("\n⚖️ Step 10: Performing legal analysis with rate limiting...")
    chunk_analyses = analyze_chunks_with_gpt4(chunks, violations_summary)
    
    # Step 11: Generate final comprehensive document
    print("\n📝 Step 11: Generating comprehensive forensic document...")
    
    output_path = "/content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt"
    
    with open(output_path, "w", encoding="utf-8") as f:
        # Header
        f.write("COMPREHENSIVE FORENSIC LEGAL ANALYSIS - ENHANCED RESOLUTION\n")
        f.write("="*80 + "\n\n")
        f.write("FORENSIC-GRADE ANALYSIS WITH DUAL FRAME SETS\n")
        f.write("This analysis uses ~354 frames for superior temporal resolution\n\n")
        
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Video: {os.path.basename(video_path)}\n")
        f.write(f"Duration Analyzed: {len(enhanced_transcript)} words\n")
        f.write(f"Frames Analyzed: {len(visual_context_combined)} (every ~10 seconds)\n\n")
        
        # Rest of the document generation remains the same...
        # [Include rest of document generation code from original]
    
    print(f"\n📥 Downloading comprehensive analysis...")
    files.download(output_path)
    
    print("\n✅ FORENSIC ANALYSIS COMPLETE WITH ENHANCED FRAME COVERAGE!")
    print(f"Total frames analyzed: {len(visual_context_combined)}")
    
    return output_path

# Also keep the original frame reuse function available
print("✅ Modified Cell 6 with dual frame analysis ready!")