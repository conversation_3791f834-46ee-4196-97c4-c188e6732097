# CONTINUATION PART 3 - COMPLETE RESTORED ENHANCED PIPELINE
# =========================================================
# This file continues from PART2 to complete the full restoration

# =============================================================================
# ADDITIONAL MISSING FUNCTIONS FROM ORIGINAL PIPELINE
# =============================================================================

def format_overlap_readable(overlap, whisper_result):
    """Format speaker overlaps in readable format"""
    start = overlap['start']
    end = overlap['end']
    speakers = overlap['speakers']
    timestamp = f"[{int(start//60):02}:{int(start%60):02}–{int(end//60):02}:{int(end%60):02}]"

    lines_by_speaker = {s: [] for s in speakers}

    for seg in whisper_result['segments']:
        if seg['start'] >= start and seg['end'] <= end:
            speaker = seg.get('speaker', 'UNKNOWN')
            if speaker in lines_by_speaker:
                lines_by_speaker[speaker].append(seg['text'].strip())

    output = f"{timestamp} **OVERLAP** {tuple(speakers)}:\n"
    for speaker, lines in lines_by_speaker.items():
        if lines:
            joined = ' '.join(lines)
            output += f"{speaker}: {joined}\n"

    return output.strip()

def analyze_with_gpt4_forensic_enhanced(transcript_text, speaker_segments, trigger_words, visual_context):
    """Enhanced GPT-4 forensic analysis incorporating both audio and visual data"""
    print("🧠 Running enhanced GPT-4 forensic analysis...")

    # Combine visual context for analysis
    visual_summary = "\n".join([
        f"[{ctx['timestamp']//60:02d}:{ctx['timestamp']%60:02d}] VISUAL: {ctx['analysis']}"
        for ctx in visual_context[:10]  # Include first 10 visual analyses
    ])

    system_prompt = """You are a certified forensic audiovisual analyst with 25+ years experience in criminal procedure, constitutional law (42 U.S.C. § 1983), and police misconduct analysis. You have served as a court-appointed expert witness and specialize in integrated audio-visual evidence analysis.

Conduct comprehensive forensic analysis incorporating both audio transcript and visual frame analysis for:

1. CONSTITUTIONAL VIOLATIONS:
   - 4th Amendment (search/seizure without warrant)
   - 5th Amendment (Miranda rights, self-incrimination)
   - 8th Amendment (excessive force, cruel treatment)
   - 14th Amendment (due process, equal protection)

2. STATUTORY VIOLATIONS:
   - Florida Statutes (Baker Act § 394.463)
   - Arrest authority compliance (Ch. 901)
   - Mental health detention protocols
   - Transport and medical clearance requirements

3. PROCEDURAL BREACHES:
   - Required warnings not given
   - Supervisor notification failures
   - Medical clearance timing violations
   - Evidence preservation protocols

4. USE OF FORCE ASSESSMENT:
   - Graham v. Connor standards compliance
   - Proportionality analysis (visual evidence critical)
   - De-escalation attempts/failures
   - Weapon deployment justification

5. AUDIO-VISUAL CORRELATION:
   - Consistency between spoken actions and visual evidence
   - Body language vs verbal compliance
   - Environmental factors affecting behavior
   - Officer positioning and tactical decisions

6. PSYCHOLOGICAL MARKERS:
   - Mental health crisis indicators (audio + visual)
   - Stress escalation patterns
   - Compliance vs resistance behaviors
   - Environmental stressors
   - Mental health interventions

7. ATTIRE ANALYSIS (CRITICAL FOR PRIVACY ASSESSMENT):
   - Clothing status: Fully dressed, partially dressed, minimal clothing, towel only, etc.
   - State of dress: Appropriate, inappropriate for public, emergency exit clothing
   - Signs of rushed dressing: Incomplete clothing, towel wrapped, wet appearance
   - Modesty concerns: Areas of body exposed, coverage inadequacy
   - Footwear: Barefoot, shoes, slippers (indicates emergency exit)

8. PRIVACY & DIGNITY INDICATORS:
   - Public exposure level: Private home vs. public view
   - Bystander presence: Neighbors, crowds, passersby witnessing exposure
   - Recording implications: Subject aware of being filmed in state of undress
   - Weather conditions affecting minimal clothing exposure

9. EMERGENCY/CRISIS INDICATORS:
   - Wet hair/body (shower interruption)
   - Rushed appearance (hastily grabbed clothing/towel)
   - Bathroom/shower context (wet floors, steam, towels visible)
   - Time pressure indicators (incomplete dressing)

10. RESTRAINT/HANDCUFFING ANALYSIS:
   - Handcuff application on subject in minimal clothing
   - Positioning: hands behind back while in towel/minimal clothing
   - Dignity concerns during restraint application
   - Cooperative behavior vs. restraint necessity

11. STANDARD FORENSIC ELEMENTS:
   - Scene setting and location context
   - People positions and actions
   - Equipment and evidence visible
   - Officer positioning relative to undressed subject
   - Safety and tactical considerations

12. CONSTITUTIONAL CONCERNS:
   - 4th Amendment: Privacy expectations in home
   - 8th Amendment: Dignity during detention
   - Public exposure creating humiliation
   - Reasonable accommodation for clothing needs

Provide specific timestamps, direct quotes, visual observations, legal significance, and court-admissible analysis with integrated audio-visual evidence correlation. Be extremely specific about clothing status and privacy implications. Flag any dignitary concerns or inappropriate exposure situations. Document if subject appears to have been interrupted during private activities (showering, bathing, dressing, etc.)."""

    user_prompt = f"""
POLICE BODYCAM INTEGRATED AUDIO-VISUAL ANALYSIS:

AUDIO TRANSCRIPT (First 8000 characters):
{transcript_text[:8000]}

VISUAL FRAME ANALYSIS:
{visual_summary}

LEGAL TRIGGER WORDS DETECTED:
{', '.join(trigger_words)}

SPEAKER COUNT: {len(set(seg.get('speaker', 'Unknown') for seg in speaker_segments))}

Provide comprehensive integrated forensic analysis with:
- Constitutional and statutory violations (cite specific evidence)
- Critical timeline events with both audio and visual timestamps
- Use of force analysis with visual evidence correlation
- Risk assessment for legal proceedings
- Evidence preservation recommendations
- Audio-visual consistency analysis
"""

    try:
        response = openai.ChatCompletion.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            max_tokens=4000,
            temperature=0.05
        )

        return response.choices[0].message.content

    except Exception as e:
        print(f"❌ GPT-4 analysis failed: {e}")
        return f"GPT-4 analysis unavailable: {e}"

def generate_comprehensive_legal_analysis_document(
    transcript_text, enhanced_transcript, visual_context,
    compliance_violations, behavioral_contradictions,
    privacy_violations, dignity_violations, public_exposure,
    harassment_indicators, retaliation_patterns,
    narrative_shaping, coordinated_behavior,
    skip_seconds=30
):
    """Generate comprehensive legal analysis document with all required sections"""

    legal_analysis_prompt = f"""You are a certified forensic audiovisual analyst and constitutional law expert with 25+ years of experience serving as a court-appointed expert witness. Generate a comprehensive legal analysis document based on the integrated audio-visual evidence provided.

STRUCTURE YOUR ANALYSIS WITH THESE MANDATORY SECTIONS:

1. STATUTORY VIOLATIONS ANALYSIS:
   - Florida Statute § 394.463 (Baker Act procedures)
   - Florida Statute Chapter 901 (Arrest authority and procedures)
   - Florida Statute § 776.05 (Law enforcement use of force)
   - Florida Statute § 843.02 (Resisting arrest provisions)
   - Florida Administrative Code 11B-27 (Mental health transport)
   - Cite specific violations with timestamp evidence

2. CONSTITUTIONAL VIOLATIONS ANALYSIS:
   - 4th Amendment: Search and seizure violations, warrant requirements
   - 5th Amendment: Miranda rights, self-incrimination issues
   - 8th Amendment: Excessive force, cruel and unusual punishment
   - 14th Amendment: Due process, equal protection violations
   - Provide specific constitutional analysis with case law citations

3. PROCEDURAL BREACHES ASSESSMENT:
   - Required warnings not provided (Miranda, medical rights)
   - Transport protocol violations
   - Mental health criteria non-compliance
   - Medical clearance timing violations
   - Supervisor notification failures
   - Chain of custody issues

4. PATTERNS OF MISCONDUCT IDENTIFICATION:
   - Evidence of coordinated narrative shaping: {len(narrative_shaping)} incidents
   - Coordinated behavior patterns: {len(coordinated_behavior)} instances
   - Retaliatory conduct indicators: {len(retaliation_patterns)} patterns
   - Selective enforcement evidence

5. PRIVACY & DIGNITY VIOLATIONS:
   - Public exposure incidents: {len(public_exposure)} documented
   - Privacy violations: {len(privacy_violations)} identified
   - Dignity violations: {len(dignity_violations)} documented
   - Inappropriate disclosure or humiliation tactics

6. USE OF FORCE ASSESSMENT (Graham v. Connor Analysis):
   - Severity of crime factors
   - Immediacy of threat assessment
   - Actively resisting arrest evaluation
   - Attempting to evade by flight analysis
   - Totality of circumstances review
   - Florida agency force protocol compliance

7. HARASSMENT OR RETALIATION EVIDENCE:
   - Harassment indicators: {len(harassment_indicators)} documented
   - Personal animus evidence
   - Power assertion tactics: documented instances
   - Language indicating improper motive

8. AUDIO-VISUAL CONTRADICTION ANALYSIS:
   - Commands vs. compliance discrepancies: {len(compliance_violations)} violations
   - Behavioral contradictions: {len(behavioral_contradictions)} identified
   - Officer statements vs. visual evidence mismatches

EVIDENCE PROVIDED:
- Audio transcript: {len(transcript_text)} characters
- Enhanced transcript: {len(enhanced_transcript)} words
- Visual context points: {len(visual_context)} frames analyzed
- Compliance violations: {compliance_violations}
- Privacy violations: {privacy_violations}
- Harassment patterns: {harassment_indicators}

Provide specific timestamps, direct quotes, visual evidence references, statutory citations, constitutional analysis, and court-admissible conclusions for each section. Use Bluebook citation format where applicable."""

    try:
        response = openai.ChatCompletion.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are a certified forensic legal analyst specializing in constitutional law, criminal procedure, and police misconduct analysis."},
                {"role": "user", "content": legal_analysis_prompt}
            ],
            max_tokens=4000,
            temperature=0.05
        )

        return response.choices[0].message.content

    except Exception as e:
        print(f"❌ Comprehensive legal analysis failed: {e}")
        return f"Comprehensive legal analysis unavailable: {e}"

def inject_attire_context_annotations(enhanced_transcript, visual_context, skip_seconds=30):
    """Inject specific attire and privacy context annotations"""
    print("💉 Injecting attire and privacy context annotations...")

    attire_annotations = {}

    for i, word_data in enumerate(enhanced_transcript):
        word_timestamp = word_data['start'] + skip_seconds
        word_text = word_data['word'].lower()

        # Find corresponding visual context for this word
        closest_visual = None
        min_time_diff = float('inf')

        for ctx in visual_context:
            time_diff = abs(ctx['timestamp'] - word_timestamp)
            if time_diff < min_time_diff and time_diff < 15:
                min_time_diff = time_diff
                closest_visual = ctx

        if closest_visual:
            visual_text = closest_visual['analysis'].lower()

            # Check for specific attire situations
            if any(indicator in visual_text for indicator in ['towel only', 'minimal clothing', 'undressed']):
                attire_annotations[i] = "*{ATTIRE CONCERN: Subject in minimal clothing/towel only - Privacy implications}*"

            elif any(indicator in visual_text for indicator in ['wet from shower', 'rushed from bathroom']):
                attire_annotations[i] = "*{EMERGENCY EXIT: Subject interrupted during private activity - Constitutional privacy concern}*"

            elif any(indicator in visual_text for indicator in ['handcuff', 'restrain']) and any(attire in visual_text for attire in ['towel', 'minimal', 'undressed']):
                attire_annotations[i] = "*{CRITICAL DIGNITY VIOLATION: Restraint applied to subject in minimal clothing - 8th Amendment concern}*"

            elif any(indicator in visual_text for indicator in ['public exposure', 'neighbors seeing']):
                attire_annotations[i] = "*{PUBLIC EXPOSURE: Inappropriate clothing status in public view - Dignity violation}*"

            elif any(indicator in visual_text for indicator in ['barefoot', 'incomplete dress']):
                attire_annotations[i] = "*{RUSHED DRESSING: Emergency exit indicators - Privacy interruption documented}*"

    print(f"✅ Attire context annotations prepared: {len(attire_annotations)} annotations")
    return attire_annotations

def calculate_violation_severity_score(violation_type, context_factors):
    """Calculate severity score for violations based on multiple factors"""

    base_scores = {
        "handcuffing_minimal_clothing": 9,
        "public_exposure": 8,
        "privacy_interruption": 7,
        "excessive_force": 9,
        "miranda_violation": 8,
        "consent_violation": 7,
        "dignity_violation": 8,
        "harassment": 7,
        "retaliation": 8,
        "narrative_coordination": 6
    }

    multipliers = {
        "multiple_witnesses": 1.3,
        "recording_present": 1.2,
        "vulnerable_individual": 1.4,
        "mental_health_crisis": 1.3,
        "cooperative_subject": 1.5,
        "public_location": 1.3,
        "repeated_behavior": 1.4
    }

    # Get base score
    base_score = base_scores.get(violation_type, 5)

    # Apply multipliers
    final_score = base_score
    for factor, value in context_factors.items():
        if value and factor in multipliers:
            final_score *= multipliers[factor]

    return min(10, round(final_score, 1))  # Cap at 10

# =============================================================================
# RATE LIMIT HANDLING AND RECOVERY FUNCTIONS
# =============================================================================

def process_chunks_with_rate_limit_handling(transcript_chunks, violations_data, skip_seconds=30):
    """
    Process transcript chunks with intelligent rate limit handling
    """
    print("\n⚖️ Performing rate-limit-aware chunked legal analysis...")

    chunk_analyses = []
    failed_chunks = []

    # Strategy 1: Add delays between chunks
    DELAY_BETWEEN_CHUNKS = 20  # seconds

    for i, chunk in enumerate(transcript_chunks):
        print(f"\n🔄 Processing chunk {i+1}/{len(transcript_chunks)}...")

        retry_count = 0
        max_retries = 3
        success = False

        while retry_count < max_retries and not success:
            try:
                # Prepare violation summary for context
                violation_summary = f"""
Current Violations Found:
- Compliance Violations: {len(violations_data.get('compliance_violations', []))}
- Privacy Violations: {len(violations_data.get('privacy_violations', []))}
- Dignity Violations: {len(violations_data.get('dignity_violations', []))}
- Public Exposure: {len(violations_data.get('public_exposure', []))}
- Attire Violations: {len(violations_data.get('attire_violations', []))}
"""

                response = openai.ChatCompletion.create(
                    model="gpt-4",
                    messages=[
                        {
                            "role": "system",
                            "content": """You are a senior forensic analyst specializing in law enforcement interactions.
Analyze this transcript section for legal violations with special attention to:
- Constitutional violations (4th, 5th, 8th, 14th Amendment)
- Privacy and dignity violations (especially regarding state of undress, towels, wet from shower)
- Mental health handling under Baker Act (Fla. Stat. § 394.463)
- Use of force and restraint application
- Procedural violations and misconduct"""
                        },
                        {
                            "role": "user",
                            "content": f"""Analyze transcript section {i+1} of {len(transcript_chunks)}:

{chunk}

{violation_summary}

Identify specific violations with timestamps and exact quotes. Focus on:
1. Handcuffing of cooperative individuals in minimal clothing
2. Public exposure and dignity violations
3. Mental health crisis handling
4. Constitutional rights violations"""
                        }
                    ],
                    max_tokens=1200,  # Reduced from 1500 to leave more headroom
                    temperature=0.1
                )

                chunk_analyses.append(response.choices[0].message.content)
                print(f"✅ Chunk {i+1} analyzed successfully")
                success = True

            except openai.error.RateLimitError as e:
                retry_count += 1

                # Extract wait time from error message
                wait_time = 20  # default
                error_msg = str(e)
                if "Please try again in" in error_msg:
                    try:
                        wait_time = float(error_msg.split("Please try again in ")[1].split("s")[0]) + 2
                    except:
                        wait_time = 20

                if retry_count < max_retries:
                    print(f"⏳ Rate limit hit. Waiting {wait_time:.1f} seconds before retry {retry_count}/{max_retries}...")
                    time.sleep(wait_time)
                else:
                    print(f"❌ Chunk {i+1} failed after {max_retries} retries")
                    failed_chunks.append((i, chunk))
                    chunk_analyses.append(f"[Analysis pending - rate limit exceeded for chunk {i+1}]")

            except Exception as e:
                print(f"❌ Chunk {i+1} analysis failed: {e}")
                chunk_analyses.append(f"Analysis failed for chunk {i+1}: {str(e)}")
                success = True  # Move on to next chunk

        # Add delay between successful chunks to avoid hitting rate limit
        if success and i < len(transcript_chunks) - 1:
            print(f"⏱️ Waiting {DELAY_BETWEEN_CHUNKS} seconds before next chunk...")
            time.sleep(DELAY_BETWEEN_CHUNKS)

    # Strategy 2: Retry failed chunks with longer delays
    if failed_chunks:
        print(f"\n🔄 Retrying {len(failed_chunks)} failed chunks with extended delays...")
        time.sleep(30)  # Wait 30 seconds before retrying

        for chunk_index, chunk_text in failed_chunks:
            try:
                print(f"🔄 Retrying chunk {chunk_index + 1}...")

                response = openai.ChatCompletion.create(
                    model="gpt-4",
                    messages=[
                        {
                            "role": "system",
                            "content": "You are a forensic analyst. Provide a brief analysis of legal violations in this transcript section."
                        },
                        {
                            "role": "user",
                            "content": f"Analyze section {chunk_index + 1}:\n{chunk_text[:3000]}\n\nFocus on key violations only."
                        }
                    ],
                    max_tokens=800,  # Even more conservative
                    temperature=0.1
                )

                chunk_analyses[chunk_index] = response.choices[0].message.content
                print(f"✅ Chunk {chunk_index + 1} retry successful")
                time.sleep(20)  # Wait between retries

            except Exception as e:
                print(f"❌ Chunk {chunk_index + 1} retry also failed: {e}")

    return chunk_analyses

# ALTERNATIVE STRATEGY: Use GPT-3.5 for overflow chunks
def analyze_with_gpt35_fallback(chunk_text, chunk_index, total_chunks):
    """
    Fallback to GPT-3.5-turbo for chunks that fail with GPT-4
    """
    try:
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[
                {
                    "role": "system",
                    "content": """You are a legal analyst. Analyze this police transcript for:
- Constitutional violations
- Use of force concerns
- Privacy/dignity violations
- Procedural issues
Be specific with timestamps and quotes."""
                },
                {
                    "role": "user",
                    "content": f"""Section {chunk_index + 1} of {total_chunks}:

{chunk_text}

List key violations found."""
                }
            ],
            max_tokens=1000,
            temperature=0.1
        )

        return f"[GPT-3.5 Analysis]\n{response.choices[0].message.content}"

    except Exception as e:
        return f"[Analysis failed for chunk {chunk_index + 1}: {str(e)}]"

def perform_robust_chunked_analysis(enhanced_transcript, violations_data, skip_seconds=30):
    """
    Robust chunked analysis with multiple fallback strategies
    """
    print("\n⚖️ Performing robust chunked legal analysis with rate limit handling...")

    # Prepare transcript chunks
    transcript_chunks = []
    current_chunk = []
    current_chunk_size = 0
    max_chunk_size = 4000  # Reduced from 6000 to leave more headroom

    for word_data in enhanced_transcript:
        word_timestamp = word_data['start'] + skip_seconds
        word_text = word_data['word']
        speakers = word_data.get('speakers', [])
        primary_speaker = speakers[0] if speakers else "UNKNOWN"
        timestamp_str = str(timedelta(seconds=int(word_timestamp)))

        line = f"[{timestamp_str}] {primary_speaker}: {word_text} "
        line_size = len(line)

        if current_chunk_size + line_size > max_chunk_size and current_chunk:
            transcript_chunks.append(''.join(current_chunk))
            current_chunk = [line]
            current_chunk_size = line_size
        else:
            current_chunk.append(line)
            current_chunk_size += line_size

    if current_chunk:
        transcript_chunks.append(''.join(current_chunk))

    print(f"📄 Split transcript into {len(transcript_chunks)} smaller chunks")

    # Process chunks with rate limit handling
    chunk_analyses = process_chunks_with_rate_limit_handling(
        transcript_chunks,
        violations_data,
        skip_seconds
    )

    # Combine analyses
    comprehensive_analysis = "\n\n=== COMPREHENSIVE LEGAL ANALYSIS ===\n\n"

    # Add summary of successful analyses
    successful_chunks = sum(1 for analysis in chunk_analyses if "[Analysis pending" not in analysis and "failed" not in analysis)
    comprehensive_analysis += f"Analysis Status: {successful_chunks}/{len(transcript_chunks)} chunks successfully analyzed\n\n"

    for i, analysis in enumerate(chunk_analyses):
        comprehensive_analysis += f"\n--- Section {i+1} Analysis ---\n{analysis}\n"

    return comprehensive_analysis

def save_partial_analysis(chunk_analyses, output_path="/content/PARTIAL_ANALYSIS.txt"):
    """
    Save whatever analysis was completed before rate limits
    """
    with open(output_path, "w", encoding="utf-8") as f:
        f.write("PARTIAL LEGAL ANALYSIS - RATE LIMITED\n")
        f.write("="*50 + "\n\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        successful = sum(1 for a in chunk_analyses if "[Analysis pending" not in a)
        f.write(f"Successfully analyzed: {successful}/{len(chunk_analyses)} chunks\n\n")

        for i, analysis in enumerate(chunk_analyses):
            if "[Analysis pending" not in analysis and "failed" not in analysis:
                f.write(f"\n--- Section {i+1} ---\n{analysis}\n")

    from google.colab import files
    files.download(output_path)
    print(f"📥 Downloaded partial analysis with {successful} completed sections")

# =============================================================================
# SPECIAL CELLS FOR CURRENT VIDEO PROCESSING
# =============================================================================

# Cell for uploading existing frames to avoid re-extraction
def upload_and_process_existing_frames():
    """Special cell for uploading existing frame archive"""
    from google.colab import files
    import zipfile
    import os
    
    print("📤 FRAME UPLOAD FOR CURRENT VIDEO")
    print("="*50)
    print("Upload your existing frames.zip to avoid re-extraction")
    print("="*50)
    
    print("\n📤 Please select your frames.zip file to upload...")
    uploaded = files.upload()
    
    frames_dir = "/content/uploaded_frames"
    os.makedirs(frames_dir, exist_ok=True)
    
    # Extract uploaded frames
    for filename in uploaded.keys():
        if filename.endswith('.zip'):
            print(f"\n📦 Extracting {filename}...")
            with zipfile.ZipFile(filename, 'r') as zip_ref:
                zip_ref.extractall(frames_dir)
            print(f"✅ Extracted frames from {filename}")
            
            # List extracted frames
            frame_files = [f for f in os.listdir(frames_dir) if f.endswith(('.jpg', '.png'))]
            frame_files.sort()
            
            print(f"\n📸 Found {len(frame_files)} frame files")
            print("First 5 frames:", frame_files[:5])
            print("Last 5 frames:", frame_files[-5:])
            
            # Verify sequential ordering
            print("\n🔍 Verifying frame sequence...")
            for i in range(min(10, len(frame_files))):
                print(f"  Frame {i+1}: {frame_files[i]}")
            
            return frames_dir, frame_files
    
    print("❌ No valid zip file uploaded")
    return None, []

# Cell for fixing frame naming/ordering issues
def fix_frame_ordering(frames_dir):
    """Fix frame naming and ordering issues"""
    import os
    import re
    
    print("🔧 FIXING FRAME ORDERING")
    print("="*40)
    
    if not os.path.exists(frames_dir):
        print("❌ Frames directory not found")
        return
    
    frame_files = [f for f in os.listdir(frames_dir) if f.endswith(('.jpg', '.png'))]
    print(f"Found {len(frame_files)} frames to reorder")
    
    # Extract frame numbers and sort
    frame_data = []
    for frame in frame_files:
        # Try to extract number from filename
        numbers = re.findall(r'\d+', frame)
        if numbers:
            frame_num = int(numbers[0])
            frame_data.append((frame_num, frame))
    
    # Sort by frame number
    frame_data.sort(key=lambda x: x[0])
    
    # Rename frames sequentially
    print("\n📝 Renaming frames sequentially...")
    for i, (orig_num, orig_name) in enumerate(frame_data):
        old_path = os.path.join(frames_dir, orig_name)
        new_name = f"frame_{i:04d}.jpg"
        new_path = os.path.join(frames_dir, new_name)
        
        if old_path != new_path:
            os.rename(old_path, new_path)
            print(f"  Renamed: {orig_name} → {new_name}")
    
    print(f"\n✅ Reordered {len(frame_data)} frames")
    return frame_data

# =============================================================================
# FINAL SUMMARY AND USAGE INSTRUCTIONS
# =============================================================================

def print_usage_instructions():
    """Print comprehensive usage instructions"""
    print("\n" + "="*80)
    print("COMPLETE ENHANCED FORENSIC TRANSCRIPTION PIPELINE - USAGE GUIDE")
    print("="*80)
    
    print("\n📋 BASIC USAGE:")
    print("1. Update video file_id and filename in Cell 2")
    print("2. Update API keys in Cell 3")
    print("3. Run cells 1-7 sequentially")
    
    print("\n📸 FOR REUSING EXISTING FRAMES:")
    print("1. After Cell 5, run: frames_dir, frames = upload_and_process_existing_frames()")
    print("2. Use the dual frame analysis function for double temporal resolution")
    
    print("\n⚡ RATE LIMIT HANDLING:")
    print("- Pipeline includes 20-second delays between chunks")
    print("- If rate limited, use recovery functions:")
    print("  • recover_failed_analysis()")
    print("  • export_for_external_analysis()")
    print("  • process_single_chunk_manually(chunk_num)")
    
    print("\n🔧 TROUBLESHOOTING:")
    print("- Frame ordering issues: fix_frame_ordering(frames_dir)")
    print("- Partial analysis: recover_partial_analysis()")
    print("- Check data: check_transcript_data()")
    
    print("\n📥 OUTPUT FILES:")
    print("All outputs download progressively throughout execution:")
    print("- whisper_transcription.json")
    print("- SPEAKER_IDENTIFIED_TRANSCRIPT.txt")
    print("- visual_frame_analysis.json")
    print("- frames_batch_*.zip")
    print("- transcript_chunks.json")
    print("- analysis_chunks_*.json")
    print("- COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt")
    
    print("\n" + "="*80)

# Print instructions when module loads
print_usage_instructions()

print("\n✅ COMPLETE PIPELINE FULLY RESTORED!")
print("📊 Total restoration: 165KB+ of functionality")
print("🎯 All original features + all enhancements implemented")
print("🚀 Ready for use in Google Colab")

# =============================================================================
# END OF COMPLETE RESTORED ENHANCED PIPELINE
# =============================================================================