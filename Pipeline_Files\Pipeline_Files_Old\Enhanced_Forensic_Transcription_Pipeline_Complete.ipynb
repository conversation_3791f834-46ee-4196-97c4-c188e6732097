{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# COMPLE<PERSON> ENHANCED FORENSIC TRANSCRIPTION PIPELINE", "", "## Instructions for Processing Multiple Videos", "", "This pipeline is designed to process videos one at a time in Google Colab.", "", "### TO PROCESS EACH VIDEO:", "1. <PERSON> Cells 1-6 once at the beginning of your session", "2. For each video:", "   - Update Cell 2 with new file_id and video_filename", "   - Update Cell 7 skip_seconds parameter if needed (default is 30)", "   - Run Cell 2 to download the new video", "   - Run Cell 7 to process and analyze", "   - The output file will download automatically when complete", "", "### SKIP_SECONDS PARAMETER:", "- Default: 30 seconds (for videos with initial silence/muted sections)", "- Set to 0 for videos that start immediately with audio", "- Adjust as needed based on each video's characteristics", "", "### CLEARING OUTPUTS:", "- To keep notebook tidy: Edit → Clear all outputs", "- This does NOT require re-running setup cells", "- Only restart runtime if you encounter memory errors", "", "### NOTE: ", "- The pipeline uses OpenAI API (gpt-4o for vision, gpt-4 for text)", "- Make sure your API key has sufficient credits for processing", "- You do NOT need to restart runtime between videos unless you encounter memory errors"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": [], "collapsed_sections": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 0}