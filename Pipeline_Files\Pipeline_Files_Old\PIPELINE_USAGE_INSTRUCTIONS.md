# Enhanced Forensic Transcription Pipeline - Usage Instructions

## System Requirements
- Google Colab with T4 GPU and High RAM
- OpenAI API key with credits for gpt-4o and gpt-4
- Hugging<PERSON><PERSON> token with access to pyannote models
- Google Drive with video files

## Initial Setup (Run Once Per Session)
1. Open the pipeline in Google Colab
2. Ensure runtime is set to T4 GPU with High RAM
3. Run Cell 1 to install all dependencies
4. Run Cell 3 to set up authentication (update API keys if needed)
5. Run Cell 4 to load all analysis functions
6. Run Cell 5 to load speaker diarization pipeline
7. Run Cell 6 to prepare the main processing function

## Processing Individual Videos

### For Each Video:
1. **Update Cell 2** with:
   ```python
   file_id = 'YOUR_GOOGLE_DRIVE_FILE_ID'  # Get from Google Drive share link
   video_filename = 'your_video_name.mp4'  # Descriptive filename
   ```

2. **Run Cell 2** to download the video from Google Drive

3. **Update Cell 7** with appropriate skip_seconds:
   ```python
   SKIP_SECONDS = 30  # Default for videos with muted beginnings
   # or
   SKIP_SECONDS = 0   # For videos that start immediately
   # or
   SKIP_SECONDS = 45  # Custom value based on video
   ```

4. **Run Cell 7** to process the video

5. **The output file downloads automatically** when processing completes
   - File downloads to your browser's default download folder
   - No manual intervention needed

## Skip Seconds Guidelines
- **30 seconds (default)**: Most body camera videos with standard pre-roll
- **0 seconds**: Videos that begin immediately with relevant audio
- **Custom value**: Preview video to determine when actual content begins

## Example Workflow for Multiple Videos

### Video 1 (with 30-second mute):
```python
# Cell 2:
file_id = '1URiqv_ve0cSj0Uj6N2Qzoih8L4izqbMR'
video_filename = 'Officer_Smith_Traffic_Stop_01.mp4'
# Run Cell 2

# Cell 7:
SKIP_SECONDS = 30
# Run Cell 7
```

### Video 2 (no initial mute):
```python
# Cell 2:
file_id = '1AnotherGoogleDriveFileID'
video_filename = 'Bodycam_Incident_Report_02.mp4'
# Run Cell 2

# Cell 7:
SKIP_SECONDS = 0
# Run Cell 7
```

### Video 3 (45-second pre-roll):
```python
# Cell 2:
file_id = '1YetAnotherFileID'
video_filename = 'Deputy_Johnson_Response_03.mp4'
# Run Cell 2

# Cell 7:
SKIP_SECONDS = 45
# Run Cell 7
```

## Output Files
Each video generates a comprehensive analysis file named:
`COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt`

Rename each output file after downloading to match the video:
- `Officer_Smith_Traffic_Stop_01_ANALYSIS.txt`
- `Bodycam_Incident_Report_02_ANALYSIS.txt`
- etc.

## Performance Tips
1. Process videos sequentially, not in parallel
2. Monitor GPU memory usage in Colab
3. If memory issues occur, restart runtime between videos
4. Expect 10-30 minutes processing time per video depending on length

## Troubleshooting

### "Model gpt-4-vision-preview deprecated" Error
- The pipeline already uses gpt-4o (resolved)
- Ensure openai==0.28.1 is installed

### GPU Memory Errors
- Clear outputs first (Edit → Clear all outputs) - this just cleans the display
- If error persists, then restart runtime (Runtime → Restart runtime)
- After runtime restart, you MUST re-run cells 1, 3, 4, 5, 6
- Ensure only one video is processed at a time

### Normal Operation Between Videos
- You do NOT need to restart runtime
- You do NOT need to re-run setup cells (1, 3, 4, 5, 6)
- Optionally clear outputs (Edit → Clear all outputs) to keep notebook tidy
- Just update Cell 2 and Cell 7 for each new video

### NER Model Loading Issues
- The pipeline will continue without officer name extraction
- Check internet connection for model download

### API Rate Limits
- Add delays between videos if needed
- Monitor OpenAI usage dashboard
- Consider processing in batches with breaks

## API Usage Estimates
Per 30-minute video:
- Whisper transcription: ~5-10 minutes GPU time
- Frame analysis: ~30-50 GPT-4o vision calls
- Legal analysis: ~2-3 GPT-4 text calls
- Total cost: Varies by video length and complexity