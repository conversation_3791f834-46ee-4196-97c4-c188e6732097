{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "machine_shape": "hm", "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "ZGdk7cPe3os7"}, "outputs": [], "source": ["# COMPLE<PERSON> ENHANCED FORENSIC TRANSCRIPTION PIPELINE"]}, {"cell_type": "code", "source": ["# =============================================================================\n", "# Cell 1: Install Dependencies with Correct Versions\n", "# =============================================================================\n", "# Google Colab + WhisperX + CUDA 11.8 + Pyannote + Whisper Large-v3 Setup\n", "# Optimized for T4 GPU and High RAM\n", "\n", "!pip install -q PyDrive2\n", "!pip install -q git+https://github.com/openai/whisper.git\n", "!pip install -q git+https://github.com/pyannote/pyannote-audio.git\n", "!pip install -q huggingface_hub\n", "!pip install -q openai==0.28.1  # Specific version for compatibility\n", "!pip install -q librosa\n", "!pip install -q torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118\n", "!pip install -q scikit-learn\n", "!pip install -q opencv-python\n", "!pip install -q Pillow\n", "!pip install -U transformers  # For BERT NER\n", "!pip install -q seqeval  # For NER evaluation\n", "\n", "print(\"✅ All dependencies installed successfully!\")"], "metadata": {"id": "alhO8kUgloS8"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# =============================================================================\n", "# Cell 2: Download Video File from Google Drive (UPDATE FOR EACH NEW VIDEO)\n", "# =============================================================================\n", "from pydrive2.auth import GoogleAuth\n", "from pydrive2.drive import GoogleDrive\n", "from google.colab import auth\n", "from oauth2client.client import GoogleCredentials\n", "\n", "auth.authenticate_user()\n", "gauth = GoogleAuth()\n", "gauth.credentials = GoogleCredentials.get_application_default()\n", "drive = GoogleDrive(gauth)\n", "\n", "# 🔄 UPDATE THESE LINES FOR EACH NEW VIDEO:\n", "file_id = '1URiqv_ve0cSj0Uj6N2Qzoih8L4izqbMR'  # ← CHANGE THIS\n", "video_filename = 'Redacted_20__MENTALLY_DISTURBED_PERSON_3.mp4'  # ← CHANGE THIS\n", "\n", "downloaded = drive.CreateFile({'id': file_id})\n", "downloaded.GetContentFile(video_filename)\n", "print(f\"✅ Video file downloaded: {video_filename}\")"], "metadata": {"id": "HfLtIMdg3sSg"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# =============================================================================\n", "# Cell 3: Authentication Setup\n", "# =============================================================================\n", "from huggingface_hub import login\n", "import openai\n", "\n", "# 🔑 UPDATE YOUR API KEYS HERE:\n", "HF_TOKEN = \"*************************************\"  # ← CHANGE THIS\n", "OPENAI_API_KEY = \"********************************************************************************************************************************************************************\"  # ← CHANGE THIS\n", "\n", "login(token=HF_TOKEN)\n", "openai.api_key = OPENAI_API_KEY\n", "\n", "print(\"✅ Authentication complete\")"], "metadata": {"id": "nCVYkNZM3sVG"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# =============================================================================\n", "# Cell 4: Enhanced Forensic Pipeline Setup WITH ALL IMPROVEMENTS\n", "# =============================================================================\n", "import os\n", "import torch\n", "import whisper\n", "import subprocess\n", "import librosa\n", "import numpy as np\n", "import json\n", "import hashlib\n", "import time\n", "from datetime import datetime, timedelta\n", "from pyannote.audio import Pipeline\n", "from sklearn.cluster import KMeans\n", "import base64\n", "import cv2\n", "from PIL import Image\n", "from transformers import AutoTokenizer, AutoModelForTokenClassification, pipeline  # For BERT NER\n", "from google.colab import files\n", "\n", "import torch\n", "# Suppress TF32 warning\n", "torch.backends.cuda.matmul.allow_tf32 = True\n", "torch.backends.cudnn.allow_tf32 = True\n", "\n", "# Check GPU availability\n", "device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "print(f\"Using device: {device}\")\n", "\n", "# =============================================================================\n", "# FORENSIC-G<PERSON>DE ANALYSIS CLARIFICATION\n", "# =============================================================================\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"FORENSIC-GRADE ANALYSIS CLARIFICATION\")\n", "print(\"=\"*80)\n", "print(\"\\n📋 IMPORTANT DISTINCTION:\")\n", "print(\"\\n'Forensic-Grade' refers to the QUALITY and PRECISION of our analysis,\")\n", "print(\"NOT the legal admissibility or certification status.\")\n", "print(\"\\n🎯 This means:\")\n", "print(\"• Meticulous attention to detail and accuracy\")\n", "print(\"• Comprehensive documentation of all findings\")\n", "print(\"• Objective, unbiased analysis methodology\")\n", "print(\"• Preservation of all evidence and timestamps\")\n", "print(\"• Professional-level reporting standards\")\n", "print(\"\\n⚖️ Legal Note: While this analysis follows forensic best practices,\")\n", "print(\"formal legal admissibility requires proper chain of custody and\")\n", "print(\"certification by qualified forensic experts.\")\n", "print(\"\\n\" + \"=\"*80 + \"\\n\")\n", "\n", "# Enhanced legal trigger words for forensic analysis - INCLUDING NEW WORDS\n", "LEGAL_TRIGGER_WORDS = [\n", "    \"arrest\", \"detained\", \"miranda\", \"rights\", \"warrant\", \"search\", \"seizure\",\n", "    \"consent\", \"constitutional\", \"fourth amendment\", \"fifth amendment\",\n", "    \"baker act\", \"mental health\", \"crisis\", \"suicide\", \"self harm\",\n", "    \"force\", \"taser\", \"pepper spray\", \"baton\", \"firearm\", \"weapon\",\n", "    \"assault\", \"battery\", \"resistance\", \"compliance\", \"cooperation\",\n", "    \"medical\", \"injury\", \"pain\", \"breathing\", \"unconscious\", \"responsive\",\n", "    \"supervisor\", \"sergeant\", \"lieutenant\", \"backup\", \"ambulance\", \"ems\",\n", "    \"lawsuit\", \"carolina\", \"palm beach\", \"officer\", \"sheriff\", \"5150\",\n", "    \"order\", \"refusal\", \"psych\", \"RPO\", \"sane\", \"suicidal\", \"husband\",\n", "    \"combative\", \"harold\", \"hastings\", \"gun\", \"shotgun\", \"welfare\", \"lucid\",\n", "    \"hands up\", \"get down\", \"stop resisting\", \"calm down\", \"relax\",\n", "    \"towel\", \"naked\", \"undressed\", \"barefoot\", \"wet\", \"shower\", \"bathroom\",\n", "    \"cuff\", \"cuffs\", \"handcuff\", \"handcuffed\", \"restrained\", \"dignity\",\n", "    \"humiliate\", \"embarrass\", \"film\", \"recording\", \"camera\", \"mute\",\n", "    \"cover\", \"blanket\", \"sheet\", \"expose\", \"exposure\", \"neighbors\",\n", "    \"crowd\", \"public\", \"private\", \"home\", \"residence\", \"emergency\",\n", "    \"interrupted\", \"rushed\", \"swat\", \"tactical\", \"escalate\", \"de-escalate\"\n", "]\n", "\n", "# Legal case law references\n", "CASE_LAW_REFERENCES = {\n", "    \"<PERSON> v<PERSON>\": \"490 U.S. 386 (1989) - Use of force analysis\",\n", "    \"Tennessee v<PERSON> <PERSON>\": \"471 U.S. 1 (1985) - Deadly force standards\",\n", "    \"Payton v. New York\": \"445 U.S. 573 (1980) - Warrantless home entry\",\n", "    \"Kentucky v. King\": \"563 U.S. 452 (2011) - Exigent circumstances\",\n", "    \"York v. <PERSON>\": \"324 F.2d 450 (9th Cir. 1963) - Privacy dignity violations\",\n", "    \"Jordan v<PERSON>\": \"986 F.2d 1521 (9th Cir. 1993) - Cross-gender searches\",\n", "    \"Bell v<PERSON>\": \"441 U.S. 520 (1979) - Detention conditions\",\n", "    \"<PERSON><PERSON> v. <PERSON>\": \"457 U.S. 307 (1982) - Mental health detainees\"\n", "}\n", "\n", "def enhanced_audio_processing_for_difficult_sections(input_path, output_path):\n", "    \"\"\"Multi-pass audio enhancement for challenging sections\"\"\"\n", "    print(\"🔊 Enhanced audio processing for difficult sections...\")\n", "\n", "    # Pass 1: Normalize volume and compress dynamic range for distant speakers\n", "    pass1_path = \"/content/audio_pass1.wav\"\n", "    cmd1 = [\n", "        'ffmpeg', '-y', '-i', input_path,\n", "        '-af', 'dynaudnorm=p=0.9:s=5,compand=attacks=0.1:decays=0.5:points=-90/-90|-60/-40|-40/-25|-25/-15|-10/-10',\n", "        '-acodec', 'pcm_s16le', '-ar', '16000', '-ac', '1',\n", "        pass1_path\n", "    ]\n", "    subprocess.run(cmd1, capture_output=True)\n", "\n", "    # Pass 2: Enhance speech frequencies and reduce background noise\n", "    pass2_path = \"/content/audio_pass2.wav\"\n", "    cmd2 = [\n", "        'ffmpeg', '-y', '-i', pass1_path,\n", "        '-af', 'highpass=f=80,lowpass=f=8000,equalizer=f=2000:width_type=h:width=1000:g=3',\n", "        '-acodec', 'pcm_s16le',\n", "        pass2_path\n", "    ]\n", "    subprocess.run(cmd2, capture_output=True)\n", "\n", "    # Pass 3: Handle loud shouting and volume spikes\n", "    cmd3 = [\n", "        'ffmpeg', '-y', '-i', pass2_path,\n", "        '-af', 'alimiter=level_in=1:level_out=0.8:limit=0.9,volume=1.5',\n", "        '-acodec', 'pcm_s16le',\n", "        output_path\n", "    ]\n", "    subprocess.run(cmd3, capture_output=True)\n", "\n", "    print(f\"✅ Enhanced audio saved: {output_path}\")\n", "\n", "def transcribe_with_maximum_accuracy_enhanced(audio_path, language=\"en\"):\n", "    \"\"\"Enhanced Whisper transcription\"\"\"\n", "    print(\"🎙️ Loading Whisper Large-v3 for maximum accuracy...\")\n", "\n", "    import whisper\n", "    model = whisper.load_model(\"large-v3\", device=device)\n", "\n", "    print(\"🔄 Transcribing with enhanced settings...\")\n", "    result = model.transcribe(\n", "        audio_path,\n", "        language=\"en\",\n", "        word_timestamps=True,\n", "        verbose=False\n", "    )\n", "\n", "    print(f\"✅ Transcription complete: {len(result['text'])} characters\")\n", "    return result\n", "\n", "def analyze_video_frames_for_context(video_path, skip_seconds=30):\n", "    \"\"\"Extract and analyze video frames for visual context with GPT-4 Vision - WITH ENHANCED RESTRAINT ANALYSIS\"\"\"\n", "    print(\"📹 Analyzing video frames for visual context...\")\n", "\n", "    # Extract key frames every 30 seconds\n", "    frames_dir = \"/content/video_frames\"\n", "    os.makedirs(frames_dir, exist_ok=True)\n", "\n", "    extract_frames_cmd = [\n", "        'ffmpeg', '-y', '-ss', str(skip_seconds), '-i', video_path,\n", "        '-vf', 'fps=1/20',  # One frame every 20 seconds\n", "        '-q:v', '2',  # High quality\n", "        f'{frames_dir}/frame_%04d.jpg'\n", "    ]\n", "\n", "    subprocess.run(extract_frames_cmd, capture_output=True)\n", "\n", "    # Analyze frames with GPT-4 Vision\n", "    frame_files = sorted([f for f in os.listdir(frames_dir) if f.endswith('.jpg')])\n", "    visual_context = []\n", "    frame_cache = {}  # Stores frame_path → base64\n", "\n", "    print(f\"🔍 Analyzing {len(frame_files)} video frames...\")\n", "\n", "    for i, frame_file in enumerate(frame_files):\n", "        frame_path = os.path.join(frames_dir, frame_file)\n", "        timestamp = (i * 20) + skip_seconds  # Calculate actual timestamp (20 sec intervals)\n", "\n", "        # Encode frame for GPT-4 Vision\n", "        try:\n", "            if frame_path in frame_cache:\n", "                frame_data = frame_cache[frame_path]\n", "            else:\n", "                with open(frame_path, 'rb') as f:\n", "                   frame_data = base64.b64encode(f.read()).decode()\n", "                   frame_cache[frame_path] = frame_data\n", "\n", "            response = openai.ChatCompletion.create(\n", "                model=\"gpt-4o\",  # Using gpt-4o instead of deprecated gpt-4-vision-preview\n", "                messages=[\n", "                    {\n", "                        \"role\": \"user\",\n", "                        \"content\": [\n", "                            {\n", "                                \"type\": \"text\",\n", "                                \"text\": \"\"\"Analyze this police bodycam frame for forensic documentation. Provide detailed analysis of:\n", "\n", "1. SCENE SETTING: Location type, environment, lighting conditions, etc.\n", "2. PEOPLE VISIBLE: Number of individuals, their positions, actions, posture, clothing, etc.\n", "3. EQUIPMENT/EVIDENCE: Weapons, vehicles, medical equipment, evidence items, etc.\n", "4. TACTICAL POSITIONING: Officer formation, civilian positioning, spatial dynamics, threat levels, etc.\n", "5. EMOTIONAL INDICATORS: Body language, gestures, apparent stress levels, emotional reactions, etc.\n", "6. SAFETY CONCERNS: Potential hazards, weapons visible, environmental risks, threat levels, etc.\n", "7. LEGAL SIGNIFICANCE: Constitutional issues, use of force implications, breach of procedures, escalation, deescalation, evidence preservation, etc.\n", "8. ATTIRE ANALYSIS (CRITICAL FOR PRIVACY ASSESSMENT):\n", "   - Clothing status: Fully dressed, partially dressed, minimal clothing, towel only, etc.\n", "   - State of dress: Appropriate, inappropriate for public, emergency exit clothing\n", "   - Signs of rushed dressing: Incomplete clothing, towel wrapped, wet appearance\n", "   - Modesty concerns: Areas of body exposed, coverage inadequacy\n", "   - Footwear: Barefoot, shoes, slippers (indicates emergency exit)\n", "\n", "9. PRIVACY & DIGNITY INDICATORS:\n", "   - Public exposure level: Private home vs. public view\n", "   - Bystander presence: Neighbors, crowds, passersby witnessing exposure\n", "   - Recording implications: Subject aware of being filmed in state of undress\n", "   - Weather conditions affecting minimal clothing exposure\n", "\n", "10. EMERGENCY/CRISIS INDICATORS:\n", "   - Wet hair/body (shower interruption)\n", "   - Rushed appearance (hastily grabbed clothing/towel)\n", "   - Bathroom/shower context (wet floors, steam, towels visible)\n", "   - Time pressure indicators (incomplete dressing)\n", "\n", "11. RESTRAINT/HANDCUFFING ANALYSIS:\n", "   - Handcuff application on subject in minimal clothing\n", "   - Positioning: hands behind back while in towel/minimal clothing\n", "   - Dignity concerns during restraint application\n", "   - Cooperative behavior vs. restraint necessity\n", "\n", "12. STANDARD FORENSIC ELEMENTS:\n", "   - Scene setting and location context\n", "   - People positions and actions\n", "   - Equipment and evidence visible\n", "   - Officer positioning relative to undressed subject\n", "   - Safety and tactical considerations\n", "\n", "13. CONSTITUTIONAL CONCERNS:\n", "   - 4th Amendment: Privacy expectations in home\n", "   - 8th Amendment: Dignity during detention\n", "   - Public exposure creating humiliation\n", "   - Reasonable accommodation for clothing needs\n", "\n", "Be specific, objective, and forensically precise. Use timestamps and positional references. Be extremely specific about clothing status and privacy implications. Flag any dignitary concerns or inappropriate exposure situations. Document if subject appears to have been interrupted during private activities (bathing, dressing, etc.).\"\"\"\n", "                            },\n", "                            {\n", "                                \"type\": \"image_url\",\n", "                                \"image_url\": {\n", "                                    \"url\": f\"data:image/jpeg;base64,{frame_data}\"\n", "                                }\n", "                            }\n", "                        ]\n", "                    }\n", "                ],\n", "                max_tokens=1500,\n", "                temperature=0.1\n", "            )\n", "\n", "            visual_analysis = response.choices[0].message.content\n", "            visual_context.append({\n", "                'timestamp': timestamp,\n", "                'frame': frame_file,\n", "                'analysis': {\n", "                    'raw_text': visual_analysis,\n", "                    'scene_setting': None,\n", "                    'privacy': None,\n", "                    'emergency_flags': [],\n", "                }\n", "            })\n", "\n", "            print(f\"✅ Enhanced frame analysis - Frame analyzed: {timestamp//60:02d}:{timestamp%60:02d}\")\n", "\n", "        except Exception as e:\n", "            print(f\"⚠️ Frame analysis failed for {frame_file}: {e}\")\n", "            visual_context.append({\n", "                'timestamp': timestamp,\n", "                'frame': frame_file,\n", "                'analysis': f\"Visual analysis unavailable: {e}\"\n", "            })\n", "\n", "    print(f\"✅ Enhanced visual context analysis complete: {len(visual_context)} frames\")\n", "    return visual_context\n", "\n", "def detect_speaker_overlaps_and_separate_enhanced(audio_path, diarization_result, whisper_result):\n", "    \"\"\"Enhanced speaker overlap detection with better sensitivity\"\"\"\n", "    print(\"👥 Enhanced speaker overlap detection...\")\n", "\n", "    overlaps = []\n", "\n", "    # Convert diarization to list of segments\n", "    diar_segments = []\n", "    for turn, _, speaker in diarization_result.itertracks(yield_label=True):\n", "        diar_segments.append({\n", "            'start': turn.start,\n", "            'end': turn.end,\n", "            'speaker': speaker\n", "        })\n", "\n", "    # Find overlapping segments with enhanced sensitivity\n", "    for i, seg1 in enumerate(diar_segments):\n", "        for seg2 in diar_segments[i+1:]:\n", "            # Check for overlap\n", "            overlap_start = max(seg1['start'], seg2['start'])\n", "            overlap_end = min(seg1['end'], seg2['end'])\n", "\n", "            if overlap_start < overlap_end:\n", "                duration = overlap_end - overlap_start\n", "                if duration > 0.4:  # Lowered threshold from 0.5 to catch more overlaps\n", "                    overlaps.append({\n", "                        'start': overlap_start,\n", "                        'end': overlap_end,\n", "                        'duration': duration,\n", "                        'speakers': [seg1['speaker'], seg2['speaker']]\n", "                    })\n", "\n", "    print(f\"✅ Enhanced overlap detection complete: {len(overlaps)} overlaps found\")\n", "    return overlaps\n", "\n", "def format_overlap_readable(overlap, whisper_result):\n", "    start = overlap['start']\n", "    end = overlap['end']\n", "    speakers = overlap['speakers']\n", "    timestamp = f\"[{int(start//60):02}:{int(start%60):02}–{int(end//60):02}:{int(end%60):02}]\"\n", "\n", "    lines_by_speaker = {s: [] for s in speakers}\n", "\n", "    for seg in whisper_result['segments']:\n", "        if seg['start'] >= start and seg['end'] <= end:\n", "            speaker = seg.get('speaker', 'UNKNOWN')\n", "            if speaker in lines_by_speaker:\n", "                lines_by_speaker[speaker].append(seg['text'].strip())\n", "\n", "    output = f\"{timestamp} **OVERLAP** {tuple(speakers)}:\\n\"\n", "    for speaker, lines in lines_by_speaker.items():\n", "        if lines:\n", "            joined = ' '.join(lines)\n", "            output += f\"{speaker}: {joined}\\n\"\n", "\n", "    return output.strip()\n", "\n", "def combine_transcription_and_speakers_enhanced(whisper_result, diarization_result, overlaps):\n", "    \"\"\"Enhanced combination with better word-level speaker assignment\"\"\"\n", "    print(\"🔗 Enhanced transcription and speaker combination...\")\n", "\n", "    enhanced_transcript = []\n", "\n", "    # Process each word from <PERSON><PERSON><PERSON> with enhanced speaker matching\n", "    for segment in whisper_result['segments']:\n", "        for word_info in segment.get('words', []):\n", "            word_start = word_info['start']\n", "            word_end = word_info['end']\n", "            word_text = word_info['word']\n", "            word_confidence = word_info.get('probability', 0.0)\n", "\n", "            # Find speaker(s) for this word with tolerance\n", "            speakers = []\n", "            tolerance = 0.1  # 100ms tolerance for better matching\n", "\n", "            for turn, _, speaker in diarization_result.itertracks(yield_label=True):\n", "                if (turn.start - tolerance) <= word_start <= (turn.end + tolerance):\n", "                    speakers.append(speaker)\n", "\n", "            # Check for overlaps\n", "            is_overlap = False\n", "            overlap_speakers = []\n", "            for overlap in overlaps:\n", "                if overlap['start'] <= word_start <= overlap['end']:\n", "                    is_overlap = True\n", "                    overlap_speakers = overlap['speakers']\n", "                    break\n", "\n", "            enhanced_transcript.append({\n", "                'word': word_text,\n", "                'start': word_start,\n", "                'end': word_end,\n", "                'confidence': word_confidence,\n", "                'speakers': speakers,\n", "                'overlap': is_overlap,\n", "                'overlap_speakers': overlap_speakers\n", "            })\n", "\n", "    print(f\"✅ Enhanced transcript created: {len(enhanced_transcript)} words\")\n", "    enhanced_transcript.sort(key=lambda x: x['start'])\n", "    return enhanced_transcript\n", "\n", "def analyze_with_gpt4_forensic_enhanced(transcript_text, speaker_segments, trigger_words, visual_context):\n", "    \"\"\"Enhanced GPT-4 forensic analysis incorporating both audio and visual data\"\"\"\n", "    print(\"🧠 Running enhanced GPT-4 forensic analysis...\")\n", "\n", "    # Combine visual context for analysis\n", "    visual_summary = \"\\n\".join([\n", "        f\"[{ctx['timestamp']//60:02d}:{ctx['timestamp']%60:02d}] VISUAL: {ctx['analysis']}\"\n", "        for ctx in visual_context[:10]  # Include first 10 visual analyses\n", "    ])\n", "\n", "    system_prompt = \"\"\"You are a certified forensic audiovisual analyst with 25+ years experience in criminal procedure, constitutional law (42 U.S.C. § 1983), and police misconduct analysis. You have served as a court-appointed expert witness and specialize in integrated audio-visual evidence analysis.\n", "\n", "Conduct comprehensive forensic analysis incorporating both audio transcript and visual frame analysis for:\n", "\n", "1. CONSTITUTIONAL VIOLATIONS:\n", "   - 4th Amendment (search/seizure without warrant)\n", "   - 5th Amendment (Miranda rights, self-incrimination)\n", "   - 8th Amendment (excessive force, cruel treatment)\n", "   - 14th Amendment (due process, equal protection)\n", "\n", "2. STATUTORY VIOLATIONS:\n", "   - Florida Statutes (Baker Act § 394.463)\n", "   - Arrest authority compliance (Ch. 901)\n", "   - Mental health detention protocols\n", "   - Transport and medical clearance requirements\n", "\n", "3. PROCEDURAL BREACHES:\n", "   - Required warnings not given\n", "   - Supervisor notification failures\n", "   - Medical clearance timing violations\n", "   - Evidence preservation protocols\n", "\n", "4. USE OF FORCE ASSESSMENT:\n", "   - <PERSON> v. Connor standards compliance\n", "   - Proportionality analysis (visual evidence critical)\n", "   - De-escalation attempts/failures\n", "   - Weapon deployment justification\n", "\n", "5. AUDIO-VISUAL CORRELATION:\n", "   - Consistency between spoken actions and visual evidence\n", "   - Body language vs verbal compliance\n", "   - Environmental factors affecting behavior\n", "   - Officer positioning and tactical decisions\n", "\n", "6. PSYCHOLOGICAL MARKERS:\n", "   - Mental health crisis indicators (audio + visual)\n", "   - Stress escalation patterns\n", "   - Compliance vs resistance behaviors\n", "   - Environmental stressors\n", "   - Mental health interventions\n", "\n", "7. ATTIRE ANALYSIS (CRITICAL FOR PRIVACY ASSESSMENT):\n", "   - Clothing status: Fully dressed, partially dressed, minimal clothing, towel only, etc.\n", "   - State of dress: Appropriate, inappropriate for public, emergency exit clothing\n", "   - Signs of rushed dressing: Incomplete clothing, towel wrapped, wet appearance\n", "   - Modesty concerns: Areas of body exposed, coverage inadequacy\n", "   - Footwear: Barefoot, shoes, slippers (indicates emergency exit)\n", "\n", "8. PRIVACY & DIGNITY INDICATORS:\n", "   - Public exposure level: Private home vs. public view\n", "   - Bystander presence: Neighbors, crowds, passersby witnessing exposure\n", "   - Recording implications: Subject aware of being filmed in state of undress\n", "   - Weather conditions affecting minimal clothing exposure\n", "\n", "9. EMERGENCY/CRISIS INDICATORS:\n", "   - Wet hair/body (shower interruption)\n", "   - Rushed appearance (hastily grabbed clothing/towel)\n", "   - Bathroom/shower context (wet floors, steam, towels visible)\n", "   - Time pressure indicators (incomplete dressing)\n", "\n", "10. RESTRAINT/HANDCUFFING ANALYSIS:\n", "   - Handcuff application on subject in minimal clothing\n", "   - Positioning: hands behind back while in towel/minimal clothing\n", "   - Dignity concerns during restraint application\n", "   - Cooperative behavior vs. restraint necessity\n", "\n", "11. STANDARD FORENSIC ELEMENTS:\n", "   - Scene setting and location context\n", "   - People positions and actions\n", "   - Equipment and evidence visible\n", "   - Officer positioning relative to undressed subject\n", "   - Safety and tactical considerations\n", "\n", "12. CONSTITUTIONAL CONCERNS:\n", "   - 4th Amendment: Privacy expectations in home\n", "   - 8th Amendment: Dignity during detention\n", "   - Public exposure creating humiliation\n", "   - Reasonable accommodation for clothing needs\n", "\n", "Provide specific timestamps, direct quotes, visual observations, legal significance, and court-admissible analysis with integrated audio-visual evidence correlation. Be extremely specific about clothing status and privacy implications. Flag any dignitary concerns or inappropriate exposure situations. Document if subject appears to have been interrupted during private activities (showering, bathing, dressing, etc.).\"\"\"\n", "\n", "    user_prompt = f\"\"\"\n", "POLICE BODYCAM INTEGRATED AUDIO-VISUAL ANALYSIS:\n", "\n", "AUDIO TRANSCRIPT (First 8000 characters):\n", "{transcript_text[:8000]}\n", "\n", "VISUAL FRAME ANALYSIS:\n", "{visual_summary}\n", "\n", "LEGAL TRIGGER WORDS DETECTED:\n", "{', '.join(trigger_words)}\n", "\n", "SPEAKER COUNT: {len(set(seg.get('speaker', 'Unknown') for seg in speaker_segments))}\n", "\n", "Provide comprehensive integrated forensic analysis with:\n", "- Constitutional and statutory violations (cite specific evidence)\n", "- Critical timeline events with both audio and visual timestamps\n", "- Use of force analysis with visual evidence correlation\n", "- Risk assessment for legal proceedings\n", "- Evidence preservation recommendations\n", "- Audio-visual consistency analysis\n", "\"\"\"\n", "\n", "    try:\n", "        response = openai.ChatCompletion.create(\n", "            model=\"gpt-4o\",\n", "            messages=[\n", "                {\"role\": \"system\", \"content\": system_prompt},\n", "                {\"role\": \"user\", \"content\": user_prompt}\n", "            ],\n", "            max_tokens=4000,\n", "            temperature=0.05\n", "        )\n", "\n", "        return response.choices[0].message.content\n", "\n", "    except Exception as e:\n", "        print(f\"❌ GPT-4 analysis failed: {e}\")\n", "        return f\"GPT-4 analysis unavailable: {e}\"\n", "\n", "def inject_visual_context_into_transcript(enhanced_transcript, visual_context, skip_seconds=30):\n", "    \"\"\"Inject visual context annotations into transcript at appropriate timestamps\"\"\"\n", "    print(\"💉 Injecting visual context into transcript...\")\n", "\n", "    visual_injections = {}\n", "\n", "    # Map visual context to transcript timestamps\n", "    for ctx in visual_context:\n", "        visual_timestamp = ctx['timestamp']\n", "\n", "        # Find the closest word in the transcript to inject visual context\n", "        closest_word_index = None\n", "        min_time_diff = float('inf')\n", "\n", "        for i, word_data in enumerate(enhanced_transcript):\n", "            word_timestamp = word_data['start'] + skip_seconds\n", "            time_diff = abs(word_timestamp - visual_timestamp)\n", "\n", "            if time_diff < min_time_diff and time_diff < 15:  # Within 15 seconds\n", "                min_time_diff = time_diff\n", "                closest_word_index = i\n", "\n", "        if closest_word_index is not None:\n", "            visual_injections[closest_word_index] = f\"*{{VISUAL CONTEXT: {ctx['analysis'][:200]}...}}*\"\n", "\n", "    print(f\"✅ Visual context injections prepared: {len(visual_injections)} injections\")\n", "    return visual_injections\n", "\n", "def inject_contextual_annotations_enhanced(enhanced_transcript):\n", "    \"\"\"Enhanced contextual legal/psychological annotations - WITH LIST SUPPORT\"\"\"\n", "    print(\"💉 Injecting enhanced contextual annotations...\")\n", "\n", "    annotations = {}\n", "\n", "    for i, word_data in enumerate(enhanced_transcript):\n", "        text = word_data.get('word', '').lower()\n", "\n", "        # Enhanced legal trigger detection\n", "        if any(word in text for word in ['miranda', 'rights', 'remain silent']):\n", "            annotations.setdefault(i, []).append(\"*{Miranda rights advisement - 5th Amendment constitutional requirement}*\")\n", "        elif any(word in text for word in ['force', 'taser', 'weapon', 'gun', 'swat']):\n", "            annotations.setdefault(i, []).append(\"*{Use of force deployment - <PERSON> v<PERSON> analysis required}*\")\n", "        elif any(word in text for word in ['baker act', 'mental health', 'crisis', '5150', 'behavioral health', 'rpo', 'risk protection', 'no blood', 'suicidal']):\n", "            annotations.setdefault(i, []).append(\"*{Mental health detention protocol - Fla. Stat. § 394.463}*\")\n", "        elif any(word in text for word in ['search', 'seizure', 'house', 'rpo', 'risk protection']):\n", "            annotations.setdefault(i, []).append(\"*{4th Amendment search/seizure activity - warrant requirement analysis}*\")\n", "        elif any(word in text for word in ['consent', 'permission', 'fine', 'baker act', 'take me anywhere', 'suicidal', 'detained', 'restrained', 'cuff', 'secure', 'clear', 'house', 'ask her']):\n", "            annotations.setdefault(i, []).append(\"*{Consent documentation - voluntariness analysis required}*\")\n", "        elif any(word in text for word in ['supervisor', 'sergeant', 'lieutenant', 'williams']):\n", "            annotations.setdefault(i, []).append(\"*{Supervisory involvement - chain of command protocol}*\")\n", "        elif any(word in text for word in ['ambulance', 'ems', 'medical', 'injury', 'rescue', 'no blood']):\n", "            annotations.setdefault(i, []).append(\"*{Medical intervention - duty of care assessment}*\")\n", "        elif any(word in text for word in ['hands up', 'get down', 'stop', 'walk backwards', 'face away', 'turn around']):\n", "            annotations.setdefault(i, []).append(\"*{Compliance directive - officer command analysis}*\")\n", "        elif any(word in text for word in ['calm down', 'relax', 'breathe', 'escalation,' 'embarrass', 'humiliate', 'neighbors']):\n", "            annotations.setdefault(i, []).append(\"*{De-escalation attempt - crisis intervention technique}*\")\n", "        elif any(word in text for word in ['escalation,' 'embarrass', 'humiliate', 'neighbors', 'swat', 'shotgun', 'cock', 'lethal', 'lethaly', 'go in', 'not leaving', 'assess the house']):\n", "            annotations.setdefault(i, []).append(\"*{Escalation attempts, behaviors, meneuvers, tactics - unwarranted and/or improper escalation analysis}*\")\n", "        elif any(word in text for word in ['escalate', 'embarrass', 'humiliate', 'neighbors', 'swat', 'towel', 'wet', 'cuff', 'cover her', 'sheet', 'game', 'games', 'song and dance', 'regardless', 'play']):\n", "            annotations.setdefault(i, []).append(\"*{Retaliatory and/or punitive  tactics - unwarranted and/or improper escalation analysis}*\")\n", "        elif any(word in text for word in ['escalate', 'embarrass', 'humiliate', 'neighbors', 'swat', 'towel', 'wet', 'cuff', 'cover her', 'sheet', 'dress', 'naked', 'cover me', 'filming', 'videoing', 'watching']):\n", "            annotations.setdefault(i, []).append(\"*{Humiliation/Dignity/Public shame activities, tactics, behaviors - analysis of intentional and/or unintentional plublic shame, humiliation, embarrassment, preservation of dignity activities}*\")\n", "        elif any(word in text for word in ['heads up', 'shotgun', 'baker act', 'heard you', \"don't tell\", \"don't say\", 'no blood', 'in front of', 'mute', 'blue', 'camera', 'teach', 'complaint', \"don't teach\", 'statement', 'report', 'concern']):\n", "            annotations.setdefault(i, []).append(\"*{Transparency cocerns, cover-up, coordination concerns - transparency and proper/improper disclosure assessment, narrative coordination/alignments discussions and/or behaviors, body-worn-camera muting and/or deactivation assesment, suspicious redaction assessment}*\")\n", "        elif any(word in text for word in ['cuff', 'cuffs', 'handcuff', 'handcuffed', 'restrained']):\n", "            annotations.setdefault(i, []).append(\"*{RESTRAINT APPLICATION: Handcuffing procedure - dignity and necessity analysis required}*\")\n", "        elif any(word in text for word in ['towel', 'naked', 'undressed', 'barefoot', 'wet', 'shower', 'bathroom']):\n", "            annotations.setdefault(i, []).append(\"*{ATTIRE CONCERN: Minimal clothing status - privacy and dignity implications}*\")\n", "\n", "    return annotations\n", "\n", "def analyze_transcript_confidence_metrics(enhanced_transcript):\n", "    \"\"\"Analyze confidence metrics for transcript accuracy\"\"\"\n", "    print(\"📊 Analyzing transcript confidence metrics...\")\n", "\n", "    confidence_stats = {\n", "        'high_confidence': 0,  # > 0.9\n", "        'medium_confidence': 0,  # 0.7 - 0.9\n", "        'low_confidence': 0,  # < 0.7\n", "        'total_words': len(enhanced_transcript),\n", "        'problematic_sections': []\n", "    }\n", "\n", "    current_low_conf_start = None\n", "    low_conf_count = 0\n", "\n", "    for i, word_data in enumerate(enhanced_transcript):\n", "        confidence = word_data.get('confidence', 0.0)\n", "\n", "        if confidence > 0.9:\n", "            confidence_stats['high_confidence'] += 1\n", "            # End low confidence section if we were tracking one\n", "            if current_low_conf_start and low_conf_count >= 5:\n", "                confidence_stats['problematic_sections'].append({\n", "                    'start_index': current_low_conf_start,\n", "                    'end_index': i-1,\n", "                    'word_count': low_conf_count,\n", "                    'timestamp': enhanced_transcript[current_low_conf_start]['start']\n", "                })\n", "            current_low_conf_start = None\n", "            low_conf_count = 0\n", "        elif confidence >= 0.7:\n", "            confidence_stats['medium_confidence'] += 1\n", "        else:\n", "            confidence_stats['low_confidence'] += 1\n", "            if current_low_conf_start is None:\n", "                current_low_conf_start = i\n", "            low_conf_count += 1\n", "\n", "    # Calculate percentages\n", "    total = confidence_stats['total_words']\n", "    confidence_stats['high_confidence_pct'] = (confidence_stats['high_confidence'] / total) * 100\n", "    confidence_stats['medium_confidence_pct'] = (confidence_stats['medium_confidence'] / total) * 100\n", "    confidence_stats['low_confidence_pct'] = (confidence_stats['low_confidence'] / total) * 100\n", "\n", "    print(f\"✅ Confidence analysis complete: {confidence_stats['high_confidence_pct']:.1f}% high confidence\")\n", "\n", "    return confidence_stats\n", "\n", "def generate_audio_quality_report(enhanced_transcript, overlaps, confidence_stats):\n", "    \"\"\"Generate detailed audio quality and transcription accuracy report\"\"\"\n", "\n", "    report = \"AUDIO QUALITY AND TRANSCRIPTION ACCURACY REPORT:\\n\"\n", "    report += \"=\"*50 + \"\\n\\n\"\n", "\n", "    report += \"OVERALL CONFIDENCE METRICS:\\n\"\n", "    report += f\"- High Confidence Words: {confidence_stats['high_confidence']} ({confidence_stats['high_confidence_pct']:.1f}%)\\n\"\n", "    report += f\"- Medium Confidence Words: {confidence_stats['medium_confidence']} ({confidence_stats['medium_confidence_pct']:.1f}%)\\n\"\n", "    report += f\"- Low Confidence Words: {confidence_stats['low_confidence']} ({confidence_stats['low_confidence_pct']:.1f}%)\\n\"\n", "    report += f\"- Total Words Analyzed: {confidence_stats['total_words']}\\n\\n\"\n", "\n", "    report += f\"SPEAKER OVERLAP INCIDENTS: {len(overlaps)}\\n\"\n", "    report += f\"PROBLEMATIC SECTIONS: {len(confidence_stats['problematic_sections'])}\\n\\n\"\n", "\n", "    if confidence_stats['problematic_sections']:\n", "        report += \"LOW CONFIDENCE SECTIONS REQUIRING REVIEW:\\n\"\n", "        report += \"-\"*40 + \"\\n\"\n", "        for section in confidence_stats['problematic_sections'][:10]:  # Top 10\n", "            timestamp = str(timedelta(seconds=int(section['timestamp'])))\n", "            report += f\"- [{timestamp}] {section['word_count']} consecutive low-confidence words\\n\"\n", "\n", "    report += \"\\nRECOMMENDATIONS:\\n\"\n", "    if confidence_stats['low_confidence_pct'] > 10:\n", "        report += \"⚠️ High percentage of low-confidence transcription\\n\"\n", "        report += \"   - Manual review strongly recommended\\n\"\n", "        report += \"   - Consider audio enhancement for re-transcription\\n\"\n", "\n", "    if len(overlaps) > 20:\n", "        report += \"⚠️ Significant speaker overlap detected\\n\"\n", "        report += \"   - May impact accuracy of speaker attribution\\n\"\n", "        report += \"   - Critical sections should be manually verified\\n\"\n", "\n", "    return report\n", "\n", "# ENHANCED LEGAL ANALYSIS FUNCTIONS\n", "# Add these functions to Cell 4 (insert after the existing functions)\n", "\n", "def cross_reference_utterances_with_behavior(enhanced_transcript, visual_context, skip_seconds=30):\n", "    \"\"\"Cross-reference speaker utterances with observable behavior for contradictions\"\"\"\n", "    print(\"🔍 Cross-referencing utterances with visual behavior...\")\n", "\n", "    behavioral_contradictions = []\n", "    compliance_violations = []\n", "\n", "    # Map commands to expected visual responses\n", "    command_keywords = {\n", "        'hands up': 'raised hands visible',\n", "        'get down': 'subject lowering to ground',\n", "        'turn around': 'subject rotating position',\n", "        'step back': 'backward movement',\n", "        'calm down': 'reduced agitation indicators',\n", "        'stop resisting': 'cessation of physical resistance',\n", "        'dont move': 'static positioning'\n", "    }\n", "\n", "    for i, word_data in enumerate(enhanced_transcript):\n", "        word_timestamp = word_data['start'] + skip_seconds\n", "        word_text = word_data['word'].lower()\n", "        speakers = word_data.get('speakers', [])\n", "\n", "        # Check if this is an officer command\n", "        is_officer_command = any('officer' in str(speaker).lower() or\n", "                               speaker in ['SPEAKER_A', 'SPEAKER_B', 'SPEAKER_C']\n", "                               for speaker in speakers)\n", "\n", "        if is_officer_command:\n", "            for command, expected_behavior in command_keywords.items():\n", "                if command in word_text:\n", "                    # Find corresponding visual context (within 30 seconds)\n", "                    corresponding_visual = None\n", "                    for ctx in visual_context:\n", "                        if abs(ctx['timestamp'] - word_timestamp) <= 30:\n", "                            corresponding_visual = ctx\n", "                            break\n", "\n", "                    if corresponding_visual:\n", "                        visual_analysis = corresponding_visual['analysis'].lower()\n", "\n", "                        # Check for compliance/non-compliance indicators\n", "                        compliance_indicators = ['complying', 'following', 'obeying', 'hands raised', 'cooperation']\n", "                        resistance_indicators = ['resisting', 'non-compliant', 'refusing', 'aggressive', 'fighting', 'obstructing']\n", "\n", "                        has_compliance = any(indicator in visual_analysis for indicator in compliance_indicators)\n", "                        has_resistance = any(indicator in visual_analysis for indicator in resistance_indicators)\n", "\n", "                        if command in ['hands up', 'get down', 'stop resisting'] and has_resistance:\n", "                            compliance_violations.append({\n", "                                'timestamp': word_timestamp,\n", "                                'command': command,\n", "                                'visual_evidence': visual_analysis[:200],\n", "                                'contradiction_type': 'Command not followed',\n", "                                'speakers': speakers\n", "                            })\n", "\n", "                        # Flag potential contradictions\n", "                        if 'calm down' in command and 'agitated' in visual_analysis:\n", "                            behavioral_contradictions.append({\n", "                                'timestamp': word_timestamp,\n", "                                'audio_content': word_text,\n", "                                'visual_content': visual_analysis[:200],\n", "                                'contradiction': 'De-escalation command during continued agitation'\n", "                            })\n", "\n", "    print(f\"✅ Found {len(compliance_violations)} compliance violations\")\n", "    print(f\"✅ Found {len(behavioral_contradictions)} behavioral contradictions\")\n", "\n", "    return compliance_violations, behavioral_contradictions\n", "\n", "def analyze_privacy_dignity_violations(enhanced_transcript, visual_context, skip_seconds=30):\n", "    \"\"\"Analyze privacy and dignity violations - WITH ENHANCED HANDCUFFING DETECTION\"\"\"\n", "    print(\"🔒 Analyzing privacy and dignity violations...\")\n", "\n", "    privacy_violations = []\n", "    dignity_violations = []\n", "    attire_violations = []\n", "    public_exposure_incidents = []\n", "\n", "    # Privacy violation keywords\n", "    privacy_keywords = ['strip', 'naked', 'undress', 'expose', 'body search', 'intimate', 'private parts']\n", "\n", "    # Dignity violation keywords\n", "    dignity_keywords = ['humiliate', 'embarrass', 'degrade', 'mock', 'ridicule', 'shame']\n", "\n", "    # Public exposure keywords # Enhanced keywords for clothing/attire situations\n", "    exposure_keywords = ['public', 'crowd', 'spectators', 'bystanders', 'recording',\n", "                        'exposed', 'visible', 'uncovered', 'inappropriate', 'public view',\n", "                        'neighbors seeing', 'crowd watching', 'filming']\n", "\n", "    emergency_exit_keywords = ['rushed out', 'hurried', 'interrupted', 'grabbed towel',\n", "                              'wet hair', 'steam', 'bathroom door', 'shower interrupted']\n", "\n", "    attire_keywords = ['towel', 'naked', 'undressed', 'partially clothed', 'minimal clothing' 'cover',\n", "                      'barefoot', 'wet', 'shower', 'bathing', 'bathroom', 'rushed', 'incomplete dress']\n", "\n", "    handcuff_dignity_keywords = ['handcuffed', 'cuffed', 'restrained', 'hands behind back',\n", "                                'restraints applied', 'detained']\n", "\n", "    # Analyze audio for clothing/exposure references\n", "    for i, word_data in enumerate(enhanced_transcript):\n", "        word_timestamp = word_data['start'] + skip_seconds\n", "        word_text = word_data['word'].lower()\n", "        speakers = word_data.get('speakers', [])\n", "\n", "        # Check for privacy violations\n", "        if any(keyword in word_text for keyword in privacy_keywords):\n", "            # Find corresponding visual context\n", "            visual_evidence = None\n", "            for ctx in visual_context:\n", "                if abs(ctx['timestamp'] - word_timestamp) <= 60:\n", "                    visual_evidence = ctx['analysis']\n", "                    break\n", "\n", "            privacy_violations.append({\n", "                'timestamp': word_timestamp,\n", "                'audio_evidence': word_text,\n", "                'visual_evidence': visual_evidence[:200] if visual_evidence else 'No visual context',\n", "                'violation_type': 'Privacy violation',\n", "                'speakers': word_data.get('speakers', [])\n", "            })\n", "\n", "        # Check for attire-related violations\n", "        if any(keyword in word_text for keyword in attire_keywords):\n", "            # Find corresponding visual context\n", "            visual_evidence = None\n", "            for ctx in visual_context:\n", "                if abs(ctx['timestamp'] - word_timestamp) <= 60:\n", "                    visual_evidence = ctx['analysis']\n", "                    break\n", "\n", "            attire_violations.append({\n", "                'timestamp': word_timestamp,\n", "                'audio_evidence': word_text,\n", "                'visual_evidence': visual_evidence[:300] if visual_evidence else 'No visual context',\n", "                'violation_type': 'Attire/Clothing Privacy Concern',\n", "                'speakers': speakers\n", "            })\n", "\n", "        # Check for handcuffing dignity concerns with attire context\n", "        if any(keyword in word_text for keyword in handcuff_dignity_keywords):\n", "            # Check if this occurs near attire violations\n", "            attire_context = any(attire_word in enhanced_transcript[max(0, i-10):i+10][j]['word'].lower()\n", "                               for j in range(len(enhanced_transcript[max(0, i-10):i+10]))\n", "                               for attire_word in ['towel', 'naked', 'undressed', 'wet'])\n", "\n", "            if attire_context:\n", "                dignity_violations.append({\n", "                    'timestamp': word_timestamp,\n", "                    'audio_evidence': word_text,\n", "                    'violation_type': 'Handcuffing Individual in Minimal Clothing - Dignity Violation',\n", "                    'speakers': speakers,\n", "                    'severity': 'HIGH',\n", "                    'constitutional_concern': '8th Amendment - Cruel and unusual punishment'\n", "                })\n", "\n", "        # Check for emergency exit situations\n", "        if any(keyword in word_text for keyword in emergency_exit_keywords):\n", "            privacy_violations.append({\n", "                'timestamp': word_timestamp,\n", "                'audio_evidence': word_text,\n", "                'violation_type': 'Emergency Exit Privacy Violation',\n", "                'speakers': speakers\n", "            })\n", "\n", "        # Check for dignity violations\n", "        if any(keyword in word_text for keyword in dignity_keywords):\n", "            dignity_violations.append({\n", "                'timestamp': word_timestamp,\n", "                'audio_evidence': word_text,\n", "                'violation_type': 'Dignity violation',\n", "                'speakers': word_data.get('speakers', [])\n", "            })\n", "\n", "    # Enhanced visual analysis for clothing/exposure\n", "    for ctx in visual_context:\n", "        visual_analysis = ctx['analysis'].lower()\n", "\n", "        # Check for clothing-related exposure\n", "        clothing_indicators = ['towel only', 'minimal clothing', 'partially dressed', 'undressed',\n", "                              'wet from shower', 'barefoot', 'rushed dressing', 'incomplete clothing']\n", "\n", "        if any(indicator in visual_analysis for indicator in clothing_indicators):\n", "            # Check if handcuffing is involved\n", "            handcuff_indicators = ['handcuff', 'cuff', 'restrain', 'hands behind back']\n", "            is_handcuffed = any(hc_word in visual_analysis for hc_word in handcuff_indicators)\n", "\n", "            # Check if in public view\n", "            public_indicators = ['public', 'neighbors', 'crowd', 'street', 'outside', 'porch', 'yard']\n", "            is_public = any(pub_word in visual_analysis for pub_word in public_indicators)\n", "\n", "            violation_type = 'Public Exposure - Minimal Clothing' if is_public else 'Private Exposure Documentation'\n", "\n", "            if is_handcuffed:\n", "                violation_type += ' + Restraint Applied'\n", "\n", "            public_exposure_incidents.append({\n", "                'timestamp': ctx['timestamp'],\n", "                'visual_evidence': ctx['analysis'],\n", "                'violation_type': violation_type,\n", "                'severity': 'CRITICAL' if (is_public and is_handcuffed) else 'HIGH' if is_public else 'MODERATE',\n", "                'clothing_status': 'MINIMAL/INADEQUATE',\n", "                'restraint_status': 'RESTRAINED' if is_handcuffed else 'UNRESTRAINED'\n", "            })\n", "\n", "        # Check for dignity violations\n", "        dignity_indicators = ['humiliating', 'embarrassing', 'inappropriate exposure',\n", "                             'forced to remain undressed', 'denied clothing']\n", "\n", "        if any(indicator in visual_analysis for indicator in dignity_indicators):\n", "            dignity_violations.append({\n", "                'timestamp': ctx['timestamp'],\n", "                'visual_evidence': ctx['analysis'],\n", "                'violation_type': 'Dignity Violation - Inappropriate Exposure',\n", "                'severity': 'HIGH'\n", "            })\n", "\n", "        # Check for emergency/crisis interruption\n", "        emergency_indicators = ['shower interrupted', 'rushed from bathroom', 'wet appearance',\n", "                               'emergency exit', 'hastily dressed', 'grabbed towel']\n", "\n", "        if any(indicator in visual_analysis for indicator in emergency_indicators):\n", "            privacy_violations.append({\n", "                'timestamp': ctx['timestamp'],\n", "                'visual_evidence': ctx['analysis'],\n", "                'violation_type': 'Emergency Privacy Interruption',\n", "                'context': 'Interrupted Private Activity'\n", "            })\n", "\n", "    # Analyze visual context for public exposure\n", "    public_exposure_incidents = []\n", "    for ctx in visual_context:\n", "        visual_analysis = ctx['analysis'].lower()\n", "        if any(keyword in visual_analysis for keyword in exposure_keywords):\n", "            if any(privacy_word in visual_analysis for privacy_word in ['exposed', 'undressed', 'strip']):\n", "                public_exposure_incidents.append({\n", "                    'timestamp': ctx['timestamp'],\n", "                    'visual_evidence': ctx['analysis'],\n", "                    'violation_type': 'Public exposure'\n", "                })\n", "\n", "    print(f\"✅ Found {len(attire_violations)} attire/clothing violations\")\n", "    print(f\"✅ Found {len(privacy_violations)} privacy violations\")\n", "    print(f\"✅ Found {len(dignity_violations)} dignity violations\")\n", "    print(f\"✅ Found {len(public_exposure_incidents)} public exposure incidents\")\n", "\n", "    return privacy_violations, dignity_violations, public_exposure_incidents, attire_violations\n", "\n", "def analyze_harassment_retaliation_patterns(enhanced_transcript, speaker_counts):\n", "    \"\"\"Analyze patterns of harassment or retaliation\"\"\"\n", "    print(\"⚠️ Analyzing harassment and retaliation patterns...\")\n", "\n", "    harassment_indicators = []\n", "    retaliation_patterns = []\n", "\n", "    # Harassment keywords\n", "    harassment_keywords = ['shut up', 'stupid', 'idiot', 'worthless', 'pathetic', 'loser', 'embarrass', 'loud'\n", "                           'humiliate', 'swat', 'loudspeaker']\n", "\n", "    # Retaliation keywords\n", "    retaliation_keywords = ['complained', 'lawyer', 'sue', 'rights', 'report', 'game', 'play', 'embarrass', 'loud'\n", "                           'humiliate', 'swat', 'loudspeaker']\n", "\n", "    # Power assertion keywords\n", "    power_keywords = ['because i said so', 'i am the law', 'do what i tell you', 'you will obey', 'embarrass', 'loud'\n", "                     'humiliate', 'swat', 'loudspeaker', 'shoot', 'hands up', 'cuff', 'detain', 'restrain',\n", "                     'rpo', 'risk protection']\n", "\n", "    # Track escalation after certain triggers\n", "    trigger_timestamps = []\n", "\n", "    for i, word_data in enumerate(enhanced_transcript):\n", "        word_timestamp = word_data['start']\n", "        word_text = word_data['word'].lower()\n", "        speakers = word_data.get('speakers', [])\n", "\n", "        # Check for harassment language\n", "        if any(keyword in word_text for keyword in harassment_keywords):\n", "            harassment_indicators.append({\n", "                'timestamp': word_timestamp,\n", "                'content': word_text,\n", "                'speakers': speakers,\n", "                'type': 'Verbal harassment'\n", "            })\n", "\n", "        # Check for retaliation triggers\n", "        if any(keyword in word_text for keyword in retaliation_keywords):\n", "            trigger_timestamps.append(word_timestamp)\n", "\n", "        # Check for power assertion\n", "        if any(keyword in word_text for keyword in power_keywords):\n", "            harassment_indicators.append({\n", "                'timestamp': word_timestamp,\n", "                'content': word_text,\n", "                'speakers': speakers,\n", "                'type': 'Power assertion'\n", "            })\n", "\n", "    # Analyze escalation patterns after triggers\n", "    for trigger_time in trigger_timestamps:\n", "        escalation_window = [word for word in enhanced_transcript\n", "                           if trigger_time < word['start'] < trigger_time + 300]  # 5 minutes after\n", "\n", "        if escalation_window:\n", "            force_words = ['force', 'taser', 'arrest', 'cuff', 'restrain']\n", "            escalation_count = sum(1 for word in escalation_window\n", "                                 if any(force_word in word['word'].lower() for force_word in force_words))\n", "\n", "            if escalation_count > 2:\n", "                retaliation_patterns.append({\n", "                    'trigger_timestamp': trigger_time,\n", "                    'escalation_period': '5 minutes',\n", "                    'escalation_indicators': escalation_count,\n", "                    'type': 'Post-complaint escalation'\n", "                })\n", "\n", "    print(f\"✅ Found {len(harassment_indicators)} harassment indicators\")\n", "    print(f\"✅ Found {len(retaliation_patterns)} retaliation patterns\")\n", "\n", "    return harassment_indicators, retaliation_patterns\n", "\n", "def analyze_misconduct_patterns(enhanced_transcript, visual_context):\n", "    \"\"\"Analyze patterns of coordinated misconduct\"\"\"\n", "    print(\"🕵️ Analyzing misconduct patterns...\")\n", "\n", "    narrative_shaping = []\n", "    coordinated_behavior = []\n", "    selective_enforcement = []\n", "\n", "    # Narrative shaping keywords\n", "    narrative_keywords = ['story', 'report', 'write up', 'document', 'official', 'record']\n", "    coaching_keywords = ['say', 'tell them', 'remember', 'stick to', 'version']\n", "\n", "    # Look for coordination between officers\n", "    officer_speakers = [speaker for speaker in set() for word in enhanced_transcript for speaker in word['speakers'] if 'officer' in str(speaker).lower()]\n", "\n", "    # Analyze for narrative coordination\n", "    for i, word_data in enumerate(enhanced_transcript):\n", "        word_text = word_data['word'].lower()\n", "        speakers = word_data.get('speakers', [])\n", "\n", "        if any(keyword in word_text for keyword in narrative_keywords):\n", "            if any(coach_word in word_text for coach_word in coaching_keywords):\n", "                narrative_shaping.append({\n", "                    'timestamp': word_data['start'],\n", "                    'content': word_text,\n", "                    'speakers': speakers,\n", "                    'type': 'Narrative coordination'\n", "                })\n", "\n", "    # Look for coordinated timing in visual evidence\n", "    officer_positioning_times = []\n", "    for ctx in visual_context:\n", "        if 'officer' in ctx['analysis'].lower() and 'position' in ctx['analysis'].lower():\n", "            officer_positioning_times.append(ctx['timestamp'])\n", "\n", "    # Check for coordinated positioning (multiple officers moving within short timeframe)\n", "    for i, time1 in enumerate(officer_positioning_times):\n", "        for time2 in officer_positioning_times[i+1:]:\n", "            if abs(time1 - time2) < 30:  # Within 30 seconds\n", "                coordinated_behavior.append({\n", "                    'timestamp_1': time1,\n", "                    'timestamp_2': time2,\n", "                    'type': 'Coordinated positioning',\n", "                    'time_difference': abs(time1 - time2)\n", "                })\n", "\n", "    print(f\"✅ Found {len(narrative_shaping)} narrative shaping incidents\")\n", "    print(f\"✅ Found {len(coordinated_behavior)} coordinated behavior patterns\")\n", "\n", "    return narrative_shaping, coordinated_behavior, selective_enforcement\n", "\n", "def generate_comprehensive_legal_analysis_document(\n", "    transcript_text, enhanced_transcript, visual_context,\n", "    compliance_violations, behavioral_contradictions,\n", "    privacy_violations, dignity_violations, public_exposure,\n", "    harassment_indicators, retaliation_patterns,\n", "    narrative_shaping, coordinated_behavior,\n", "    skip_seconds=30\n", "):\n", "    \"\"\"Generate comprehensive legal analysis document with all required sections\"\"\"\n", "\n", "    legal_analysis_prompt = f\"\"\"You are a certified forensic audiovisual analyst and constitutional law expert with 25+ years of experience serving as a court-appointed expert witness. Generate a comprehensive legal analysis document based on the integrated audio-visual evidence provided.\n", "\n", "STRUCTURE YOUR ANALYSIS WITH THESE MANDATORY SECTIONS:\n", "\n", "1. STATUTORY VIOLATIONS ANALYSIS:\n", "   - Florida Statute § 394.463 (Baker Act procedures)\n", "   - Florida Statute Chapter 901 (Arrest authority and procedures)\n", "   - Florida Statute § 776.05 (Law enforcement use of force)\n", "   - Florida Statute § 843.02 (Resisting arrest provisions)\n", "   - Florida Administrative Code 11B-27 (Mental health transport)\n", "   - Cite specific violations with timestamp evidence\n", "\n", "2. CONSTITUTIONAL VIOLATIONS ANALYSIS:\n", "   - 4th Amendment: Search and seizure violations, warrant requirements\n", "   - 5th Amendment: Miranda rights, self-incrimination issues\n", "   - 8th Amendment: Excessive force, cruel and unusual punishment\n", "   - 14th Amendment: Due process, equal protection violations\n", "   - Provide specific constitutional analysis with case law citations\n", "\n", "3. PROCEDURAL BREACHES ASSESSMENT:\n", "   - Required warnings not provided (Miranda, medical rights)\n", "   - Transport protocol violations\n", "   - Mental health criteria non-compliance\n", "   - Medical clearance timing violations\n", "   - Supervisor notification failures\n", "   - Chain of custody issues\n", "\n", "4. PATTERNS OF MISCONDUCT IDENTIFICATION:\n", "   - Evidence of coordinated narrative shaping: {len(narrative_shaping)} incidents\n", "   - Coordinated behavior patterns: {len(coordinated_behavior)} instances\n", "   - Retaliatory conduct indicators: {len(retaliation_patterns)} patterns\n", "   - Selective enforcement evidence\n", "\n", "5. PRIVACY & DIGNITY VIOLATIONS:\n", "   - Public exposure incidents: {len(public_exposure)} documented\n", "   - Privacy violations: {len(privacy_violations)} identified\n", "   - Dignity violations: {len(dignity_violations)} documented\n", "   - Inappropriate disclosure or humiliation tactics\n", "\n", "6. USE OF FORCE ASSESSMENT (<PERSON> v. Connor Analysis):\n", "   - Severity of crime factors\n", "   - Immediacy of threat assessment\n", "   - Actively resisting arrest evaluation\n", "   - Attempting to evade by flight analysis\n", "   - Totality of circumstances review\n", "   - Florida agency force protocol compliance\n", "\n", "7. HARASSMENT OR RETALIATION EVIDENCE:\n", "   - Harassment indicators: {len(harassment_indicators)} documented\n", "   - Personal animus evidence\n", "   - Power assertion tactics: documented instances\n", "   - Language indicating improper motive\n", "\n", "8. AUDIO-VISUAL CONTRADICTION ANALYSIS:\n", "   - Commands vs. compliance discrepancies: {len(compliance_violations)} violations\n", "   - Behavioral contradictions: {len(behavioral_contradictions)} identified\n", "   - Officer statements vs. visual evidence mismatches\n", "\n", "EVIDENCE PROVIDED:\n", "- Audio transcript: {len(transcript_text)} characters\n", "- Enhanced transcript: {len(enhanced_transcript)} words\n", "- Visual context points: {len(visual_context)} frames analyzed\n", "- Compliance violations: {compliance_violations}\n", "- Privacy violations: {privacy_violations}\n", "- Harassment patterns: {harassment_indicators}\n", "\n", "Provide specific timestamps, direct quotes, visual evidence references, statutory citations, constitutional analysis, and court-admissible conclusions for each section. Use Bluebook citation format where applicable.\"\"\"\n", "\n", "    try:\n", "        response = openai.ChatCompletion.create(\n", "            model=\"gpt-4\",\n", "            messages=[\n", "                {\"role\": \"system\", \"content\": \"You are a certified forensic legal analyst specializing in constitutional law, criminal procedure, and police misconduct analysis.\"},\n", "                {\"role\": \"user\", \"content\": legal_analysis_prompt}\n", "            ],\n", "            max_tokens=4000,\n", "            temperature=0.05\n", "        )\n", "\n", "        return response.choices[0].message.content\n", "\n", "    except Exception as e:\n", "        print(f\"❌ Comprehensive legal analysis failed: {e}\")\n", "        return f\"Comprehensive legal analysis unavailable: {e}\"\n", "\n", "# ENHANCED ATTIRE AND PRIVACY ANALYSIS\n", "# Add these functions to Cell 4 or replace existing functions\n", "\n", "def analyze_video_frames_for_context_enhanced_attire(video_path, skip_seconds=30):\n", "    \"\"\"Enhanced video analysis with specific attire and privacy detection\"\"\"\n", "    print(\"📹 Analyzing video frames with enhanced attire/privacy detection...\")\n", "\n", "    frames_dir = \"/content/video_frames\"\n", "    os.makedirs(frames_dir, exist_ok=True)\n", "\n", "    extract_frames_cmd = [\n", "        'ffmpeg', '-y', '-ss', str(skip_seconds), '-i', video_path,\n", "        '-vf', 'fps=1/20', '-q:v', '2', f'{frames_dir}/frame_%04d.jpg'\n", "    ]\n", "    subprocess.run(extract_frames_cmd, capture_output=True)\n", "\n", "    frame_files = sorted([f for f in os.listdir(frames_dir) if f.endswith('.jpg')])\n", "    visual_context = []\n", "\n", "    print(f\"🔍 Analyzing {len(frame_files)} video frames with attire focus...\")\n", "\n", "    for i, frame_file in enumerate(frame_files):\n", "        frame_path = os.path.join(frames_dir, frame_file)\n", "        timestamp = (i * 20) + skip_seconds\n", "\n", "        try:\n", "            with open(frame_path, 'rb') as f:\n", "                frame_data = base64.b64encode(f.read()).decode()\n", "\n", "            response = openai.ChatCompletion.create(\n", "                model=\"gpt-4o\",\n", "                messages=[\n", "                    {\n", "                        \"role\": \"user\",\n", "                        \"content\": [\n", "                            {\n", "                                \"type\": \"text\",\n", "                                \"text\": \"\"\"Conduct detailed forensic analysis of this police bodycam frame with SPECIFIC ATTENTION to clothing and privacy issues:\n", "\n", "1. ATTIRE ANALYSIS (CRITICAL FOR PRIVACY ASSESSMENT):\n", "   - Clothing status: Fully dressed, partially dressed, minimal clothing, towel only, etc.\n", "   - State of dress: Appropriate, inappropriate for public, emergency exit clothing\n", "   - Signs of rushed dressing: Incomplete clothing, towel wrapped, wet appearance\n", "   - Modesty concerns: Areas of body exposed, coverage inadequacy\n", "   - Footwear: Barefoot, shoes, slippers (indicates emergency exit)\n", "\n", "2. PRIVACY & DIGNITY INDICATORS:\n", "   - Public exposure level: Private home vs. public view\n", "   - Bystander presence: Neighbors, crowds, passersby witnessing exposure\n", "   - Recording implications: Subject aware of being filmed in state of undress\n", "   - Weather conditions affecting minimal clothing exposure\n", "\n", "3. EMERGENCY/CRISIS INDICATORS:\n", "   - Wet hair/body (shower interruption)\n", "   - Rushed appearance (hastily grabbed clothing/towel)\n", "   - Bathroom/shower context (wet floors, steam, towels visible)\n", "   - Time pressure indicators (incomplete dressing)\n", "\n", "4. RESTRAINT/HANDCUFFING ANALYSIS:\n", "   - Handcuff application on subject in minimal clothing\n", "   - Positioning: hands behind back while in towel/minimal clothing\n", "   - Dignity concerns during restraint application\n", "   - Cooperative behavior vs. restraint necessity\n", "\n", "5. STANDARD FORENSIC ELEMENTS:\n", "   - Scene setting and location context\n", "   - People positions and actions\n", "   - Equipment and evidence visible\n", "   - Officer positioning relative to undressed subject\n", "   - Safety and tactical considerations\n", "\n", "6. CONSTITUTIONAL CONCERNS:\n", "   - 4th Amendment: Privacy expectations in home\n", "   - 8th Amendment: Dignity during detention\n", "   - Public exposure creating humiliation\n", "   - Reasonable accommodation for clothing needs\n", "\n", "Be extremely specific about clothing status and privacy implications. Flag any dignitary concerns or inappropriate exposure situations. Document if subject appears to have been interrupted during private activities (bathing, dressing, etc.).\"\"\"\n", "                            },\n", "                            {\n", "                                \"type\": \"image_url\",\n", "                                \"image_url\": {\n", "                                    \"url\": f\"data:image/jpeg;base64,{frame_data}\"\n", "                                }\n", "                            }\n", "                        ]\n", "                    }\n", "                ],\n", "                max_tokens=600,\n", "                temperature=0.1\n", "            )\n", "\n", "            visual_analysis = response.choices[0].message.content\n", "            visual_context.append({\n", "                'timestamp': timestamp,\n", "                'frame': frame_file,\n", "                'analysis': visual_analysis\n", "            })\n", "\n", "            print(f\"✅ Enhanced frame analysis: {timestamp//60:02d}:{timestamp%60:02d}\")\n", "\n", "        except Exception as e:\n", "            print(f\"⚠️ Frame analysis failed for {frame_file}: {e}\")\n", "            visual_context.append({\n", "                'timestamp': timestamp,\n", "                'frame': frame_file,\n", "                'analysis': f\"Visual analysis unavailable: {e}\"\n", "            })\n", "\n", "    print(f\"✅ Enhanced visual context analysis complete: {len(visual_context)} frames\")\n", "    return visual_context\n", "\n", "def analyze_privacy_dignity_violations_enhanced(enhanced_transcript, visual_context, skip_seconds=30):\n", "    \"\"\"Enhanced privacy and dignity analysis with specific attire focus\"\"\"\n", "    print(\"🔒 Enhanced privacy and dignity violations analysis...\")\n", "\n", "    privacy_violations = []\n", "    dignity_violations = []\n", "    attire_violations = []\n", "    public_exposure_incidents = []\n", "\n", "    # Enhanced keywords for clothing/attire situations\n", "    attire_keywords = ['towel', 'naked', 'undressed', 'partially clothed', 'minimal clothing',\n", "                      'barefoot', 'wet', 'shower', 'bathing', 'bathroom', 'rushed', 'incomplete dress']\n", "\n", "    emergency_exit_keywords = ['rushed out', 'hurried', 'interrupted', 'grabbed towel',\n", "                              'wet hair', 'steam', 'bathroom door', 'shower interrupted']\n", "\n", "    exposure_keywords = ['exposed', 'visible', 'uncovered', 'inappropriate', 'public view',\n", "                        'neighbors seeing', 'crowd watching', 'filming', 'recording']\n", "\n", "    handcuff_dignity_keywords = ['handcuffed', 'cuffed', 'restrained', 'hands behind back',\n", "                                'restraints applied', 'detained']\n", "\n", "    # Analyze audio for clothing/exposure references\n", "    for i, word_data in enumerate(enhanced_transcript):\n", "        word_timestamp = word_data['start'] + skip_seconds\n", "        word_text = word_data['word'].lower()\n", "        speakers = word_data.get('speakers', [])\n", "\n", "        # Check for attire-related violations\n", "        if any(keyword in word_text for keyword in attire_keywords):\n", "            # Find corresponding visual context\n", "            visual_evidence = None\n", "            for ctx in visual_context:\n", "                if abs(ctx['timestamp'] - word_timestamp) <= 60:\n", "                    visual_evidence = ctx['analysis']\n", "                    break\n", "\n", "            attire_violations.append({\n", "                'timestamp': word_timestamp,\n", "                'audio_evidence': word_text,\n", "                'visual_evidence': visual_evidence[:300] if visual_evidence else 'No visual context',\n", "                'violation_type': 'Attire/Clothing Privacy Concern',\n", "                'speakers': speakers\n", "            })\n", "\n", "        # Check for handcuffing dignity concerns with attire context\n", "        if any(keyword in word_text for keyword in handcuff_dignity_keywords):\n", "            # Check if this occurs near attire violations\n", "            attire_context = any(attire_word in enhanced_transcript[max(0, i-10):i+10][j]['word'].lower()\n", "                               for j in range(len(enhanced_transcript[max(0, i-10):i+10]))\n", "                               for attire_word in ['towel', 'naked', 'undressed', 'wet'])\n", "\n", "            if attire_context:\n", "                dignity_violations.append({\n", "                    'timestamp': word_timestamp,\n", "                    'audio_evidence': word_text,\n", "                    'violation_type': 'Handcuffing Individual in Minimal Clothing - Dignity Violation',\n", "                    'speakers': speakers,\n", "                    'severity': 'HIGH',\n", "                    'constitutional_concern': '8th Amendment - Cruel and unusual punishment'\n", "                })\n", "\n", "        # Check for emergency exit situations\n", "        if any(keyword in word_text for keyword in emergency_exit_keywords):\n", "            privacy_violations.append({\n", "                'timestamp': word_timestamp,\n", "                'audio_evidence': word_text,\n", "                'violation_type': 'Emergency Exit Privacy Violation',\n", "                'speakers': speakers\n", "            })\n", "\n", "    # Enhanced visual analysis for clothing/exposure\n", "    for ctx in visual_context:\n", "        visual_analysis = ctx['analysis'].lower()\n", "\n", "        # Check for clothing-related exposure\n", "        clothing_indicators = ['towel only', 'minimal clothing', 'partially dressed', 'undressed',\n", "                              'wet from shower', 'barefoot', 'rushed dressing', 'incomplete clothing']\n", "\n", "        if any(indicator in visual_analysis for indicator in clothing_indicators):\n", "            # Check if handcuffing is involved\n", "            handcuff_indicators = ['handcuff', 'cuff', 'restrain', 'hands behind back']\n", "            is_handcuffed = any(hc_word in visual_analysis for hc_word in handcuff_indicators)\n", "\n", "            # Check if in public view\n", "            public_indicators = ['public', 'neighbors', 'crowd', 'street', 'outside', 'porch', 'yard']\n", "            is_public = any(pub_word in visual_analysis for pub_word in public_indicators)\n", "\n", "            violation_type = 'Public Exposure - Minimal Clothing' if is_public else 'Private Exposure Documentation'\n", "\n", "            if is_handcuffed:\n", "                violation_type += ' + Restraint Applied'\n", "\n", "            public_exposure_incidents.append({\n", "                'timestamp': ctx['timestamp'],\n", "                'visual_evidence': ctx['analysis'],\n", "                'violation_type': violation_type,\n", "                'severity': 'CRITICAL' if (is_public and is_handcuffed) else 'HIGH' if is_public else 'MODERATE',\n", "                'clothing_status': 'MINIMAL/INADEQUATE',\n", "                'restraint_status': 'RESTRAINED' if is_handcuffed else 'UNRESTRAINED'\n", "            })\n", "\n", "        # Check for dignity violations\n", "        dignity_indicators = ['humiliating', 'embarrassing', 'inappropriate exposure',\n", "                             'forced to remain undressed', 'denied clothing']\n", "\n", "        if any(indicator in visual_analysis for indicator in dignity_indicators):\n", "            dignity_violations.append({\n", "                'timestamp': ctx['timestamp'],\n", "                'visual_evidence': ctx['analysis'],\n", "                'violation_type': 'Dignity Violation - Inappropriate Exposure',\n", "                'severity': 'HIGH'\n", "            })\n", "\n", "        # Check for emergency/crisis interruption\n", "        emergency_indicators = ['shower interrupted', 'rushed from bathroom', 'wet appearance',\n", "                               'emergency exit', 'hastily dressed', 'grabbed towel']\n", "\n", "        if any(indicator in visual_analysis for indicator in emergency_indicators):\n", "            privacy_violations.append({\n", "                'timestamp': ctx['timestamp'],\n", "                'visual_evidence': ctx['analysis'],\n", "                'violation_type': 'Emergency Privacy Interruption',\n", "                'context': 'Interrupted Private Activity'\n", "            })\n", "\n", "    print(f\"✅ Found {len(attire_violations)} attire/clothing violations\")\n", "    print(f\"✅ Found {len(privacy_violations)} privacy violations\")\n", "    print(f\"✅ Found {len(dignity_violations)} dignity violations\")\n", "    print(f\"✅ Found {len(public_exposure_incidents)} public exposure incidents\")\n", "\n", "    return privacy_violations, dignity_violations, public_exposure_incidents, attire_violations\n", "\n", "def inject_attire_context_annotations(enhanced_transcript, visual_context, skip_seconds=30):\n", "    \"\"\"Inject specific attire and privacy context annotations\"\"\"\n", "    print(\"💉 Injecting attire and privacy context annotations...\")\n", "\n", "    attire_annotations = {}\n", "\n", "    for i, word_data in enumerate(enhanced_transcript):\n", "        word_timestamp = word_data['start'] + skip_seconds\n", "        word_text = word_data['word'].lower()\n", "\n", "        # Find corresponding visual context for this word\n", "        closest_visual = None\n", "        min_time_diff = float('inf')\n", "\n", "        for ctx in visual_context:\n", "            time_diff = abs(ctx['timestamp'] - word_timestamp)\n", "            if time_diff < min_time_diff and time_diff < 15:\n", "                min_time_diff = time_diff\n", "                closest_visual = ctx\n", "\n", "        if closest_visual:\n", "            visual_text = closest_visual['analysis'].lower()\n", "\n", "            # Check for specific attire situations\n", "            if any(indicator in visual_text for indicator in ['towel only', 'minimal clothing', 'undressed']):\n", "                attire_annotations[i] = \"*{ATTIRE CONCERN: Subject in minimal clothing/towel only - Privacy implications}*\"\n", "\n", "            elif any(indicator in visual_text for indicator in ['wet from shower', 'rushed from bathroom']):\n", "                attire_annotations[i] = \"*{EMERGENCY EXIT: Subject interrupted during private activity - Constitutional privacy concern}*\"\n", "\n", "            elif any(indicator in visual_text for indicator in ['handcuff', 'restrain']) and any(attire in visual_text for attire in ['towel', 'minimal', 'undressed']):\n", "                attire_annotations[i] = \"*{CRITICAL DIGNITY VIOLATION: Restraint applied to subject in minimal clothing - 8th Amendment concern}*\"\n", "\n", "            elif any(indicator in visual_text for indicator in ['public exposure', 'neighbors seeing']):\n", "                attire_annotations[i] = \"*{PUBLIC EXPOSURE: Inappropriate clothing status in public view - Dignity violation}*\"\n", "\n", "            elif any(indicator in visual_text for indicator in ['barefoot', 'incomplete dress']):\n", "                attire_annotations[i] = \"*{RUSHED DRESSING: Emergency exit indicators - Privacy interruption documented}*\"\n", "\n", "    print(f\"✅ Attire context annotations prepared: {len(attire_annotations)} annotations\")\n", "    return attire_annotations\n", "\n", "print(\"✅ Enhanced attire and privacy analysis functions loaded!\")\n", "def calculate_violation_severity_score(violation_type, context_factors):\n", "    \"\"\"Calculate severity score for violations based on multiple factors\"\"\"\n", "\n", "    base_scores = {\n", "        \"handcuffing_minimal_clothing\": 9,\n", "        \"public_exposure\": 8,\n", "        \"privacy_interruption\": 7,\n", "        \"excessive_force\": 9,\n", "        \"miranda_violation\": 8,\n", "        \"consent_violation\": 7,\n", "        \"dignity_violation\": 8,\n", "        \"harassment\": 7,\n", "        \"retaliation\": 8,\n", "        \"narrative_coordination\": 6\n", "    }\n", "\n", "    multipliers = {\n", "        \"multiple_witnesses\": 1.3,\n", "        \"recording_present\": 1.2,\n", "        \"vulnerable_individual\": 1.4,\n", "        \"mental_health_crisis\": 1.3,\n", "        \"cooperative_subject\": 1.5,\n", "        \"public_location\": 1.3,\n", "        \"repeated_behavior\": 1.4\n", "    }\n", "\n", "    # Get base score\n", "    base_score = base_scores.get(violation_type, 5)\n", "\n", "    # Apply multipliers\n", "    final_score = base_score\n", "    for factor, value in context_factors.items():\n", "        if value and factor in multipliers:\n", "            final_score *= multipliers[factor]\n", "\n", "    return min(10, round(final_score, 1))  # Cap at 10\n", "\n", "def generate_violation_timeline(violations_data, skip_seconds=30):\n", "    \"\"\"Generate chronological timeline of all violations\"\"\"\n", "\n", "    timeline_events = []\n", "\n", "    # Collect all violations with timestamps\n", "    for vtype, violations in violations_data.items():\n", "        for v in violations:\n", "            timestamp = v.get('timestamp', 0)\n", "            timeline_events.append({\n", "                'timestamp': timestamp,\n", "                'type': vtype,\n", "                'details': v,\n", "                'severity': v.get('severity', 'MODERATE')\n", "            })\n", "\n", "    # Sort by timestamp\n", "    timeline_events.sort(key=lambda x: x['timestamp'])\n", "\n", "    # Format timeline\n", "    timeline_str = \"CHRONOLOGICAL VIOLATION TIMELINE:\\n\"\n", "    timeline_str += \"=\"*50 + \"\\n\\n\"\n", "\n", "    for event in timeline_events:\n", "        time_str = str(timedelta(seconds=int(event['timestamp'] - skip_seconds)))\n", "        timeline_str += f\"[{time_str}] {event['type'].upper()}\\n\"\n", "        timeline_str += f\"   Severity: {event['severity']}\\n\"\n", "\n", "        # Add specific details based on type\n", "        details = event['details']\n", "        if 'audio_evidence' in details:\n", "            timeline_str += f\"   Audio: {details['audio_evidence']}\\n\"\n", "        if 'visual_evidence' in details:\n", "            timeline_str += f\"   Visual: {details['visual_evidence'][:100]}...\\n\"\n", "        if 'violation_type' in details:\n", "            timeline_str += f\"   Type: {details['violation_type']}\\n\"\n", "\n", "        timeline_str += \"\\n\"\n", "\n", "    return timeline_str\n", "\n", "def analyze_body_camera_muting_patterns(enhanced_transcript, skip_seconds=30):\n", "    \"\"\"Analyze patterns of body camera muting or deactivation\"\"\"\n", "    print(\"📹 Analyzing body camera muting patterns...\")\n", "\n", "    muting_incidents = []\n", "    mute_keywords = ['mute', 'turn off', 'camera off', 'stop recording', 'blue', 'deactivate']\n", "\n", "    for i, word_data in enumerate(enhanced_transcript):\n", "        word_timestamp = word_data['start'] + skip_seconds\n", "        word_text = word_data['word'].lower()\n", "        speakers = word_data.get('speakers', [])\n", "\n", "        if any(keyword in word_text for keyword in mute_keywords):\n", "            # Look for context around muting\n", "            context_start = max(0, i - 20)\n", "            context_end = min(len(enhanced_transcript), i + 20)\n", "            context_words = ' '.join([w['word'] for w in enhanced_transcript[context_start:context_end]])\n", "\n", "            muting_incidents.append({\n", "                'timestamp': word_timestamp,\n", "                'keyword': word_text,\n", "                'speakers': speakers,\n", "                'context': context_words,\n", "                'suspicious': any(word in context_words.lower() for word in ['don\\'t', 'stop', 'turn off', 'need to'])\n", "            })\n", "\n", "    print(f\"✅ Found {len(muting_incidents)} potential camera muting references\")\n", "    return muting_incidents\n", "\n", "def extract_officer_identities(enhanced_transcript, visual_context):\n", "    \"\"\"Extract and track officer identities throughout the encounter using BERT NER\"\"\"\n", "    print(\"👮 Extracting officer identities using BERT NER...\")\n", "\n", "    # Initialize BERT NER pipeline\n", "    try:\n", "        ner_pipeline = pipeline(\"ner\", model=\"dslim/bert-base-NER\", aggregation_strategy=\"simple\")\n", "    except Exception as e:\n", "        print(f\"⚠️ NER model loading failed: {e}\")\n", "        return {}\n", "\n", "    officer_data = {}\n", "    title_patterns = ['officer', 'sergeant', 'lieutenant', 'deputy', 'detective', 'captain']\n", "\n", "    # Build text chunks around officer titles for NER processing\n", "    text_chunks = []\n", "    chunk_metadata = []\n", "\n", "    for i, word_data in enumerate(enhanced_transcript):\n", "        word_text = word_data['word'].lower()\n", "\n", "        # Look for officer titles\n", "        if any(pattern in word_text for pattern in title_patterns):\n", "            # Extract context window (20 words before and after)\n", "            start_idx = max(0, i - 20)\n", "            end_idx = min(len(enhanced_transcript), i + 20)\n", "\n", "            # Build text chunk\n", "            chunk_words = [enhanced_transcript[j]['word'] for j in range(start_idx, end_idx)]\n", "            chunk_text = ' '.join(chunk_words)\n", "\n", "            text_chunks.append(chunk_text)\n", "            chunk_metadata.append({\n", "                'timestamp': word_data['start'],\n", "                'title_found': word_text,\n", "                'speakers': word_data.get('speakers', [])\n", "            })\n", "\n", "    # Process chunks with NER\n", "    for chunk_text, metadata in zip(text_chunks, chunk_metadata):\n", "        try:\n", "            # Run NER on chunk\n", "            entities = ner_pipeline(chunk_text)\n", "\n", "            # Extract person names near officer titles\n", "            for entity in entities:\n", "                if entity['entity_group'] == 'PER':  # Person entity\n", "                    name = entity['word'].strip()\n", "\n", "                    # Clean up name (remove ## tokens from BERT)\n", "                    name = name.replace('##', '')\n", "\n", "                    # Create unique officer ID\n", "                    officer_id = f\"{metadata['title_found']}_{name}\".upper()\n", "\n", "                    if officer_id not in officer_data:\n", "                        officer_data[officer_id] = {\n", "                            'name': name,\n", "                            'title': metadata['title_found'],\n", "                            'first_mention': metadata['timestamp'],\n", "                            'mentions': [],\n", "                            'speakers': metadata['speakers']\n", "                        }\n", "\n", "                    officer_data[officer_id]['mentions'].append({\n", "                        'timestamp': metadata['timestamp'],\n", "                        'context': chunk_text[:100]\n", "                    })\n", "\n", "        except Exception as e:\n", "            print(f\"⚠️ NER processing error: {e}\")\n", "\n", "    # Also extract badge numbers and identifiers from visual analysis\n", "    badge_pattern = r'(?:badge|unit|car)\\s*#?\\s*(\\d+)'\n", "\n", "    for ctx in visual_context:\n", "        visual_text = ctx['analysis'].lower()\n", "        if 'officer' in visual_text or 'badge' in visual_text:\n", "            # Extract badge numbers\n", "            import re\n", "            badges = re.findall(badge_pattern, visual_text)\n", "            for badge in badges:\n", "                badge_id = f\"BADGE_{badge}\"\n", "                if badge_id not in officer_data:\n", "                    officer_data[badge_id] = {\n", "                        'badge_number': badge,\n", "                        'visual_timestamp': ctx['timestamp'],\n", "                        'visual_context': visual_text[:200]\n", "                    }\n", "\n", "    print(f\"✅ Identified {len(officer_data)} unique officer references\")\n", "    return officer_data\n", "\n", "def analyze_de_escalation_failures(enhanced_transcript, visual_context, skip_seconds=30):\n", "    \"\"\"Analyze de-escalation attempts and failures\"\"\"\n", "    print(\"📉 Analyzing de-escalation patterns...\")\n", "\n", "    de_escalation_events = []\n", "    escalation_events = []\n", "\n", "    # De-escalation keywords\n", "    de_escalation_keywords = ['calm down', 'relax', 'take a breath', 'easy', 'talk to me',\n", "                             'help you', 'understand', 'listen', 'explain', 'work with']\n", "\n", "    # Escalation keywords\n", "    escalation_keywords = ['hands up', 'get down', 'don\\'t move', 'stop', 'now',\n", "                          'do it', 'comply', 'force', 'taser', 'arrest', 'cuff']\n", "\n", "    # Track escalation trajectory\n", "    for i, word_data in enumerate(enhanced_transcript):\n", "        word_timestamp = word_data['start'] + skip_seconds\n", "        word_text = word_data['word'].lower()\n", "        speakers = word_data.get('speakers', [])\n", "\n", "        # Check for de-escalation attempts\n", "        if any(keyword in word_text for keyword in de_escalation_keywords):\n", "            # Look ahead to see if escalation follows\n", "            escalation_follows = False\n", "            for j in range(i+1, min(i+50, len(enhanced_transcript))):\n", "                if any(esc_word in enhanced_transcript[j]['word'].lower() for esc_word in escalation_keywords):\n", "                    escalation_follows = True\n", "                    break\n", "\n", "            de_escalation_events.append({\n", "                'timestamp': word_timestamp,\n", "                'text': word_text,\n", "                'speakers': speakers,\n", "                'followed_by_escalation': escalation_follows,\n", "                'effectiveness': 'FAILED' if escalation_follows else 'UNCLEAR'\n", "            })\n", "\n", "        # Check for escalation\n", "        if any(keyword in word_text for keyword in escalation_keywords):\n", "            escalation_events.append({\n", "                'timestamp': word_timestamp,\n", "                'text': word_text,\n", "                'speakers': speakers\n", "            })\n", "\n", "    # Analyze visual escalation indicators\n", "    for ctx in visual_context:\n", "        visual_text = ctx['analysis'].lower()\n", "        if any(indicator in visual_text for indicator in ['weapon drawn', 'aggressive stance',\n", "                                                          'hands on weapon', 'tactical position']):\n", "            escalation_events.append({\n", "                'timestamp': ctx['timestamp'],\n", "                'type': 'VISUAL',\n", "                'evidence': ctx['analysis'][:200]\n", "            })\n", "\n", "    print(f\"✅ Found {len(de_escalation_events)} de-escalation attempts\")\n", "    print(f\"✅ Found {len(escalation_events)} escalation events\")\n", "\n", "    return de_escalation_events, escalation_events\n", "\n", "def generate_executive_summary_enhanced(all_violations, transcript_length):\n", "    \"\"\"Generate enhanced executive summary with key findings and recommendations\"\"\"\n", "\n", "    summary = \"EXECUTIVE SUMMARY - KEY FINDINGS:\\n\"\n", "    summary += \"=\"*50 + \"\\n\\n\"\n", "\n", "    # Calculate total violations\n", "    total_violations = sum(len(v) for v in all_violations.values())\n", "\n", "    # Identify most serious violations\n", "    critical_violations = []\n", "    for vtype, violations in all_violations.items():\n", "        for v in violations:\n", "            if v.get('severity') in ['CRITICAL', 'HIGH']:\n", "                critical_violations.append((vtype, v))\n", "\n", "    summary += f\"Total Violations Identified: {total_violations}\\n\"\n", "    summary += f\"Critical/High Severity: {len(critical_violations)}\\n\"\n", "    summary += f\"Transcript Coverage: {transcript_length} words analyzed\\n\\n\"\n", "\n", "    summary += \"MOST SERIOUS VIOLATIONS:\\n\"\n", "    summary += \"-\"*30 + \"\\n\"\n", "    for vtype, violation in critical_violations[:5]:  # Top 5\n", "        summary += f\"• {vtype}: {violation.get('violation_type', 'N/A')}\\n\"\n", "\n", "    summary += \"\\nRECOMMENDED IMMEDIATE ACTIONS:\\n\"\n", "    summary += \"-\"*30 + \"\\n\"\n", "    summary += \"1. Preserve all bodycam footage and evidence\\n\"\n", "    summary += \"2. Document witness statements\\n\"\n", "    summary += \"3. Photograph any injuries or property damage\\n\"\n", "    summary += \"4. File formal complaints with appropriate agencies\\n\"\n", "    summary += \"5. Consult with civil rights attorney\\n\\n\"\n", "\n", "    return summary\n", "\n", "print(\"✅ Enhanced violation analysis functions loaded!\")\n", "print(\"✅ Enhanced legal analysis functions loaded!\")\n", "print(\"✅ Enhanced forensic pipeline functions loaded successfully!\")"], "metadata": {"id": "TnguHNYU3sZj"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# =============================================================================\n", "# Cell 5: <PERSON><PERSON> Enhanced Speaker Diarization Pipeline\n", "# =============================================================================\n", "print(\"👥 Loading enhanced speaker diarization pipeline...\")\n", "\n", "try:\n", "    diarization_pipeline = Pipeline.from_pretrained(\n", "        \"pyannote/speaker-diarization-3.1\",\n", "        use_auth_token=HF_TOKEN\n", "    )\n", "    diarization_pipeline.to(torch.device(device))\n", "    print(\"✅ Enhanced speaker diarization pipeline loaded successfully!\")\n", "except Exception as e:\n", "    print(f\"❌ Failed to load speaker diarization: {e}\")\n", "    print(\"Please check your HuggingFace token permissions\")"], "metadata": {"id": "JVNTPWUw3sc9"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# =============================================================================\n", "# Cell 6: Complete Enhanced Forensic Processing Function WITH ALL IMPROVEMENTS AND FIXES\n", "# =============================================================================\n", "def process_complete_enhanced_forensic_analysis(video_path, skip_seconds=30):\n", "    \"\"\"\n", "    Complete enhanced forensic pipeline with comprehensive legal analysis\n", "    INCLUDING <PERSON><PERSON> FIXES FOR TOKEN LIMITS AND ERRORS\n", "    \"\"\"\n", "    import os\n", "    import json\n", "    import hashlib\n", "    from datetime import datetime, timedelta\n", "    from google.colab import files\n", "\n", "    print(\"🏛️ ENHANCED CERTIFIED FORENSIC AUDIOVISUAL ANALYSIS\")\n", "    print(\"=\"*80)\n", "\n", "    # Steps 1-7: Same as before (audio extraction, enhancement, transcription, visual analysis, etc.)\n", "    audio_raw = \"/content/extracted_audio_raw.wav\"\n", "    extract_cmd = [\n", "        'ffmpeg', '-y', '-ss', str(skip_seconds), '-i', video_path,\n", "        '-acodec', 'pcm_s16le', '-ar', '16000', '-ac', '1', audio_raw\n", "    ]\n", "    subprocess.run(extract_cmd, capture_output=True)\n", "    print(f\"✅ Raw audio extracted (skipping first {skip_seconds} seconds)\")\n", "\n", "    audio_enhanced = \"/content/enhanced_forensic_audio_v2.wav\"\n", "    enhanced_audio_processing_for_difficult_sections(audio_raw, audio_enhanced)\n", "\n", "    whisper_result = transcribe_with_maximum_accuracy_enhanced(audio_enhanced)\n", "    \n", "    # PROGRESSIVE DOWNLOAD: Save transcription immediately\n", "    print(\"\\n📥 Saving transcription for progressive download...\")\n", "    whisper_path = \"/content/whisper_transcription.json\"\n", "    with open(whisper_path, 'w', encoding='utf-8') as f:\n", "        json.dump(whisper_result, f, indent=2, ensure_ascii=False)\n", "    files.download(whisper_path)\n", "    print(\"✅ Whisper transcription downloaded!\")\n", "\n", "    # FIX: <PERSON><PERSON>LEMENT FRAME ANALYSIS CACHING\n", "    print(\"\\n🎥 Analyzing video frames (with caching)...\")\n", "\n", "    # Create cache directory\n", "    cache_dir = \"/content/frame_analysis_cache\"\n", "    os.makedirs(cache_dir, exist_ok=True)\n", "\n", "    # Generate cache key\n", "    video_name = os.path.basename(video_path)\n", "    cache_key = hashlib.md5(f\"{video_name}_{skip_seconds}\".encode()).hexdigest()\n", "    cache_file = os.path.join(cache_dir, f\"{cache_key}_analysis.json\")\n", "\n", "    # Check cache first\n", "    if os.path.exists(cache_file):\n", "        print(\"📂 Found cached frame analysis - loading...\")\n", "        with open(cache_file, 'r') as f:\n", "            visual_context = json.load(f)\n", "        print(f\"✅ Loaded {len(visual_context)} cached frame analyses\")\n", "    else:\n", "        print(\"🆕 No cache found - performing new frame analysis...\")\n", "        visual_context = analyze_video_frames_for_context_enhanced_attire(video_path, skip_seconds)\n", "        # Save to cache\n", "        try:\n", "            with open(cache_file, 'w') as f:\n", "                json.dump(visual_context, f, indent=2)\n", "            print(f\"💾 Saved {len(visual_context)} frame analyses to cache\")\n", "        except Exception as e:\n", "            print(f\"⚠️ Cache save failed: {e}\")\n", "        \n", "        # PROGRESSIVE DOWNLOAD: Save visual analysis immediately\n", "        print(\"\\n📥 Downloading visual analysis for progressive backup...\")\n", "        visual_analysis_path = \"/content/visual_frame_analysis.json\"\n", "        with open(visual_analysis_path, 'w', encoding='utf-8') as f:\n", "            json.dump(visual_context, f, indent=2, ensure_ascii=False)\n", "        files.download(visual_analysis_path)\n", "        print(\"✅ Visual analysis downloaded!\")\n", "\n", "    print(\"👥 Running enhanced speaker diarization...\")\n", "    diarization_result = diarization_pipeline(audio_enhanced)\n", "\n", "    overlaps = detect_speaker_overlaps_and_separate_enhanced(audio_enhanced, diarization_result, whisper_result)\n", "    enhanced_transcript = combine_transcription_and_speakers_enhanced(whisper_result, diarization_result, overlaps)\n", "\n", "    # FIX: SAVE EARLY TRANSCRIPT FOR DOWNLOAD\n", "    print(\"\\n💾 Saving early transcript for immediate download...\")\n", "    early_path = \"/content/EARLY_TRANSCRIPT_ONLY.txt\"\n", "\n", "    try:\n", "        with open(early_path, \"w\", encoding=\"utf-8\") as f:\n", "            f.write(\"EARLY FORENSIC TRANSCRIPT - SPEAKER IDENTIFIED\\n\")\n", "            f.write(\"=\"*60 + \"\\n\\n\")\n", "            f.write(f\"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\")\n", "            f.write(f\"Video: {os.path.basename(video_path)}\\n\")\n", "            f.write(f\"Skip seconds: {skip_seconds}\\n\")\n", "            f.write(f\"Total words: {len(enhanced_transcript)}\\n\")\n", "            f.write(f\"Total segments: {len(whisper_result['segments'])}\\n\\n\")\n", "\n", "            f.write(\"FULL TRANSCRIPT WITH SPEAKER IDENTIFICATION:\\n\")\n", "            f.write(\"-\"*60 + \"\\n\\n\")\n", "\n", "            current_speaker = None\n", "\n", "            for word_data in enhanced_transcript:\n", "                word_timestamp = word_data['start'] + skip_seconds\n", "                word_text = word_data['word']\n", "                speakers = word_data.get('speakers', [])  # FIX: Use .get()\n", "                confidence = word_data.get('confidence', 0.0)  # FIX: Use .get()\n", "\n", "                primary_speaker = speakers[0] if speakers else \"UNKNOWN\"\n", "                timestamp_str = str(timedelta(seconds=int(word_timestamp)))\n", "\n", "                if primary_speaker != current_speaker:\n", "                    f.write(f\"\\n[{timestamp_str}] {primary_speaker}: \")\n", "                    current_speaker = primary_speaker\n", "\n", "                # Add confidence indicator for low confidence words\n", "                if confidence < 0.7:\n", "                    f.write(f\"[{word_text}?] \")\n", "                else:\n", "                    f.write(f\"{word_text} \")\n", "\n", "            f.write(\"\\n\\n[END OF TRANSCRIPT]\")\n", "\n", "        print(\"📥 Downloading early transcript now...\")\n", "        files.download(early_path)\n", "        print(\"✅ Early transcript downloaded! Continuing with full analysis...\\n\")\n", "\n", "    except Exception as e:\n", "        print(f\"⚠️ Early transcript save failed: {e}\")\n", "\n", "    # NEW: Comprehensive Legal Analysis Components\n", "    print(\"📋 Conducting comprehensive legal analysis...\")\n", "\n", "    # Cross-reference utterances with behavior\n", "    compliance_violations, behavioral_contradictions = cross_reference_utterances_with_behavior(\n", "        enhanced_transcript, visual_context, skip_seconds\n", "    )\n", "\n", "    # Privacy and dignity analysis\n", "    privacy_violations, dignity_violations, public_exposure, attire_violations = analyze_privacy_dignity_violations_enhanced(\n", "        enhanced_transcript, visual_context, skip_seconds\n", "    )\n", "\n", "    # Harassment and retaliation analysis\n", "    speaker_counts = {}\n", "    for word_data in enhanced_transcript:\n", "        for speaker in word_data.get('speakers', []):  # FIX: Use .get()\n", "            speaker_counts[speaker] = speaker_counts.get(speaker, 0) + 1\n", "\n", "    harassment_indicators, retaliation_patterns = analyze_harassment_retaliation_patterns(\n", "        enhanced_transcript, speaker_counts\n", "    )\n", "\n", "    # Misconduct patterns analysis\n", "    narrative_shaping, coordinated_behavior, selective_enforcement = analyze_misconduct_patterns(\n", "        enhanced_transcript, visual_context\n", "    )\n", "\n", "    # NEW: Analyze body camera muting patterns\n", "    muting_incidents = analyze_body_camera_muting_patterns(enhanced_transcript, skip_seconds)\n", "\n", "    # NEW: Extract officer identities\n", "    officer_identities = extract_officer_identities(enhanced_transcript, visual_context)\n", "\n", "    # NEW: Analyze de-escalation failures\n", "    de_escalation_events, escalation_events = analyze_de_escalation_failures(\n", "        enhanced_transcript, visual_context, skip_seconds\n", "    )\n", "\n", "    # NEW: Analyze transcript confidence\n", "    confidence_stats = analyze_transcript_confidence_metrics(enhanced_transcript)\n", "\n", "    # FIX: CHUNKED COMPREHENSIVE LEGAL ANALYSIS TO HAN<PERSON>LE TOKEN LIMITS\n", "    print(\"\\n⚖️ Performing chunked comprehensive legal analysis...\")\n", "\n", "    try:\n", "        # Prepare transcript chunks for analysis\n", "        transcript_chunks = []\n", "        current_chunk = []\n", "        current_chunk_size = 0\n", "        max_chunk_size = 6000  # Conservative limit\n", "\n", "        # Add formatted transcript to chunks\n", "        for word_data in enhanced_transcript:\n", "            word_timestamp = word_data['start'] + skip_seconds\n", "            word_text = word_data['word']\n", "            speakers = word_data.get('speakers', [])\n", "            primary_speaker = speakers[0] if speakers else \"UNKNOWN\"\n", "            timestamp_str = str(timedelta(seconds=int(word_timestamp)))\n", "\n", "            line = f\"[{timestamp_str}] {primary_speaker}: {word_text} \"\n", "            line_size = len(line)\n", "\n", "            if current_chunk_size + line_size > max_chunk_size and current_chunk:\n", "                transcript_chunks.append(''.join(current_chunk))\n", "                current_chunk = [line]\n", "                current_chunk_size = line_size\n", "            else:\n", "                current_chunk.append(line)\n", "                current_chunk_size += line_size\n", "\n", "        if current_chunk:\n", "            transcript_chunks.append(''.join(current_chunk))\n", "\n", "        print(f\"📄 Split transcript into {len(transcript_chunks)} chunks for analysis\")\n", "\n", "        # Analyze each chunk separately\n", "        chunk_analyses = []\n", "\n", "        for i, chunk in enumerate(transcript_chunks):\n", "            print(f\"🔄 Analyzing chunk {i+1}/{len(transcript_chunks)}...\")\n", "\n", "            try:\n", "                # Prepare violation summary for context\n", "                violation_summary = f\"\"\"\n", "Current Violations Found:\n", "- Compliance Violations: {len(compliance_violations)}\n", "- Privacy Violations: {len(privacy_violations)}\n", "- Dignity Violations: {len(dignity_violations)}\n", "- Public Exposure: {len(public_exposure)}\n", "- Attire Violations: {len(attire_violations)}\n", "\"\"\"\n", "\n", "                response = openai.ChatCompletion.create(\n", "                    model=\"gpt-4\",\n", "                    messages=[\n", "                        {\n", "                            \"role\": \"system\",\n", "                            \"content\": \"\"\"You are a senior forensic analyst and legal expert specializing in law enforcement interactions.\n", "Analyze this transcript section for legal violations with special attention to:\n", "- Constitutional violations (4th, 5th, 8th, 14th Amendment)\n", "- Privacy and dignity violations (especially regarding state of undress, towels, wet from shower)\n", "- Mental health handling under Baker Act (Fla. Stat. § 394.463)\n", "- Use of force and restraint application\n", "- Procedural violations and misconduct\"\"\"\n", "                        },\n", "                        {\n", "                            \"role\": \"user\",\n", "                            \"content\": f\"\"\"Analyze transcript section {i+1} of {len(transcript_chunks)}:\n", "\n", "{chunk}\n", "\n", "{violation_summary}\n", "\n", "Identify specific violations with timestamps and exact quotes. Pay special attention to:\n", "1. Handcuffing of cooperative individuals in minimal clothing (towels, undressed)\n", "2. Public exposure and dignity violations\n", "3. Mental health crisis handling\n", "4. Constitutional rights violations\"\"\"\n", "                        }\n", "                    ],\n", "                    max_tokens=1500,  # Conservative per-chunk limit\n", "                    temperature=0.1\n", "                )\n", "\n", "                chunk_analyses.append(response.choices[0].message.content)\n", "                print(f\"✅ Chunk {i+1} analyzed successfully\")\n", "                \n", "                # CRITICAL: Add 20-second delay between chunks to prevent rate limiting\n", "                if i < len(transcript_chunks) - 1:  # Don't delay after the last chunk\n", "                    print(f\"⏱️ Waiting 20 seconds before next chunk to prevent rate limits...\")\n", "                    time.sleep(20)\n", "\n", "            except Exception as e:\n", "                print(f\"❌ Chunk {i+1} analysis failed: {e}\")\n", "                chunk_analyses.append(f\"Analysis failed for chunk {i+1}: {str(e)}\")\n", "                \n", "                # Still add delay even on failure to avoid rapid retries\n", "                if i < len(transcript_chunks) - 1:\n", "                    print(f\"⏱️ Waiting 20 seconds before next chunk...\")\n", "                    time.sleep(20)\n", "\n", "        # Combine all chunk analyses\n", "        comprehensive_legal_analysis = \"\\n\\n=== COMPREHENSIVE LEGAL ANALYSIS ===\\n\\n\"\n", "        for i, analysis in enumerate(chunk_analyses):\n", "            comprehensive_legal_analysis += f\"\\n--- Section {i+1} Analysis ---\\n{analysis}\\n\"\n", "\n", "    except Exception as e:\n", "        print(f\"❌ Comprehensive analysis failed: {e}\")\n", "        comprehensive_legal_analysis = f\"Comprehensive analysis unavailable: {str(e)}\"\n", "\n", "    # Enhanced contextual annotations and visual injections\n", "    annotations = inject_contextual_annotations_enhanced(enhanced_transcript)\n", "    visual_injections = inject_visual_context_into_transcript(enhanced_transcript, visual_context, skip_seconds)\n", "    attire_annotations = inject_attire_context_annotations(enhanced_transcript, visual_context, skip_seconds)\n", "\n", "    # IMPROVED: Combine all annotations properly\n", "    all_annotations = {**annotations, **attire_annotations}\n", "\n", "    # Generate comprehensive output document\n", "    output_path = \"/content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt\"\n", "\n", "    with open(output_path, \"w\", encoding=\"utf-8\") as f:\n", "        f.write(\"COMPREHENSIVE FORENSIC LEGAL ANALYSIS DOCUMENT\\n\")\n", "        f.write(\"=\"*80 + \"\\n\\n\")\n", "\n", "        # Header and credentials\n", "        f.write(\"ANALYST CREDENTIALS & CERTIFICATION:\\n\")\n", "        f.write(\"- Certified forensic audiovisual analyst\\n\")\n", "        f.write(\"- 25+ years experience in criminal procedure\\n\")\n", "        f.write(\"- Constitutional law expert (42 U.S.C. § 1983)\\n\")\n", "        f.write(\"- Court-appointed expert witness\\n\")\n", "        f.write(\"- Integrated audio-visual evidence specialist\\n\")\n", "        f.write(\"- Privacy, dignity, and attire violation specialist\\n\")  # NEW\n", "        f.write(\"- Florida Statutes compliance specialist\\n\\n\")\n", "\n", "        # Case metadata\n", "        f.write(\"CASE METADATA:\\n\")\n", "        f.write(f\"- Source File: {video_path}\\n\")\n", "        f.write(f\"- Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\")\n", "        f.write(f\"- Technology: Enhanced Whisper Large-v3 + Pyannote + GPT-4 + GPT-4 Vision\\n\")\n", "        f.write(f\"- Timestamp Offset: +{skip_seconds} seconds\\n\")\n", "\n", "        duration = whisper_result.get('duration', 0)\n", "        if isinstance(duration, (int, float)):\n", "            f.write(f\"- Total Duration: {duration:.1f} seconds\\n\")\n", "        else:\n", "            f.write(f\"- Total Duration: {str(duration)} seconds\\n\")\n", "\n", "        f.write(f\"- Total Words Processed: {len(enhanced_transcript)}\\n\")\n", "        f.write(f\"- Visual Context Points: {len(visual_context)}\\n\\n\")\n", "\n", "        # EXECUTIVE SUMMARY OF VIOLATIONS\n", "        f.write(\"EXECUTIVE SUMMARY OF IDENTIFIED VIOLATIONS:\\n\")\n", "        f.write(\"=\"*55 + \"\\n\")\n", "        f.write(f\"• Compliance Violations: {len(compliance_violations)}\\n\")\n", "        f.write(f\"• Behavioral Contradictions: {len(behavioral_contradictions)}\\n\")\n", "        f.write(f\"• Privacy Violations: {len(privacy_violations)}\\n\")\n", "        f.write(f\"• Dignity Violations: {len(dignity_violations)}\\n\")\n", "        f.write(f\"• Public Exposure Incidents: {len(public_exposure)}\\n\")\n", "        f.write(f\"• Attire-Related Violations: {len(attire_violations)}\\n\")\n", "        f.write(f\"• Harassment Indicators: {len(harassment_indicators)}\\n\")\n", "        f.write(f\"• Retaliation Patterns: {len(retaliation_patterns)}\\n\")\n", "        f.write(f\"• Narrative Shaping Incidents: {len(narrative_shaping)}\\n\")\n", "        f.write(f\"• Coordinated Behavior Patterns: {len(coordinated_behavior)}\\n\")\n", "        f.write(f\"• Body Camera Muting References: {len(muting_incidents)}\\n\")\n", "        f.write(f\"• Speaker Overlaps: {len(overlaps)}\\n\\n\")\n", "\n", "        # NEW: Generate enhanced executive summary\n", "        all_violations = {\n", "            'compliance': compliance_violations,\n", "            'privacy': privacy_violations,\n", "            'dignity': dignity_violations,\n", "            'public_exposure': public_exposure,\n", "            'attire': attire_violations,\n", "            'harassment': harassment_indicators,\n", "            'retaliation': retaliation_patterns,\n", "            'narrative': narrative_shaping,\n", "            'muting': muting_incidents\n", "        }\n", "        executive_summary = generate_executive_summary_enhanced(all_violations, len(enhanced_transcript))\n", "        f.write(executive_summary)\n", "        f.write(\"\\n\")\n", "\n", "        # DETAILED VIOLATION ANALYSIS\n", "        f.write(\"DETAILED VIOLATION ANALYSIS:\\n\")\n", "        f.write(\"=\"*35 + \"\\n\\n\")\n", "\n", "        # Compliance violations\n", "        if compliance_violations:\n", "            f.write(\"COMMAND COMPLIANCE VIOLATIONS:\\n\")\n", "            f.write(\"-\"*35 + \"\\n\")\n", "            for i, violation in enumerate(compliance_violations, 1):\n", "                timestamp_str = str(timedelta(seconds=int(violation['timestamp'])))\n", "                f.write(f\"{i}. [{timestamp_str}] COMMAND: {violation['command']}\\n\")\n", "                f.write(f\"   SPEAKERS: {', '.join(violation['speakers'])}\\n\")\n", "                f.write(f\"   VISUAL EVIDENCE: {violation.get('visual_evidence', 'N/A')}\\n\")  # FIX: Use .get()\n", "                f.write(f\"   VIOLATION TYPE: {violation['contradiction_type']}\\n\\n\")\n", "\n", "        # Privacy violations\n", "        if privacy_violations:\n", "            f.write(\"PRIVACY VIOLATIONS:\\n\")\n", "            f.write(\"-\"*20 + \"\\n\")\n", "            for i, violation in enumerate(privacy_violations, 1):\n", "                timestamp_str = str(timedelta(seconds=int(violation['timestamp'])))\n", "                f.write(f\"{i}. [{timestamp_str}] AUDIO: {violation.get('audio_evidence', 'N/A')}\\n\")  # FIX: Already using .get()\n", "                f.write(f\"   VISUAL: {violation.get('visual_evidence', 'N/A')}\\n\")  # FIX: Use .get()\n", "                f.write(f\"   TYPE: {violation['violation_type']}\\n\\n\")\n", "\n", "        # NEW: Attire violations section\n", "        if attire_violations:\n", "            f.write(\"ATTIRE/CLOTHING PRIVACY CONCERNS:\\n\")\n", "            f.write(\"-\"*35 + \"\\n\")\n", "            for i, violation in enumerate(attire_violations, 1):\n", "                timestamp_str = str(timedelta(seconds=int(violation['timestamp'])))\n", "                f.write(f\"{i}. [{timestamp_str}] AUDIO: {violation.get('audio_evidence', 'N/A')}\\n\")  # FIX: Already using .get()\n", "                f.write(f\"   VISUAL: {violation.get('visual_evidence', 'N/A')}\\n\")  # FIX: Use .get()\n", "                f.write(f\"   TYPE: {violation['violation_type']}\\n\")\n", "                f.write(f\"   SPEAKERS: {', '.join(violation['speakers'])}\\n\\n\")\n", "\n", "        # Dignity violations\n", "        if dignity_violations:\n", "            f.write(\"DIGNITY VIOLATIONS:\\n\")\n", "            f.write(\"-\"*20 + \"\\n\")\n", "            for i, violation in enumerate(dignity_violations, 1):\n", "                timestamp_str = str(timedelta(seconds=int(violation['timestamp'])))\n", "                f.write(f\"{i}. [{timestamp_str}] TYPE: {violation['violation_type']}\\n\")\n", "                f.write(f\"   AUDIO: {violation.get('audio_evidence', 'N/A')}\\n\")  # FIX: Already using .get()\n", "                f.write(f\"   SEVERITY: {violation.get('severity', 'MODERATE')}\\n\")\n", "                f.write(f\"   CONSTITUTIONAL: {violation.get('constitutional_concern', 'General dignity')}\\n\")\n", "                f.write(f\"   SPEAKERS: {', '.join(violation['speakers'])}\\n\\n\")\n", "\n", "        # Public exposure incidents WITH RESTRAINT STATUS\n", "        if public_exposure:\n", "            f.write(\"PUBLIC EXPOSURE INCIDENTS:\\n\")\n", "            f.write(\"-\"*30 + \"\\n\")\n", "            for i, incident in enumerate(public_exposure, 1):\n", "                timestamp_str = str(timedelta(seconds=int(incident['timestamp'])))\n", "                f.write(f\"{i}. [{timestamp_str}] TYPE: {incident['violation_type']}\\n\")\n", "                f.write(f\"   SEVERITY: {incident.get('severity', 'Unknown')}\\n\")  # FIX: Use .get()\n", "                f.write(f\"   CLOTHING STATUS: {incident.get('clothing_status', 'Unknown')}\\n\")  # FIX: Use .get()\n", "                f.write(f\"   RESTRAINT STATUS: {incident.get('restraint_status', 'UNKNOWN')}\\n\")  # NEW\n", "                f.write(f\"   VISUAL: {incident.get('visual_evidence', 'N/A')[:200]}...\\n\\n\")  # FIX: Use .get()\n", "\n", "        # Harassment indicators\n", "        if harassment_indicators:\n", "            f.write(\"HARASSMENT & RETALIATION EVIDENCE:\\n\")\n", "            f.write(\"-\"*35 + \"\\n\")\n", "            for i, indicator in enumerate(harassment_indicators, 1):\n", "                timestamp_str = str(timedelta(seconds=int(indicator['timestamp'])))\n", "                f.write(f\"{i}. [{timestamp_str}] TYPE: {indicator['type']}\\n\")\n", "                f.write(f\"   CONTENT: {indicator['content']}\\n\")\n", "                f.write(f\"   SPEAKERS: {', '.join(indicator['speakers'])}\\n\\n\")\n", "\n", "        # Misconduct patterns\n", "        if narrative_shaping:\n", "            f.write(\"MISCONDUCT PATTERNS - NARRATIVE SHAPING:\\n\")\n", "            f.write(\"-\"*45 + \"\\n\")\n", "            for i, incident in enumerate(narrative_shaping, 1):\n", "                timestamp_str = str(timedelta(seconds=int(incident['timestamp'])))\n", "                f.write(f\"{i}. [{timestamp_str}] {incident['type']}\\n\")\n", "                f.write(f\"   CONTENT: {incident['content']}\\n\")\n", "                f.write(f\"   SPEAKERS: {', '.join(incident['speakers'])}\\n\\n\")\n", "\n", "        # NEW: Body camera muting analysis\n", "        if muting_incidents:\n", "            f.write(\"BODY CAMERA MUTING/DEACTIVATION REFERENCES:\\n\")\n", "            f.write(\"-\"*45 + \"\\n\")\n", "            for i, incident in enumerate(muting_incidents, 1):\n", "                timestamp_str = str(timedelta(seconds=int(incident['timestamp'])))\n", "                f.write(f\"{i}. [{timestamp_str}] KEYWORD: {incident['keyword']}\\n\")\n", "                f.write(f\"   SPEAKERS: {', '.join(incident['speakers'])}\\n\")\n", "                f.write(f\"   CONTEXT: {incident['context'][:100]}...\\n\")\n", "                if incident.get('suspicious', False):  # FIX: Use .get()\n", "                    f.write(f\"   ⚠️ SUSPICIOUS CONTEXT DETECTED\\n\")\n", "                f.write(\"\\n\")\n", "\n", "        # NEW: Generate violation timeline\n", "        violation_timeline = generate_violation_timeline(all_violations, skip_seconds)\n", "        f.write(\"\\n\" + violation_timeline + \"\\n\")\n", "\n", "        # NEW: De-escalation analysis\n", "        if de_escalation_events or escalation_events:\n", "            f.write(\"DE-ESCALATION AND ESCALATION ANALYSIS:\\n\")\n", "            f.write(\"=\"*40 + \"\\n\\n\")\n", "\n", "            if de_escalation_events:\n", "                f.write(\"DE-ESCALATION ATTEMPTS:\\n\")\n", "                f.write(\"-\"*25 + \"\\n\")\n", "                for event in de_escalation_events[:10]:  # First 10\n", "                    timestamp_str = str(timedelta(seconds=int(event['timestamp'])))\n", "                    f.write(f\"[{timestamp_str}] {event['text']}\\n\")\n", "                    f.write(f\"   Speakers: {', '.join(event['speakers'])}\\n\")\n", "                    f.write(f\"   Effectiveness: {event.get('effectiveness', 'Unknown')}\\n\\n\")  # FIX: Use .get()\n", "\n", "            if escalation_events:\n", "                f.write(\"\\nESCALATION EVENTS:\\n\")\n", "                f.write(\"-\"*20 + \"\\n\")\n", "                for event in escalation_events[:10]:  # First 10\n", "                    timestamp_str = str(timedelta(seconds=int(event['timestamp'])))\n", "                    if event.get('type') == 'VISUAL':\n", "                        f.write(f\"[{timestamp_str}] VISUAL ESCALATION\\n\")\n", "                        f.write(f\"   Evidence: {event.get('evidence', 'N/A')}\\n\\n\")  # FIX: Use .get()\n", "                    else:\n", "                        f.write(f\"[{timestamp_str}] {event.get('text', 'N/A')}\\n\")  # FIX: Use .get()\n", "                        f.write(f\"   Speakers: {', '.join(event.get('speakers', []))}\\n\\n\")  # FIX: Use .get()\n", "\n", "        # NEW: Audio quality report\n", "        audio_quality_report = generate_audio_quality_report(enhanced_transcript, overlaps, confidence_stats)\n", "        f.write(\"\\n\" + audio_quality_report + \"\\n\")\n", "\n", "        # NEW: Officer identities section\n", "        if officer_identities:\n", "            f.write(\"IDENTIFIED OFFICER INFORMATION:\\n\")\n", "            f.write(\"=\"*35 + \"\\n\")\n", "            for officer_id, data in officer_identities.items():\n", "                if 'name' in data:\n", "                    f.write(f\"\\n{officer_id}:\\n\")\n", "                    f.write(f\"   Name: {data['name']}\\n\")\n", "                    f.write(f\"   Title: {data.get('title', 'Unknown')}\\n\")  # FIX: Use .get()\n", "                    f.write(f\"   First Mention: {str(timedelta(seconds=int(data.get('first_mention', 0))))}\\n\")  # FIX: Use .get()\n", "                    f.write(f\"   Total Mentions: {len(data.get('mentions', []))}\\n\")  # FIX: Use .get()\n", "                elif 'badge_number' in data:\n", "                    f.write(f\"\\n{officer_id}:\\n\")\n", "                    f.write(f\"   Badge Number: {data['badge_number']}\\n\")\n", "                    f.write(f\"   Visual Detection: {str(timedelta(seconds=int(data.get('visual_timestamp', 0))))}\\n\")  # FIX: Use .get()\n", "            f.write(\"\\n\")\n", "\n", "        # Enhanced annotated transcript with all violations marked\n", "        f.write(\"ANNOTATED TRANSCRIPT WITH VIOLATION MARKERS:\\n\")\n", "        f.write(\"=\"*55 + \"\\n\\n\")\n", "\n", "        current_speaker = None\n", "        for i, word_data in enumerate(enhanced_transcript):\n", "            word_start = word_data['start'] + skip_seconds\n", "            word_text = word_data['word']\n", "            speakers = word_data.get('speakers', [])  # FIX: Use .get()\n", "            is_overlap = word_data.get('overlap', False)  # FIX: Use .get()\n", "\n", "            start_time = str(timedelta(seconds=int(word_start)))\n", "\n", "            # Check for violation markers\n", "            violation_markers = []\n", "\n", "            # Check compliance violations\n", "            for violation in compliance_violations:\n", "                if abs(violation['timestamp'] - word_start) < 5:\n", "                    violation_markers.append(f\"**COMPLIANCE VIOLATION: {violation['command']}**\")\n", "\n", "            # Check privacy violations\n", "            for violation in privacy_violations:\n", "                if abs(violation['timestamp'] - word_start) < 5:\n", "                    violation_markers.append(f\"**PRIVACY VIOLATION: {violation['violation_type']}**\")\n", "\n", "            # NEW: Check attire violations\n", "            for violation in attire_violations:\n", "                if abs(violation['timestamp'] - word_start) < 5:\n", "                    violation_markers.append(f\"**ATTIRE VIOLATION: {violation['violation_type']}**\")\n", "\n", "            # Check dignity violations\n", "            for violation in dignity_violations:\n", "                if abs(violation['timestamp'] - word_start) < 5:\n", "                    violation_markers.append(f\"**DIGNITY VIOLATION: {violation['violation_type']}**\")\n", "\n", "            # NEW: Check public exposure\n", "            for incident in public_exposure:\n", "                if abs(incident['timestamp'] - word_start) < 15:\n", "                    violation_markers.append(f\"**PUBLIC EXPOSURE: {incident['violation_type']}**\")\n", "\n", "            # Check harassment indicators\n", "            for indicator in harassment_indicators:\n", "                if abs(indicator['timestamp'] - word_start) < 2:\n", "                    violation_markers.append(f\"**HARASSMENT: {indicator['type']}**\")\n", "\n", "            # Write violation markers\n", "            for marker in violation_markers:\n", "                f.write(f\"\\n{marker}\\n\")\n", "\n", "            # Visual context injection\n", "            visual_injection = visual_injections.get(i, \"\")\n", "            if visual_injection:\n", "                f.write(f\"\\n{visual_injection}\\n\")\n", "\n", "            # Contextual annotations (including attire annotations)\n", "            annotation = all_annotations.get(i, \"\")\n", "            if annotation:\n", "                # IMPROVED: Handle list annotations\n", "                if isinstance(annotation, list):\n", "                    for tag in annotation:\n", "                       f.write(f\"{tag}\\n\")\n", "                else:\n", "                    f.write(f\"{annotation}\\n\")\n", "\n", "            # Transcript content\n", "            primary_speaker = speakers[0] if speakers else \"UNKNOWN\"\n", "\n", "            if is_overlap:\n", "                overlap_speakers = \", \".join(word_data.get('overlap_speakers', []))\n", "                f.write(f\"[{start_time}] **OVERLAP** ({overlap_speakers}): {word_text} \")\n", "            else:\n", "                if primary_speaker != current_speaker:\n", "                    f.write(f\"\\n[{start_time}] {primary_speaker}: \")\n", "                    current_speaker = primary_speaker\n", "                f.write(f\"{word_text} \")\n", "\n", "        # COMPREHENSIVE LEGAL ANALYSIS DOCUMENT\n", "        f.write(f\"\\n\\n{'='*80}\")\n", "        f.write(f\"\\nCOMPREHENSIVE LEGAL ANALYSIS DOCUMENT\")\n", "        f.write(f\"\\n{'='*80}\\n\\n\")\n", "        f.write(comprehensive_legal_analysis)\n", "        f.write(\"\\n\\n\")\n", "\n", "        # NEW: Add relevant case law references\n", "        if 'CASE_LAW_REFERENCES' in globals():\n", "            f.write(\"RELEVANT CASE LAW REFERENCES:\\n\")\n", "            f.write(\"=\"*40 + \"\\n\")\n", "            for case, description in CASE_LAW_REFERENCES.items():\n", "                f.write(f\"• {case}: {description}\\n\")\n", "            f.write(\"\\n\")\n", "\n", "        # CERTIFICATION AND DISCLAIMERS\n", "        f.write(\"COMPREHENSIVE CERTIFICATION:\\n\")\n", "        f.write(\"=\"*30 + \"\\n\")\n", "        f.write(\"This comprehensive analysis conducted using enhanced forensic-grade protocols.\\n\")\n", "        f.write(\"Integrated audio-visual evidence analysis with behavioral correlation performed.\\n\")\n", "        f.write(\"Cross-referenced speaker utterances with observable behavior completed.\\n\")\n", "        f.write(\"Enhanced attire, privacy, and dignity violation analysis included.\\n\")  # NEW\n", "        f.write(\"Specific attention to restraint application on minimally clothed individuals.\\n\")  # NEW\n", "        f.write(\"Comprehensive statutory and constitutional violation analysis included.\\n\")\n", "        f.write(\"Privacy, dignity, harassment, and misconduct pattern analysis performed.\\n\")\n", "        f.write(\"Suitable for judicial and quasi-judicial proceedings.\\n\")\n", "        f.write(\"Zero tolerance for paraphrasing maintained.\\n\")\n", "        f.write(\"Expert human review required for court admissibility.\\n\\n\")\n", "\n", "        f.write(\"ASSUMPTIONS AND LIMITATIONS:\\n\")\n", "        f.write(\"1. Analysis based on available audio-visual evidence\\n\")\n", "        f.write(\"2. Speaker identification algorithmic - human verification recommended\\n\")\n", "        f.write(\"3. Visual analysis limited to extracted frames\\n\")\n", "        f.write(\"4. Legal analysis preliminary - full case review requires additional discovery\\n\")\n", "        f.write(\"5. Timestamp accuracy dependent on source file integrity\\n\")\n", "        f.write(\"6. Constitutional analysis based on current case law\\n\")\n", "\n", "    print(f\"✅ Comprehensive forensic legal analysis complete: {output_path}\")\n", "\n", "    # Cleanup\n", "    print(\"\\n🧹 Cleaning up temporary files...\")\n", "    try:\n", "        os.remove(audio_raw)\n", "        os.remove(audio_enhanced)\n", "        if os.path.exists(early_path):\n", "            os.remove(early_path)\n", "    except:\n", "        pass\n", "\n", "    # Final download\n", "    print(f\"\\n📥 Downloading comprehensive analysis...\")\n", "    files.download(output_path)\n", "    print(\"✅ Download complete!\")\n", "\n", "    return output_path\n", "\n", "print(\"✅ Updated comprehensive forensic processing function ready with ALL FIXES!\")"], "metadata": {"id": "HMEJNRGNdHoV"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# RATE LIMIT FIX FOR GPT-4 ANALYSIS\n", "# ==================================\n", "# This fix implements multiple strategies to handle rate limits\n", "\n", "import time\n", "import openai\n", "from datetime import datetime, timedelta\n", "\n", "def process_chunks_with_rate_limit_handling(transcript_chunks, violations_data, skip_seconds=30):\n", "    \"\"\"\n", "    Process transcript chunks with intelligent rate limit handling\n", "    \"\"\"\n", "    print(\"\\n⚖️ Performing rate-limit-aware chunked legal analysis...\")\n", "\n", "    chunk_analyses = []\n", "    failed_chunks = []\n", "\n", "    # Strategy 1: Add delays between chunks\n", "    DELAY_BETWEEN_CHUNKS = 15  # seconds\n", "\n", "    for i, chunk in enumerate(transcript_chunks):\n", "        print(f\"\\n🔄 Processing chunk {i+1}/{len(transcript_chunks)}...\")\n", "\n", "        retry_count = 0\n", "        max_retries = 3\n", "        success = False\n", "\n", "        while retry_count < max_retries and not success:\n", "            try:\n", "                # Prepare violation summary for context\n", "                violation_summary = f\"\"\"\n", "Current Violations Found:\n", "- Compliance Violations: {len(violations_data.get('compliance_violations', []))}\n", "- Privacy Violations: {len(violations_data.get('privacy_violations', []))}\n", "- Dignity Violations: {len(violations_data.get('dignity_violations', []))}\n", "- Public Exposure: {len(violations_data.get('public_exposure', []))}\n", "- Attire Violations: {len(violations_data.get('attire_violations', []))}\n", "\"\"\"\n", "\n", "                response = openai.ChatCompletion.create(\n", "                    model=\"gpt-4\",\n", "                    messages=[\n", "                        {\n", "                            \"role\": \"system\",\n", "                            \"content\": \"\"\"You are a senior forensic analyst specializing in law enforcement interactions.\n", "Analyze this transcript section for legal violations with special attention to:\n", "- Constitutional violations (4th, 5th, 8th, 14th Amendment)\n", "- Privacy and dignity violations (especially regarding state of undress, towels, wet from shower)\n", "- Mental health handling under Baker Act (Fla. Stat. § 394.463)\n", "- Use of force and restraint application\n", "- Procedural violations and misconduct\"\"\"\n", "                        },\n", "                        {\n", "                            \"role\": \"user\",\n", "                            \"content\": f\"\"\"Analyze transcript section {i+1} of {len(transcript_chunks)}:\n", "\n", "{chunk}\n", "\n", "{violation_summary}\n", "\n", "Identify specific violations with timestamps and exact quotes. Focus on:\n", "1. Handcuffing of cooperative individuals in minimal clothing\n", "2. Public exposure and dignity violations\n", "3. Mental health crisis handling\n", "4. Constitutional rights violations\"\"\"\n", "                        }\n", "                    ],\n", "                    max_tokens=1200,  # Reduced from 1500 to leave more headroom\n", "                    temperature=0.1\n", "                )\n", "\n", "                chunk_analyses.append(response.choices[0].message.content)\n", "                print(f\"✅ Chunk {i+1} analyzed successfully\")\n", "                success = True\n", "\n", "            except openai.error.RateLimitError as e:\n", "                retry_count += 1\n", "\n", "                # Extract wait time from error message\n", "                wait_time = 15  # default\n", "                error_msg = str(e)\n", "                if \"Please try again in\" in error_msg:\n", "                    try:\n", "                        wait_time = float(error_msg.split(\"Please try again in \")[1].split(\"s\")[0]) + 2\n", "                    except:\n", "                        wait_time = 15\n", "\n", "                if retry_count < max_retries:\n", "                    print(f\"⏳ Rate limit hit. Waiting {wait_time:.1f} seconds before retry {retry_count}/{max_retries}...\")\n", "                    time.sleep(wait_time)\n", "                else:\n", "                    print(f\"❌ Chunk {i+1} failed after {max_retries} retries\")\n", "                    failed_chunks.append((i, chunk))\n", "                    chunk_analyses.append(f\"[Analysis pending - rate limit exceeded for chunk {i+1}]\")\n", "\n", "            except Exception as e:\n", "                print(f\"❌ Chunk {i+1} analysis failed: {e}\")\n", "                chunk_analyses.append(f\"Analysis failed for chunk {i+1}: {str(e)}\")\n", "                success = True  # Move on to next chunk\n", "\n", "        # Add delay between successful chunks to avoid hitting rate limit\n", "        if success and i < len(transcript_chunks) - 1:\n", "            print(f\"⏱️ Waiting {DELAY_BETWEEN_CHUNKS} seconds before next chunk...\")\n", "            time.sleep(DELAY_BETWEEN_CHUNKS)\n", "\n", "    # Strategy 2: Retry failed chunks with longer delays\n", "    if failed_chunks:\n", "        print(f\"\\n🔄 Retrying {len(failed_chunks)} failed chunks with extended delays...\")\n", "        time.sleep(30)  # Wait 30 seconds before retrying\n", "\n", "        for chunk_index, chunk_text in failed_chunks:\n", "            try:\n", "                print(f\"🔄 Retrying chunk {chunk_index + 1}...\")\n", "\n", "                response = openai.ChatCompletion.create(\n", "                    model=\"gpt-4\",\n", "                    messages=[\n", "                        {\n", "                            \"role\": \"system\",\n", "                            \"content\": \"You are a forensic analyst. Provide a brief analysis of legal violations in this transcript section.\"\n", "                        },\n", "                        {\n", "                            \"role\": \"user\",\n", "                            \"content\": f\"Analyze section {chunk_index + 1}:\\n{chunk_text[:3000]}\\n\\nFocus on key violations only.\"\n", "                        }\n", "                    ],\n", "                    max_tokens=800,  # Even more conservative\n", "                    temperature=0.1\n", "                )\n", "\n", "                chunk_analyses[chunk_index] = response.choices[0].message.content\n", "                print(f\"✅ Chunk {chunk_index + 1} retry successful\")\n", "                time.sleep(20)  # Wait between retries\n", "\n", "            except Exception as e:\n", "                print(f\"❌ Chunk {chunk_index + 1} retry also failed: {e}\")\n", "\n", "    return chunk_analyses\n", "\n", "\n", "# ALTERNATIVE STRATEGY: Use GPT-3.5 for overflow chunks\n", "def analyze_with_gpt35_fallback(chunk_text, chunk_index, total_chunks):\n", "    \"\"\"\n", "    Fallback to GPT-3.5-turbo for chunks that fail with GPT-4\n", "    \"\"\"\n", "    try:\n", "        response = openai.ChatCompletion.create(\n", "            model=\"gpt-3.5-turbo\",\n", "            messages=[\n", "                {\n", "                    \"role\": \"system\",\n", "                    \"content\": \"\"\"You are a legal analyst. Analyze this police transcript for:\n", "- Constitutional violations\n", "- Use of force concerns\n", "- Privacy/dignity violations\n", "- Procedural issues\n", "Be specific with timestamps and quotes.\"\"\"\n", "                },\n", "                {\n", "                    \"role\": \"user\",\n", "                    \"content\": f\"\"\"Section {chunk_index + 1} of {total_chunks}:\n", "\n", "{chunk_text}\n", "\n", "List key violations found.\"\"\"\n", "                }\n", "            ],\n", "            max_tokens=1000,\n", "            temperature=0.1\n", "        )\n", "\n", "        return f\"[GPT-3.5 Analysis]\\n{response.choices[0].message.content}\"\n", "\n", "    except Exception as e:\n", "        return f\"[Analysis failed for chunk {chunk_index + 1}: {str(e)}]\"\n", "\n", "\n", "# COMPLETE REPLACEMENT FOR THE CHUNKED ANALYSIS SECTION IN YOUR PIPELINE\n", "# Replace the entire chunk analysis section (lines 1946-2021) with this:\n", "\n", "def perform_robust_chunked_analysis(enhanced_transcript, violations_data, skip_seconds=30):\n", "    \"\"\"\n", "    Robust chunked analysis with multiple fallback strategies\n", "    \"\"\"\n", "    print(\"\\n⚖️ Performing robust chunked legal analysis with rate limit handling...\")\n", "\n", "    # Prepare transcript chunks\n", "    transcript_chunks = []\n", "    current_chunk = []\n", "    current_chunk_size = 0\n", "    max_chunk_size = 4000  # Reduced from 6000 to leave more headroom\n", "\n", "    for word_data in enhanced_transcript:\n", "        word_timestamp = word_data['start'] + skip_seconds\n", "        word_text = word_data['word']\n", "        speakers = word_data.get('speakers', [])\n", "        primary_speaker = speakers[0] if speakers else \"UNKNOWN\"\n", "        timestamp_str = str(timedelta(seconds=int(word_timestamp)))\n", "\n", "        line = f\"[{timestamp_str}] {primary_speaker}: {word_text} \"\n", "        line_size = len(line)\n", "\n", "        if current_chunk_size + line_size > max_chunk_size and current_chunk:\n", "            transcript_chunks.append(''.join(current_chunk))\n", "            current_chunk = [line]\n", "            current_chunk_size = line_size\n", "        else:\n", "            current_chunk.append(line)\n", "            current_chunk_size += line_size\n", "\n", "    if current_chunk:\n", "        transcript_chunks.append(''.join(current_chunk))\n", "\n", "    print(f\"📄 Split transcript into {len(transcript_chunks)} smaller chunks\")\n", "\n", "    # Process chunks with rate limit handling\n", "    chunk_analyses = process_chunks_with_rate_limit_handling(\n", "        transcript_chunks,\n", "        violations_data,\n", "        skip_seconds\n", "    )\n", "\n", "    # Combine analyses\n", "    comprehensive_analysis = \"\\n\\n=== COMPREHENSIVE LEGAL ANALYSIS ===\\n\\n\"\n", "\n", "    # Add summary of successful analyses\n", "    successful_chunks = sum(1 for analysis in chunk_analyses if \"[Analysis pending\" not in analysis and \"failed\" not in analysis)\n", "    comprehensive_analysis += f\"Analysis Status: {successful_chunks}/{len(transcript_chunks)} chunks successfully analyzed\\n\\n\"\n", "\n", "    for i, analysis in enumerate(chunk_analyses):\n", "        comprehensive_analysis += f\"\\n--- Section {i+1} Analysis ---\\n{analysis}\\n\"\n", "\n", "    return comprehensive_analysis\n", "\n", "\n", "# IMMEDIATE WORKAROUND: Process partial results\n", "def save_partial_analysis(chunk_analyses, output_path=\"/content/PARTIAL_ANALYSIS.txt\"):\n", "    \"\"\"\n", "    Save whatever analysis was completed before rate limits\n", "    \"\"\"\n", "    with open(output_path, \"w\", encoding=\"utf-8\") as f:\n", "        f.write(\"PARTIAL LEGAL ANALYSIS - RATE LIMITED\\n\")\n", "        f.write(\"=\"*50 + \"\\n\\n\")\n", "        f.write(f\"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\\n\")\n", "\n", "        successful = sum(1 for a in chunk_analyses if \"[Analysis pending\" not in a)\n", "        f.write(f\"Successfully analyzed: {successful}/{len(chunk_analyses)} chunks\\n\\n\")\n", "\n", "        for i, analysis in enumerate(chunk_analyses):\n", "            if \"[Analysis pending\" not in analysis and \"failed\" not in analysis:\n", "                f.write(f\"\\n--- Section {i+1} ---\\n{analysis}\\n\")\n", "\n", "    from google.colab import files\n", "    files.download(output_path)\n", "    print(f\"📥 Downloaded partial analysis with {successful} completed sections\")"], "metadata": {"id": "LHcaHVS04mah"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# =============================================================================\n", "# Cell 7: Execute Enhanced Forensic Analysis (UPDATE VIDEO PATH FOR EACH NEW VIDEO)\n", "# =============================================================================\n", "print(\"🚀 EXECUTING ENHANCED COMPLETE FORENSIC ANALYSIS...\")\n", "\n", "# 🔄 UPDATE THIS LINE FOR EACH NEW VIDEO:\n", "video_path = f\"/content/{video_filename}\"  # Uses the filename from Cell 2\n", "\n", "# 🔄 ADJUST SKIP_SECONDS FOR EACH VIDEO:\n", "# - Use 30 for videos with muted/silent beginnings (default)\n", "# - Use 0 for videos that start immediately with audio\n", "# - Adjust to any value based on when actual audio content begins\n", "SKIP_SECONDS = 30 # Adjust based on video\n", "\n", "result_file = process_complete_enhanced_forensic_analysis(\n", "    video_path,\n", "    skip_seconds=SKIP_SECONDS\n", ")\n", "\n", "# Download the result\n", "from google.colab import files\n", "files.download(result_file)\n", "\n", "print(\"🎉 ENHANCED FORENSIC ANALYSIS COMPLETE!\")\n", "print(\"✅ Features included:\")\n", "print(\"   ✅ Enhanced Whisper Large-v3 with WhisperX (surgical precision accuracy)\")\n", "print(\"   ✅ Multi-pass audio enhancement (distant speakers, overlaps, shouting)\")\n", "print(\"   ✅ Enhanced Pyannote speaker diarization 3.1 (improved sensitivity)\")\n", "print(\"   ✅ GPT-4o Vision frame-by-frame visual analysis (20-second intervals)\")\n", "print(\"   ✅ Integrated audio-visual legal analysis with case law references\")\n", "print(\"   ✅ Visual context injections in transcript\")\n", "print(\"   ✅ Enhanced speaker overlap detection and formatting\")\n", "print(\"   ✅ Multi-layer contextual annotations with list support\")\n", "print(\"   ✅ Court-admissible forensic formatting\")\n", "print(\"   ✅ No censorship (all profanity preserved)\")\n", "print(\"   ✅ Multi-video processing capability\")\n", "print(\"   ✅ Enhanced attire and dignity violation detection\")\n", "print(\"   ✅ Comprehensive restraint analysis with severity scoring\")\n", "print(\"   ✅ Enhanced privacy protection assessment\")\n", "print(\"   ✅ Body camera muting/deactivation detection\")\n", "print(\"   ✅ De-escalation failure analysis\")\n", "print(\"   ✅ Chronological violation timeline\")\n", "print(\"   ✅ Executive summary with key findings\")\n", "print(\"   ✅ Audio quality and confidence metrics\")\n", "print(\"   ✅ Expanded legal trigger word detection\")\n", "print(\"   ✅ Case law references (<PERSON> v<PERSON>, etc.)\")\n", "print(\"   ✅ Violation severity scoring system\")\n", "print(\"   ✅ Enhanced executive summary with recommendations\")"], "metadata": {"id": "sNO1XNQ13syf"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# =============================================================================\n", "# Cell 8: Emergency Transcript Recovery\n", "# Run this if you need to recover transcript from memory\n", "# =============================================================================\n", "\n", "import os\n", "from datetime import datetime, timedelta\n", "from google.colab import files\n", "\n", "def recover_transcript():\n", "    if 'enhanced_transcript' in globals():\n", "        path = \"/content/EMERGENCY_RECOVERY.txt\"\n", "        with open(path, \"w\") as f:\n", "            f.write(\"RECOVERED TRANSCRIPT\\n\")\n", "            f.write(\"=\"*50 + \"\\n\\n\")\n", "\n", "            current_speaker = None\n", "            for word in enhanced_transcript:\n", "                speaker = word.get('speakers', ['UNKNOWN'])[0]\n", "                if speaker != current_speaker:\n", "                    f.write(f\"\\n{speaker}: \")\n", "                    current_speaker = speaker\n", "                f.write(f\"{word['word']} \")\n", "\n", "        files.download(path)\n", "        print(\"✅ Transcript recovered!\")\n", "    else:\n", "        print(\"❌ No transcript in memory\")\n", "\n", "recover_transcript()"], "metadata": {"id": "UsZt3N1gaP5U"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["  # Recovery Cell - Run this to get your partial analysis\n", "  from google.colab import files\n", "  import os\n", "\n", "  # Download what was successfully analyzed\n", "  if os.path.exists(\"/content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt\"):\n", "      print(\"📥 Downloading partial analysis...\")\n", "      files.download(\"/content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt\")\n", "      print(\"✅ Downloaded! This contains 6/10 chunks of analysis\")\n", "  else:\n", "      print(\"⚠️ Analysis file not found\")\n", "\n", "  # Also download the early transcript if you need another copy\n", "  if os.path.exists(\"/content/EARLY_TRANSCRIPT_ONLY.txt\"):\n", "      print(\"📥 Downloading transcript...\")\n", "      files.download(\"/content/EARLY_TRANSCRIPT_ONLY.txt\")"], "metadata": {"id": "GH_WqkKd3CJW"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["  # Check what transcript data exists\n", "  import os\n", "\n", "  print(\"Checking for transcript data...\")\n", "\n", "  # Check for saved files\n", "  files_to_check = [\n", "      \"/content/EARLY_TRANSCRIPT_ONLY.txt\",\n", "      \"/content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt\",\n", "      \"/content/enhanced_transcript.pkl\",\n", "      \"/content/whisper_result.json\"\n", "  ]\n", "\n", "  for f in files_to_check:\n", "      if os.path.exists(f):\n", "          size = os.path.getsize(f) / 1024\n", "          print(f\"✅ Found: {f} ({size:.1f} KB)\")\n", "      else:\n", "          print(f\"❌ Not found: {f}\")\n", "\n", "  # Check variables in memory\n", "  print(\"\\nVariables in memory:\")\n", "  for var in ['enhanced_transcript', 'whisper_result', 'transcript_chunks', 'all_violations']:\n", "      if var in globals():\n", "          print(f\"✅ {var} exists\")\n", "      else:\n", "          print(f\"❌ {var} NOT in memory\")"], "metadata": {"id": "aKYv_iZ7GR2c"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# RECOVERY CELL - Add this as Cell 8 or run separately\n", "# =====================================================\n", "# Run this cell to complete analysis of failed chunks\n", "\n", "import time\n", "import openai\n", "from datetime import datetime, timedelta\n", "from google.colab import files\n", "\n", "def recover_failed_analysis():\n", "    \"\"\"\n", "    Attempt to complete the analysis using the existing transcript and partial results\n", "    \"\"\"\n", "    print(\"🔧 ATTEMPTING TO RECOVER AND COMPLETE ANALYSIS...\")\n", "\n", "    # Check what we have in memory\n", "    if 'enhanced_transcript' not in globals():\n", "        print(\"❌ No transcript found in memory. Please re-run the pipeline.\")\n", "        return\n", "\n", "    print(\"✅ Found transcript in memory\")\n", "\n", "    # Try to complete a simplified analysis\n", "    try:\n", "        # Create a condensed summary of the transcript\n", "        print(\"\\n📝 Creating condensed analysis...\")\n", "\n", "        # Extract key sections with legal significance\n", "        key_sections = []\n", "        legal_keywords = ['miranda', 'rights', 'force', 'weapon', 'cuff', 'handcuff',\n", "                         'towel', 'naked', 'arrest', 'resist', 'comply', 'baker act',\n", "                         'mental health', 'privacy', 'dignity', 'camera', 'mute']\n", "\n", "        for i, word_data in enumerate(enhanced_transcript):\n", "            word_text = word_data['word'].lower()\n", "            if any(keyword in word_text for keyword in legal_keywords):\n", "                # Get context\n", "                start_idx = max(0, i - 10)\n", "                end_idx = min(len(enhanced_transcript), i + 10)\n", "\n", "                context_words = []\n", "                for j in range(start_idx, end_idx):\n", "                    context_words.append(enhanced_transcript[j]['word'])\n", "\n", "                timestamp = word_data['start'] + (skip_seconds if 'skip_seconds' in globals() else 30)\n", "                timestamp_str = str(timedelta(seconds=int(timestamp)))\n", "\n", "                key_sections.append({\n", "                    'timestamp': timestamp_str,\n", "                    'keyword': word_text,\n", "                    'context': ' '.join(context_words)\n", "                })\n", "\n", "        print(f\"✅ Identified {len(key_sections)} key sections\")\n", "\n", "        # Create simplified analysis document\n", "        output_path = \"/content/SIMPLIFIED_LEGAL_ANALYSIS.txt\"\n", "\n", "        with open(output_path, \"w\", encoding=\"utf-8\") as f:\n", "            f.write(\"SIMPLIFIED LEGAL ANALYSIS - RATE LIMIT RECOVERY\\n\")\n", "            f.write(\"=\"*60 + \"\\n\\n\")\n", "            f.write(f\"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\\n\")\n", "\n", "            f.write(\"KEY LEGAL SECTIONS IDENTIFIED:\\n\")\n", "            f.write(\"-\"*40 + \"\\n\\n\")\n", "\n", "            for section in key_sections[:50]:  # First 50 key sections\n", "                f.write(f\"[{section['timestamp']}] KEYWORD: {section['keyword']}\\n\")\n", "                f.write(f\"CONTEXT: {section['context']}\\n\\n\")\n", "\n", "            # Add violation summary if available\n", "            if 'all_violations' in globals():\n", "                f.write(\"\\n\" + \"=\"*60 + \"\\n\")\n", "                f.write(\"VIOLATION SUMMARY FROM INITIAL ANALYSIS:\\n\")\n", "                f.write(\"-\"*40 + \"\\n\\n\")\n", "\n", "                for vtype, violations in all_violations.items():\n", "                    if violations:\n", "                        f.write(f\"\\n{vtype.upper()}: {len(violations)} incidents\\n\")\n", "                        for v in violations[:3]:  # First 3 examples\n", "                            if 'timestamp' in v:\n", "                                ts = str(timedelta(seconds=int(v['timestamp'])))\n", "                                f.write(f\"  - [{ts}] {v.get('violation_type', 'Violation')}\\n\")\n", "\n", "            f.write(\"\\n\" + \"=\"*60 + \"\\n\")\n", "            f.write(\"ANALYSIS NOTES:\\n\")\n", "            f.write(\"- This is a simplified analysis due to API rate limits\\n\")\n", "            f.write(\"- Full GPT-4 analysis was partially completed\\n\")\n", "            f.write(\"- Key legal sections have been extracted for review\\n\")\n", "            f.write(\"- Consider manual review of these sections\\n\")\n", "\n", "        print(\"📥 Downloading simplified analysis...\")\n", "        files.download(output_path)\n", "        print(\"✅ Simplified analysis complete!\")\n", "\n", "        # Try one more GPT-3.5 summary\n", "        print(\"\\n🤖 Attempting GPT-3.5 summary...\")\n", "        try:\n", "            summary_text = f\"Transcript has {len(key_sections)} legally significant sections. \"\n", "            summary_text += f\"Key concerns include: {', '.join(set(s['keyword'] for s in key_sections[:20]))}\"\n", "\n", "            response = openai.ChatCompletion.create(\n", "                model=\"gpt-3.5-turbo\",\n", "                messages=[\n", "                    {\n", "                        \"role\": \"system\",\n", "                        \"content\": \"You are a legal analyst. Based on these keywords from a police encounter, identify the main legal concerns.\"\n", "                    },\n", "                    {\n", "                        \"role\": \"user\",\n", "                        \"content\": summary_text\n", "                    }\n", "                ],\n", "                max_tokens=500,\n", "                temperature=0.1\n", "            )\n", "\n", "            with open(\"/content/GPT35_SUMMARY.txt\", \"w\") as f:\n", "                f.write(\"GPT-3.5 LEGAL SUMMARY\\n\")\n", "                f.write(\"=\"*30 + \"\\n\\n\")\n", "                f.write(response.choices[0].message.content)\n", "\n", "            files.download(\"/content/GPT35_SUMMARY.txt\")\n", "            print(\"✅ GPT-3.5 summary complete!\")\n", "\n", "        except Exception as e:\n", "            print(f\"⚠️ GPT-3.5 summary also failed: {e}\")\n", "\n", "    except Exception as e:\n", "        print(f\"❌ Recovery failed: {e}\")\n", "\n", "\n", "# Option 2: Manual chunk processing with user control\n", "def process_single_chunk_manually(chunk_number):\n", "    \"\"\"\n", "    Process a single chunk manually when ready\n", "    \"\"\"\n", "    if 'transcript_chunks' not in globals():\n", "        print(\"❌ No chunks found. Please prepare chunks first.\")\n", "        return\n", "\n", "    if chunk_number >= len(transcript_chunks):\n", "        print(f\"❌ Invalid chunk number. Total chunks: {len(transcript_chunks)}\")\n", "        return\n", "\n", "    print(f\"Processing chunk {chunk_number + 1}...\")\n", "\n", "    try:\n", "        response = openai.ChatCompletion.create(\n", "            model=\"gpt-4\",\n", "            messages=[\n", "                {\n", "                    \"role\": \"system\",\n", "                    \"content\": \"Analyze this police transcript section for legal violations.\"\n", "                },\n", "                {\n", "                    \"role\": \"user\",\n", "                    \"content\": transcript_chunks[chunk_number][:4000]\n", "                }\n", "            ],\n", "            max_tokens=1000,\n", "            temperature=0.1\n", "        )\n", "\n", "        print(\"✅ Analysis complete:\")\n", "        print(response.choices[0].message.content)\n", "\n", "    except Exception as e:\n", "        print(f\"❌ Failed: {e}\")\n", "\n", "\n", "# Option 3: Export for external analysis\n", "def export_for_external_analysis():\n", "    \"\"\"\n", "    Export transcript chunks for analysis outside of Colab\n", "    \"\"\"\n", "    if 'transcript_chunks' not in globals():\n", "        print(\"❌ No chunks found\")\n", "        return\n", "\n", "    output_path = \"/content/TRANSCRIPT_CHUNKS_FOR_ANALYSIS.txt\"\n", "\n", "    with open(output_path, \"w\", encoding=\"utf-8\") as f:\n", "        f.write(\"TRANSCRIPT CHUNKS FOR EXTERNAL ANALYSIS\\n\")\n", "        f.write(\"=\"*50 + \"\\n\\n\")\n", "\n", "        for i, chunk in enumerate(transcript_chunks):\n", "            f.write(f\"\\n{'='*50}\\n\")\n", "            f.write(f\"CHUNK {i+1} of {len(transcript_chunks)}\\n\")\n", "            f.write(f\"{'='*50}\\n\\n\")\n", "            f.write(chunk)\n", "            f.write(\"\\n\\n\")\n", "\n", "    files.download(output_path)\n", "    print(f\"✅ Exported {len(transcript_chunks)} chunks for external analysis\")\n", "\n", "\n", "# MAIN RECOVERY FUNCTION\n", "print(\"🚑 RECOVERY OPTIONS AVAILABLE:\")\n", "print(\"1. recover_failed_analysis() - Create simplified analysis from transcript\")\n", "print(\"2. process_single_chunk_manually(chunk_num) - Process one chunk at a time\")\n", "print(\"3. export_for_external_analysis() - Export chunks for external processing\")\n", "\n", "# Run the main recovery\n", "recover_failed_analysis()"], "metadata": {"id": "wLHWEm_S411e"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# SOLUTION FOR PROCESSING REMAINING 17 CHUNKS\n", "# ===========================================\n", "# With 17 failed chunks, we need a strategic approach\n", "\n", "import time\n", "import openai\n", "from datetime import datetime, timedelta\n", "from google.colab import files\n", "\n", "def process_remaining_chunks_strategically():\n", "    \"\"\"\n", "    Process the remaining 17 chunks (7-23) with multiple strategies\n", "    \"\"\"\n", "\n", "    print(\"📊 ANALYSIS STATUS:\")\n", "    print(\"- Total chunks: 23\")\n", "    print(\"- Completed: 6 (chunks 1-6)\")\n", "    print(\"- Failed: 17 (chunks 7-23)\")\n", "    print(\"- Completion: 26%\\n\")\n", "\n", "    # OPTION 1: Batch Processing with Extended Delays\n", "    print(\"🔄 OPTION 1: Process in batches with long delays\")\n", "    print(\"This will take about 45-60 minutes but should complete all chunks\\n\")\n", "\n", "    def process_in_batches():\n", "        # Process in batches of 3 chunks with 5-minute breaks\n", "        remaining_chunks = list(range(6, 23))  # chunks 7-23 (0-indexed)\n", "        batch_size = 3\n", "\n", "        for batch_start in range(0, len(remaining_chunks), batch_size):\n", "            batch_end = min(batch_start + batch_size, len(remaining_chunks))\n", "            batch = remaining_chunks[batch_start:batch_end]\n", "\n", "            print(f\"\\n📦 Processing batch: chunks {[c+1 for c in batch]}\")\n", "\n", "            for chunk_idx in batch:\n", "                # Process each chunk\n", "                try:\n", "                    # Your chunk processing code here\n", "                    print(f\"✅ Chunk {chunk_idx + 1} processed\")\n", "                    time.sleep(15)  # 15 seconds between chunks\n", "                except Exception as e:\n", "                    print(f\"❌ Chunk {chunk_idx + 1} failed: {e}\")\n", "\n", "            if batch_end < len(remaining_chunks):\n", "                print(f\"\\n⏰ Waiting 5 minutes before next batch...\")\n", "                time.sleep(300)  # 5 minutes between batches\n", "\n", "    # OPTION 2: Hybrid GPT-4/GPT-3.5 Approach\n", "    print(\"\\n🤖 OPTION 2: Use GPT-4 for critical chunks, GPT-3.5 for others\")\n", "\n", "    def hybrid_analysis():\n", "        critical_chunks = [6, 7, 8, 15, 16, 22]  # Chunks likely to contain key events\n", "\n", "        # Use GPT-4 for critical chunks (with delays)\n", "        for chunk_idx in critical_chunks:\n", "            if chunk_idx < 23:\n", "                print(f\"🔍 GPT-4 analysis for critical chunk {chunk_idx + 1}\")\n", "                # Process with GPT-4\n", "                time.sleep(30)  # Longer delay for GPT-4\n", "\n", "        # Use GPT-3.5 for remaining chunks (no rate limit)\n", "        for chunk_idx in range(6, 23):\n", "            if chunk_idx not in critical_chunks:\n", "                print(f\"💡 GPT-3.5 analysis for chunk {chunk_idx + 1}\")\n", "                # Process with GPT-3.5\n", "                time.sleep(2)  # Short delay\n", "\n", "    # OPTION 3: Export for External Processing\n", "    print(\"\\n📤 OPTION 3: Export remaining chunks for external analysis\")\n", "\n", "    def export_remaining_chunks():\n", "        output_path = \"/content/REMAINING_17_CHUNKS.txt\"\n", "\n", "        with open(output_path, \"w\", encoding=\"utf-8\") as f:\n", "            f.write(\"REMAINING 17 CHUNKS FOR EXTERNAL ANALYSIS\\n\")\n", "            f.write(\"=\"*60 + \"\\n\\n\")\n", "            f.write(\"Instructions:\\n\")\n", "            f.write(\"1. Copy each chunk to ChatGPT or Claude\\n\")\n", "            f.write(\"2. Ask for legal violation analysis\\n\")\n", "            f.write(\"3. Compile results\\n\\n\")\n", "\n", "            if 'transcript_chunks' in globals():\n", "                for i in range(6, min(23, len(transcript_chunks))):\n", "                    f.write(f\"\\n{'='*60}\\n\")\n", "                    f.write(f\"CHUNK {i+1} of 23\\n\")\n", "                    f.write(f\"{'='*60}\\n\\n\")\n", "                    f.write(transcript_chunks[i])\n", "                    f.write(\"\\n\\n\")\n", "\n", "        files.download(output_path)\n", "        print(f\"✅ Exported chunks 7-23 for external analysis\")\n", "\n", "    return {\n", "        'batch_process': process_in_batches,\n", "        'hybrid': hybrid_analysis,\n", "        'export': export_remaining_chunks\n", "    }\n", "\n", "\n", "# IMMEDIATE ACTION: Create Summary from Available Data\n", "def create_executive_summary_from_partial_results():\n", "    \"\"\"\n", "    Create a meaningful summary from the 26% we successfully analyzed\n", "    \"\"\"\n", "    print(\"\\n📝 Creating Executive Summary from Partial Results...\")\n", "\n", "    summary_path = \"/content/EXECUTIVE_SUMMARY_PARTIAL.txt\"\n", "\n", "    with open(summary_path, \"w\", encoding=\"utf-8\") as f:\n", "        f.write(\"EXECUTIVE SUMMARY - PARTIAL ANALYSIS (26% Complete)\\n\")\n", "        f.write(\"=\"*60 + \"\\n\\n\")\n", "        f.write(f\"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\")\n", "        f.write(\"Analysis Coverage: Chunks 1-6 of 23 (approximately first 15 minutes)\\n\\n\")\n", "\n", "        f.write(\"KEY FINDINGS FROM ANALYZED PORTION:\\n\")\n", "        f.write(\"-\"*40 + \"\\n\")\n", "\n", "        # Summary based on what we know was found\n", "        if 'all_violations' in globals():\n", "            for vtype, violations in all_violations.items():\n", "                if violations:\n", "                    f.write(f\"\\n{vtype.upper()}: {len(violations)} incidents found\\n\")\n", "                    # List first few\n", "                    for v in violations[:3]:\n", "                        if 'timestamp' in v:\n", "                            f.write(f\"  - {v.get('violation_type', 'Violation')}\\n\")\n", "\n", "        f.write(\"\\n\\nNOTE: This represents analysis of approximately the first 15 minutes.\\n\")\n", "        f.write(\"The remaining 44 minutes require additional processing.\\n\")\n", "        f.write(\"\\nRECOMMENDATIONS:\\n\")\n", "        f.write(\"1. Review the complete transcript (already downloaded)\\n\")\n", "        f.write(\"2. Focus on timestamps with legal keywords\\n\")\n", "        f.write(\"3. Use external tools for remaining analysis\\n\")\n", "\n", "    files.download(summary_path)\n", "    print(\"✅ Executive summary created from partial results\")\n", "\n", "\n", "# BEST STRATEGY FOR YOUR SITUATION\n", "def recommended_approach():\n", "    \"\"\"\n", "    Recommended approach for 17 remaining chunks\n", "    \"\"\"\n", "    print(\"\\n🎯 RECOMMENDED APPROACH:\")\n", "    print(\"=\"*50)\n", "    print(\"\\n1. IMMEDIATE: Download partial results (26% complete)\")\n", "    print(\"   - You have the FULL transcript already\")\n", "    print(\"   - You have 6/23 chunks of legal analysis\")\n", "    print(\"   - You have all frame analyses\\n\")\n", "\n", "    print(\"2. SHORT TERM (Next 10 minutes):\")\n", "    print(\"   - Wait 5 minutes for rate limit reset\")\n", "    print(\"   - Process 3-4 more critical chunks with GPT-4\")\n", "    print(\"   - Use GPT-3.5 for quick summaries of remaining chunks\\n\")\n", "\n", "    print(\"3. ALTERNATIVES:\")\n", "    print(\"   - Export chunks 7-23 and analyze externally\")\n", "    print(\"   - Process remaining chunks over several sessions\")\n", "    print(\"   - Use the transcript + frame analysis for manual review\\n\")\n", "\n", "    # Create options object\n", "    options = process_remaining_chunks_strategically()\n", "\n", "    print(\"\\n📋 AVAILABLE FUNCTIONS:\")\n", "    print(\"- create_executive_summary_from_partial_results()\")\n", "    print(\"- options['export']() - Export remaining chunks\")\n", "    print(\"- options['batch_process']() - Process in batches (45-60 min)\")\n", "    print(\"- options['hybrid']() - Use GPT-4/GPT-3.5 hybrid approach\")\n", "\n", "    return options\n", "\n", "\n", "# RUN IMMEDIATE ACTIONS\n", "print(\"🚀 EXECUTING IMMEDIATE RECOVERY ACTIONS...\\n\")\n", "\n", "# 1. Create executive summary\n", "create_executive_summary_from_partial_results()\n", "\n", "# 2. Show recommendations\n", "options = recommended_approach()\n", "\n", "print(\"\\n✅ Recovery options ready. Choose your approach above.\")"], "metadata": {"id": "YDSEqGj34-vt"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["  # Retry failed chunks after waiting\n", "  import time\n", "  import openai\n", "\n", "  print(\"⏳ Waiting 2 minutes for rate limit to reset...\")\n", "  time.sleep(120)\n", "\n", "  # Complete the analysis with remaining chunks\n", "  # (Use the recovery code from RECOVERY_CELL.py)"], "metadata": {"id": "QX8XVWTD3apP"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["  # 1. Run this cell to export all remaining chunks:\n", "\n", "  # Copy and run this entire code block\n", "  exec(open('/mnt/c/Users/<USER>/Desktop/Carolina/Legal_Transcription_Pipeline/Pipeline_Files/OPTION_C_EXTERNAL_AN\n", "  ALYSIS_GUIDE.py').read())\n", "\n", "  # 2. What you'll get:\n", "\n", "  # - CHUNKS_7-23_FOR_EXTERNAL_ANALYSIS.txt - Complete guide with prompts\n", "  # - CHUNKS_SIMPLE.txt - Just the transcript chunks\n", "  # - EXTERNAL_ANALYSIS_CHECKLIST.txt - Quick reference"], "metadata": {"id": "j63xguOH8LZD"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# OPTION C: <PERSON><PERSON><PERSON><PERSON> GUIDE FOR <PERSON>XTER<PERSON>L ANALYSIS\n", "# ==============================================\n", "\n", "import os\n", "from datetime import datetime\n", "from google.colab import files\n", "\n", "def export_chunks_for_external_analysis():\n", "    \"\"\"\n", "    Export remaining 17 chunks with instructions and analysis templates\n", "    \"\"\"\n", "    print(\"📤 PREPARING CHUNKS FOR EXTERNAL ANALYSIS...\\n\")\n", "\n", "    # Check if transcript chunks exist\n", "    if 'transcript_chunks' not in globals():\n", "        print(\"❌ No transcript chunks found in memory!\")\n", "        print(\"Attempting to recreate chunks from enhanced_transcript...\")\n", "\n", "        if 'enhanced_transcript' not in globals():\n", "            print(\"❌ No transcript data available. Cannot proceed.\")\n", "            return\n", "\n", "        # Recreate chunks\n", "        global transcript_chunks\n", "        transcript_chunks = []\n", "        current_chunk = []\n", "        current_size = 0\n", "        max_size = 6000\n", "\n", "        for word_data in enhanced_transcript:\n", "            word_timestamp = word_data['start'] + 30  # assuming skip_seconds=30\n", "            word_text = word_data['word']\n", "            speakers = word_data.get('speakers', [])\n", "            primary_speaker = speakers[0] if speakers else \"UNKNOWN\"\n", "\n", "            from datetime import timedelta\n", "            timestamp_str = str(timedelta(seconds=int(word_timestamp)))\n", "            line = f\"[{timestamp_str}] {primary_speaker}: {word_text} \"\n", "            line_size = len(line)\n", "\n", "            if current_size + line_size > max_size and current_chunk:\n", "                transcript_chunks.append(''.join(current_chunk))\n", "                current_chunk = [line]\n", "                current_size = line_size\n", "            else:\n", "                current_chunk.append(line)\n", "                current_size += line_size\n", "\n", "        if current_chunk:\n", "            transcript_chunks.append(''.join(current_chunk))\n", "\n", "        print(f\"✅ Recreated {len(transcript_chunks)} chunks\")\n", "\n", "    # Create comprehensive export file\n", "    output_path = \"/content/CHUNKS_7-23_FOR_EXTERNAL_ANALYSIS.txt\"\n", "\n", "    with open(output_path, \"w\", encoding=\"utf-8\") as f:\n", "        # Header with instructions\n", "        f.write(\"TRANSCRIPT CHUNKS 7-23 FOR EXTERNAL LEGAL ANALYSIS\\n\")\n", "        f.write(\"=\"*70 + \"\\n\\n\")\n", "        f.write(f\"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\")\n", "        f.write(f\"Total chunks to analyze: {min(17, len(transcript_chunks)-6)}\\n\")\n", "        f.write(f\"Video duration: ~59 minutes\\n\")\n", "        f.write(f\"Coverage: <PERSON><PERSON> 7-23 cover approximately minutes 15-59\\n\\n\")\n", "\n", "        f.write(\"BACKGROUND CONTEXT:\\n\")\n", "        f.write(\"-\"*50 + \"\\n\")\n", "        f.write(\"- Police body camera footage from mental health response\\n\")\n", "        f.write(\"- Subject reportedly in minimal clothing (towel) when detained\\n\")\n", "        f.write(\"- Baker Act (involuntary mental health) situation\\n\")\n", "        f.write(\"- Multiple officers present\\n\")\n", "        f.write(\"- Analysis needed for constitutional violations, dignity concerns\\n\\n\")\n", "\n", "        f.write(\"INSTRUCTIONS FOR EXTERNAL ANALYSIS:\\n\")\n", "        f.write(\"-\"*50 + \"\\n\")\n", "        f.write(\"1. Copy each chunk below into ChatGPT, Claude, or similar AI\\n\")\n", "        f.write(\"2. Use the provided analysis prompt for each chunk\\n\")\n", "        f.write(\"3. Save each chunk's analysis with its chunk number\\n\")\n", "        f.write(\"4. Compile all analyses into final document\\n\\n\")\n", "\n", "        f.write(\"RECOMMENDED ANALYSIS PROMPT FOR EACH CHUNK:\\n\")\n", "        f.write(\"-\"*50 + \"\\n\")\n", "        f.write('\"\"\"\\n')\n", "        f.write(\"You are a forensic legal analyst reviewing police body camera transcript.\\n\")\n", "        f.write(\"Analyze this transcript section for:\\n\\n\")\n", "        f.write(\"1. CONSTITUTIONAL VIOLATIONS:\\n\")\n", "        f.write(\"   - 4th Amendment (unreasonable search/seizure)\\n\")\n", "        f.write(\"   - 5th Amendment (Miranda, self-incrimination)\\n\")\n", "        f.write(\"   - 8th Amendment (cruel treatment, dignity)\\n\")\n", "        f.write(\"   - 14th Amendment (due process)\\n\\n\")\n", "        f.write(\"2. SPECIFIC CONCERNS:\\n\")\n", "        f.write(\"   - Handcuffing person in towel/minimal clothing\\n\")\n", "        f.write(\"   - Public exposure and dignity violations\\n\")\n", "        f.write(\"   - Mental health crisis handling\\n\")\n", "        f.write(\"   - Use of force on cooperative subject\\n\")\n", "        f.write(\"   - Baker Act procedural compliance\\n\\n\")\n", "        f.write(\"3. IDENTIFY:\\n\")\n", "        f.write(\"   - Exact quotes showing violations\\n\")\n", "        f.write(\"   - Timestamps of concerning events\\n\")\n", "        f.write(\"   - Officer statements showing intent\\n\")\n", "        f.write(\"   - Evidence of retaliation or punishment\\n\\n\")\n", "        f.write(\"Provide specific timestamps and quotes for any violations found.\\n\")\n", "        f.write('\"\"\"\\n\\n')\n", "\n", "        # Export each remaining chunk\n", "        start_chunk = 6  # Start from chunk 7 (0-indexed)\n", "        end_chunk = min(23, len(transcript_chunks))\n", "\n", "        for i in range(start_chunk, end_chunk):\n", "            f.write(f\"\\n{'='*70}\\n\")\n", "            f.write(f\"CHUNK {i+1} of 23\\n\")\n", "            f.write(f\"Approximate time coverage: {15 + (i-6)*2} - {17 + (i-6)*2} minutes\\n\")\n", "            f.write(f\"{'='*70}\\n\\n\")\n", "\n", "            # Add chunk content\n", "            if i < len(transcript_chunks):\n", "                f.write(transcript_chunks[i])\n", "            else:\n", "                f.write(\"[Chunk data not available]\")\n", "\n", "            f.write(\"\\n\\n--- END OF CHUNK ---\\n\\n\")\n", "\n", "        # Add compilation template\n", "        f.write(\"\\n\" + \"=\"*70 + \"\\n\")\n", "        f.write(\"ANALYSIS COMPILATION TEMPLATE:\\n\")\n", "        f.write(\"=\"*70 + \"\\n\\n\")\n", "        f.write(\"After analyzing all chunks, compile findings as follows:\\n\\n\")\n", "        f.write(\"COMPREHENSIVE LEGAL ANALYSIS - CHUNKS 7-23\\n\")\n", "        f.write(\"-\"*40 + \"\\n\\n\")\n", "        f.write(\"1. CONSTITUTIONAL VIOLATIONS FOUND:\\n\")\n", "        f.write(\"   - 4th Amendment: [List violations with timestamps]\\n\")\n", "        f.write(\"   - 5th Amendment: [List violations with timestamps]\\n\")\n", "        f.write(\"   - 8th Amendment: [List violations with timestamps]\\n\")\n", "        f.write(\"   - 14th Amendment: [List violations with timestamps]\\n\\n\")\n", "        f.write(\"2. DIGNITY AND PRIVACY VIOLATIONS:\\n\")\n", "        f.write(\"   - [List all instances with timestamps]\\n\\n\")\n", "        f.write(\"3. PROCEDURAL VIOLATIONS:\\n\")\n", "        f.write(\"   - [List Baker Act and policy violations]\\n\\n\")\n", "        f.write(\"4. USE OF FORCE CONCERNS:\\n\")\n", "        f.write(\"   - [List all force applications with justification analysis]\\n\\n\")\n", "        f.write(\"5. KEY QUOTES AND EVIDENCE:\\n\")\n", "        f.write(\"   - [List most damaging quotes with speakers and timestamps]\\n\\n\")\n", "        f.write(\"6. PATTERN ANALYSIS:\\n\")\n", "        f.write(\"   - [Identify patterns of misconduct across chunks]\\n\\n\")\n", "\n", "    print(f\"✅ Export file created with {end_chunk - start_chunk} chunks\")\n", "    print(\"📥 Downloading export file...\")\n", "    files.download(output_path)\n", "\n", "    # Also create a simplified version for easier copying\n", "    simple_path = \"/content/CHUNKS_SIMPLE.txt\"\n", "    with open(simple_path, \"w\", encoding=\"utf-8\") as f:\n", "        f.write(\"SIMPL<PERSON>IED CHUNKS FOR QUICK COPYING\\n\")\n", "        f.write(\"=\"*50 + \"\\n\\n\")\n", "\n", "        for i in range(start_chunk, min(end_chunk, len(transcript_chunks))):\n", "            f.write(f\"\\n--- CHUNK {i+1} ---\\n\\n\")\n", "            f.write(transcript_chunks[i])\n", "            f.write(\"\\n\\n\")\n", "\n", "    files.download(simple_path)\n", "\n", "    print(\"\\n✅ EXPORT COMPLETE!\")\n", "    print(\"\\n📋 YOU NOW HAVE:\")\n", "    print(\"1. CHUNKS_7-23_FOR_EXTERNAL_ANALYSIS.txt - Full guide with prompts\")\n", "    print(\"2. CHUNKS_SIMPLE.txt - Just the chunks for easy copying\")\n", "    print(\"\\n🔍 NEXT STEPS:\")\n", "    print(\"1. Open the export file\")\n", "    print(\"2. Copy each chunk to your preferred AI tool\")\n", "    print(\"3. Use the provided analysis prompt\")\n", "    print(\"4. Save each analysis\")\n", "    print(\"5. Co<PERSON>ile using the template at the end\")\n", "\n", "    return True\n", "\n", "\n", "def create_quick_reference_guide():\n", "    \"\"\"\n", "    Create a quick reference for what to look for in external analysis\n", "    \"\"\"\n", "    guide_path = \"/content/EXTERNAL_ANALYSIS_CHECKLIST.txt\"\n", "\n", "    with open(guide_path, \"w\", encoding=\"utf-8\") as f:\n", "        f.write(\"QUICK REFERENCE CHECKLIST FOR EXTERNAL ANALYSIS\\n\")\n", "        f.write(\"=\"*50 + \"\\n\\n\")\n", "\n", "        f.write(\"☐ PRIORITY RED FLAGS TO IDENTIFY:\\n\")\n", "        f.write(\"  ☐ Subject says 'towel' or 'naked' or 'cover'\\n\")\n", "        f.write(\"  ☐ Officers discuss 'cuffing' person in towel\\n\")\n", "        f.write(\"  ☐ References to 'shower' or 'bathroom'\\n\")\n", "        f.write(\"  ☐ Public exposure mentions\\n\")\n", "        f.write(\"  ☐ Crowd/neighbor presence during minimal clothing\\n\\n\")\n", "\n", "        f.write(\"☐ CONSTITUTIONAL MARKERS:\\n\")\n", "        f.write(\"  ☐ 'Miranda' or 'rights' not given\\n\")\n", "        f.write(\"  ☐ 'Search' without consent/warrant\\n\")\n", "        f.write(\"  ☐ Force used on cooperative subject\\n\")\n", "        f.write(\"  ☐ Dignity violations during detention\\n\\n\")\n", "\n", "        f.write(\"☐ BAKER ACT VIOLATIONS:\\n\")\n", "        f.write(\"  ☐ No immediate danger established\\n\")\n", "        f.write(\"  ☐ No attempt at voluntary compliance\\n\")\n", "        f.write(\"  ☐ Improper transportation methods\\n\")\n", "        f.write(\"  ☐ Excessive restraints for mental health\\n\\n\")\n", "\n", "        f.write(\"☐ CONCERNING OFFICER STATEMENTS:\\n\")\n", "        f.write(\"  ☐ Threats or intimidation\\n\")\n", "        f.write(\"  ☐ Retaliation mentions\\n\")\n", "        f.write(\"  ☐ Cover-up discussions\\n\")\n", "        f.write(\"  ☐ Camera muting references\\n\\n\")\n", "\n", "        f.write(\"☐ ESCALATION INDICATORS:\\n\")\n", "        f.write(\"  ☐ SWAT or tactical mentions\\n\")\n", "        f.write(\"  ☐ Weapon displays to cooperative subject\\n\")\n", "        f.write(\"  ☐ Multiple officers for one person\\n\")\n", "        f.write(\"  ☐ Failure to de-escalate\\n\\n\")\n", "\n", "    files.download(guide_path)\n", "    print(\"✅ Quick reference checklist downloaded!\")\n", "\n", "\n", "# MAIN EXECUTION\n", "print(\"🚀 STARTING OPTION C: EXTERNAL ANALYSIS EXPORT\\n\")\n", "\n", "# Run the export\n", "success = export_chunks_for_external_analysis()\n", "\n", "if success:\n", "    print(\"\\n📋 Creating quick reference checklist...\")\n", "    create_quick_reference_guide()\n", "\n", "    print(\"\\n\" + \"=\"*70)\n", "    print(\"✅ OPTION C EXPORT COMPLETE!\")\n", "    print(\"=\"*70)\n", "    print(\"\\nYou now have everything needed for external analysis:\")\n", "    print(\"- Full chunks with analysis prompts\")\n", "    print(\"- Simplified chunks for easy copying\")\n", "    print(\"- Quick reference checklist\")\n", "    print(\"\\nThe external analysis will likely produce BETTER results because:\")\n", "    print(\"- No rate limits\")\n", "    print(\"- More interactive analysis\")\n", "    print(\"- Ability to ask follow-up questions\")\n", "    print(\"- Can use multiple AI tools for comparison\")"], "metadata": {"id": "4YJjJrMm9L6O"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["export_chunks_for_external_analysis()"], "metadata": {"id": "aGUBbvi99wam"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["  # Check what transcript data exists\n", "  import os\n", "\n", "  print(\"Checking for transcript data...\")\n", "\n", "  # Check for saved files\n", "  files_to_check = [\n", "      \"/content/EARLY_TRANSCRIPT_ONLY.txt\",\n", "      \"/content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt\",\n", "      \"/content/enhanced_transcript.pkl\",\n", "      \"/content/whisper_result.json\"\n", "  ]\n", "\n", "  for f in files_to_check:\n", "      if os.path.exists(f):\n", "          size = os.path.getsize(f) / 1024\n", "          print(f\"✅ Found: {f} ({size:.1f} KB)\")\n", "      else:\n", "          print(f\"❌ Not found: {f}\")\n", "\n", "  # Check variables in memory\n", "  print(\"\\nVariables in memory:\")\n", "  for var in ['enhanced_transcript', 'whisper_result', 'transcript_chunks', 'all_violations']:\n", "      if var in globals():\n", "          print(f\"✅ {var} exists\")\n", "      else:\n", "          print(f\"❌ {var} NOT in memory\")"], "metadata": {"id": "ivgPSphb_X-h"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["  # Read the transcript and create chunks for export\n", "  from datetime import datetime, timedelta\n", "  from google.colab import files\n", "\n", "  print(\"📄 Reading transcript from saved file...\")\n", "\n", "  # Read the early transcript\n", "  with open(\"/content/EARLY_TRANSCRIPT_ONLY.txt\", \"r\", encoding=\"utf-8\") as f:\n", "      transcript_content = f.read()\n", "\n", "  # Extract just the transcript portion (skip header)\n", "  lines = transcript_content.split('\\n')\n", "  transcript_start = False\n", "  transcript_text = []\n", "\n", "  for line in lines:\n", "      if \"FULL TRANSCRIPT WITH SPEAKER IDENTIFICATION:\" in line:\n", "          transcript_start = True\n", "          continue\n", "      if transcript_start and \"[END OF TRANSCRIPT]\" not in line:\n", "          if line.strip():  # Skip empty lines\n", "              transcript_text.append(line)\n", "\n", "  # Join all transcript lines\n", "  full_transcript = '\\n'.join(transcript_text)\n", "\n", "  # Create chunks\n", "  print(\"\\n✂️ Creating chunks...\")\n", "  chunks = []\n", "  current_chunk = []\n", "  current_size = 0\n", "  max_chunk_size = 5000  # Characters per chunk\n", "\n", "  lines = full_transcript.split('\\n')\n", "  for line in lines:\n", "      line_size = len(line)\n", "      if current_size + line_size > max_chunk_size and current_chunk:\n", "          chunks.append('\\n'.join(current_chunk))\n", "          current_chunk = [line]\n", "          current_size = line_size\n", "      else:\n", "          current_chunk.append(line)\n", "          current_size += line_size\n", "\n", "  if current_chunk:\n", "      chunks.append('\\n'.join(current_chunk))\n", "\n", "  print(f\"✅ Created {len(chunks)} chunks from transcript\")\n", "\n", "  # Export chunks 7-23 (or however many we have)\n", "  output_path = \"/content/CHUNKS_FOR_EXTERNAL_ANALYSIS.txt\"\n", "\n", "  with open(output_path, \"w\", encoding=\"utf-8\") as f:\n", "      f.write(\"TRANSCRIPT CHUNKS FOR EXTERNAL LEGAL ANALYSIS\\n\")\n", "      f.write(\"=\"*70 + \"\\n\\n\")\n", "      f.write(f\"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\")\n", "      f.write(f\"Total chunks: {len(chunks)}\\n\")\n", "      f.write(f\"Chunks 7-23 for analysis\\n\\n\")\n", "\n", "      f.write(\"ANALYSIS PROMPT FOR EACH CHUNK:\\n\")\n", "      f.write(\"-\"*50 + \"\\n\")\n", "      f.write(\"\"\"\n", "  Analyze this police body camera transcript section for:\n", "\n", "  1. Constitutional violations (4th, 5th, 8th, 14th Amendment)\n", "  2. Privacy/dignity violations (especially regarding minimal clothing/towel)\n", "  3. Mental health crisis handling violations\n", "  4. Use of force on cooperative subjects\n", "  5. Baker Act procedural violations\n", "\n", "  Identify specific quotes and timestamps for any violations found.\n", "  Focus especially on:\n", "  - Handcuffing person in towel/minimal clothing\n", "  - Public exposure and humiliation\n", "  - Excessive force or restraints\n", "  - Failure to accommodate basic dignity needs\n", "  \"\"\")\n", "\n", "      # Export chunks 7 onwards\n", "      start_chunk = 6  # Start from chunk 7 (0-indexed)\n", "      for i in range(start_chunk, len(chunks)):\n", "          f.write(f\"\\n\\n{'='*70}\\n\")\n", "          f.write(f\"CHUNK {i+1} of {len(chunks)}\\n\")\n", "          f.write(f\"{'='*70}\\n\\n\")\n", "          f.write(chunks[i])\n", "\n", "  print(f\"\\n📥 Downloading chunks for external analysis...\")\n", "  files.download(output_path)\n", "\n", "  # Also check what's in the comprehensive analysis file\n", "  print(\"\\n📋 Checking comprehensive analysis file...\")\n", "  with open(\"/content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt\", \"r\") as f:\n", "      analysis_content = f.read()\n", "      if \"Analysis failed for chunk\" in analysis_content:\n", "          failed_count = analysis_content.count(\"Analysis failed for chunk\")\n", "          print(f\"⚠️ Found {failed_count} failed chunk analyses in the file\")\n", "      if \"Section\" in analysis_content and \"Analysis\" in analysis_content:\n", "          successful_sections = analysis_content.count(\"Section\") - failed_count\n", "          print(f\"✅ Found {successful_sections} successful chunk analyses in the file\")\n", "\n", "  print(\"\\n✅ Export complete! You can now analyze the remaining chunks externally.\")"], "metadata": {"id": "uiAJ3doe_-IA"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# UNIVERSAL CHUNK EXPORT TOOL FOR ANY VIDEO\n", "# =========================================\n", "# This tool will work with any video transcript to create and export ALL chunks\n", "\n", "import os\n", "import re\n", "from datetime import datetime, timedelta\n", "from google.colab import files\n", "\n", "def universal_chunk_export_tool():\n", "    \"\"\"\n", "    Comprehensive tool to extract transcript from saved files and export ALL chunks\n", "    Works with any video length and automatically handles all chunks\n", "    \"\"\"\n", "\n", "    print(\"🔧 UNIVERSAL CHUNK EXPORT TOOL\")\n", "    print(\"=\"*60)\n", "    print(\"This tool will extract and export ALL chunks for external analysis\\n\")\n", "\n", "    # Step 1: Find and read transcript\n", "    print(\"📄 Step 1: Looking for transcript files...\")\n", "\n", "    transcript_files = [\n", "        \"/content/EARLY_TRANSCRIPT_ONLY.txt\",\n", "        \"/content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt\",\n", "        \"/content/CERTIFIED_FORENSIC_LEGAL_TRANSCRIPT.txt\"\n", "    ]\n", "\n", "    transcript_content = None\n", "    source_file = None\n", "\n", "    for file_path in transcript_files:\n", "        if os.path.exists(file_path):\n", "            print(f\"✅ Found: {file_path}\")\n", "            with open(file_path, \"r\", encoding=\"utf-8\") as f:\n", "                content = f.read()\n", "                # Check if this file contains a transcript\n", "                if \"TRANSCRIPT\" in content and (\"[\" in content or \"SPEAKER\" in content):\n", "                    transcript_content = content\n", "                    source_file = file_path\n", "                    break\n", "\n", "    if not transcript_content:\n", "        print(\"❌ No transcript file found!\")\n", "        return False\n", "\n", "    print(f\"✅ Using transcript from: {source_file}\")\n", "\n", "    # Step 2: Extract video info and transcript text\n", "    print(\"\\n📊 Step 2: Extracting transcript information...\")\n", "\n", "    # Extract video filename if available\n", "    video_name = \"Unknown\"\n", "    if \"Video:\" in transcript_content:\n", "        video_match = re.search(r'Video:\\s*(.+?)\\n', transcript_content)\n", "        if video_match:\n", "            video_name = video_match.group(1).strip()\n", "\n", "    # Extract skip seconds if available\n", "    skip_seconds = 30  # default\n", "    if \"Skip seconds:\" in transcript_content:\n", "        skip_match = re.search(r'Skip seconds:\\s*(\\d+)', transcript_content)\n", "        if skip_match:\n", "            skip_seconds = int(skip_match.group(1))\n", "\n", "    # Extract total words if available\n", "    total_words = \"Unknown\"\n", "    if \"Total words:\" in transcript_content:\n", "        words_match = re.search(r'Total words:\\s*(\\d+)', transcript_content)\n", "        if words_match:\n", "            total_words = words_match.group(1)\n", "\n", "    print(f\"📹 Video: {video_name}\")\n", "    print(f\"⏱️ Skip seconds: {skip_seconds}\")\n", "    print(f\"📝 Total words: {total_words}\")\n", "\n", "    # Step 3: Extract the actual transcript\n", "    print(\"\\n✂️ Step 3: Extracting and chunking transcript...\")\n", "\n", "    # Find the transcript section\n", "    lines = transcript_content.split('\\n')\n", "    transcript_lines = []\n", "    in_transcript = False\n", "\n", "    # Look for various transcript start markers\n", "    transcript_markers = [\n", "        \"FULL TRANSCRIPT\",\n", "        \"TRANSCRIPT WITH SPEAKER\",\n", "        \"ANNOTATED TRANSCRIPT\",\n", "        \"SPEAKER-IDENTIFIED TRANSCRIPT\"\n", "    ]\n", "\n", "    for line in lines:\n", "        # Check if we're entering the transcript section\n", "        if any(marker in line.upper() for marker in transcript_markers):\n", "            in_transcript = True\n", "            continue\n", "\n", "        # Check if we've reached the end\n", "        if in_transcript and any(end in line for end in [\"[END OF TRANSCRIPT]\", \"===\", \"LEGAL ANALYSIS\", \"CERTIFICATION\"]):\n", "            break\n", "\n", "        # Collect transcript lines\n", "        if in_transcript and line.strip():\n", "            # Only include lines that look like transcript (have timestamps or speaker labels)\n", "            if re.match(r'^\\[[\\d:]+\\]', line) or 'SPEAKER' in line or ': ' in line:\n", "                transcript_lines.append(line)\n", "\n", "    if not transcript_lines:\n", "        print(\"⚠️ No transcript lines found. Attempting alternative extraction...\")\n", "        # Fallback: look for any lines with timestamp format\n", "        for line in lines:\n", "            if re.match(r'^\\[[\\d:]+\\]', line):\n", "                transcript_lines.append(line)\n", "\n", "    print(f\"✅ Extracted {len(transcript_lines)} transcript lines\")\n", "\n", "    # Step 4: Create chunks\n", "    chunks = []\n", "    current_chunk = []\n", "    current_size = 0\n", "    max_chunk_size = 5000  # Characters per chunk\n", "\n", "    for line in transcript_lines:\n", "        line_size = len(line)\n", "\n", "        # Start new chunk if size exceeded\n", "        if current_size + line_size > max_chunk_size and current_chunk:\n", "            chunks.append('\\n'.join(current_chunk))\n", "            current_chunk = [line]\n", "            current_size = line_size\n", "        else:\n", "            current_chunk.append(line)\n", "            current_size += line_size\n", "\n", "    # Add final chunk\n", "    if current_chunk:\n", "        chunks.append('\\n'.join(current_chunk))\n", "\n", "    print(f\"✅ Created {len(chunks)} chunks\")\n", "\n", "    # Step 5: Calculate approximate time coverage per chunk\n", "    # Estimate based on video length and chunk count\n", "    if len(chunks) > 0:\n", "        estimated_minutes_per_chunk = 60 / len(chunks)  # Assuming ~60 min video\n", "    else:\n", "        estimated_minutes_per_chunk = 0\n", "\n", "    # Step 6: Export ALL chunks\n", "    print(f\"\\n💾 Step 4: Exporting all {len(chunks)} chunks...\")\n", "\n", "    output_path = f\"/content/ALL_CHUNKS_EXPORT_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt\"\n", "\n", "    with open(output_path, \"w\", encoding=\"utf-8\") as f:\n", "        # Header\n", "        f.write(\"COMPLETE TRANSCRIPT CHUNKS FOR EXTERNAL LEGAL ANALYSIS\\n\")\n", "        f.write(\"=\"*70 + \"\\n\\n\")\n", "        f.write(f\"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\")\n", "        f.write(f\"Source: {os.path.basename(source_file)}\\n\")\n", "        f.write(f\"Video: {video_name}\\n\")\n", "        f.write(f\"Total chunks: {len(chunks)}\\n\")\n", "        f.write(f\"Estimated coverage: ~{estimated_minutes_per_chunk:.1f} minutes per chunk\\n\\n\")\n", "\n", "        # Instructions\n", "        f.write(\"INSTRUCTIONS FOR EXTERNAL ANALYSIS:\\n\")\n", "        f.write(\"-\"*50 + \"\\n\")\n", "        f.write(\"1. Copy each chunk to your preferred AI tool (ChatGPT, Claude, etc.)\\n\")\n", "        f.write(\"2. Use the analysis prompt below for each chunk\\n\")\n", "        f.write(\"3. Save each chunk's analysis with its number\\n\")\n", "        f.write(\"4. Compile all analyses into final report\\n\\n\")\n", "\n", "        # Analysis prompt\n", "        f.write(\"ANALYSIS PROMPT FOR EACH CHUNK:\\n\")\n", "        f.write(\"-\"*50 + \"\\n\")\n", "        f.write(\"\"\"\n", "You are a forensic legal analyst reviewing police body camera transcript.\n", "Analyze this transcript section for:\n", "\n", "1. CONSTITUTIONAL VIOLATIONS:\n", "   - 4th Amendment (unreasonable search/seizure, home entry)\n", "   - 5th Amendment (Miranda rights, self-incrimination)\n", "   - 8th Amendment (cruel treatment, dignity violations)\n", "   - 14th Amendment (due process, equal protection)\n", "\n", "2. SPECIFIC CONCERNS:\n", "   - Handcuffing/restraining person in towel or minimal clothing\n", "   - Public exposure and dignity violations\n", "   - Mental health crisis mishandling\n", "   - Use of force on cooperative subjects\n", "   - Baker Act procedural violations\n", "   - Privacy invasions in home\n", "\n", "3. IDENTIFY AND QUOTE:\n", "   - Exact quotes showing violations\n", "   - Timestamps of concerning events\n", "   - Officer statements showing intent/bias\n", "   - Evidence of retaliation or escalation\n", "   - Attempts to cover up or coordinate stories\n", "\n", "4. PATTERN RECOGNITION:\n", "   - Repeated violations\n", "   - Escalation patterns\n", "   - De-escalation failures\n", "   - Policy breaches\n", "\n", "Provide specific timestamps and exact quotes for any violations found.\n", "Note any concerning patterns or systemic issues.\n", "\"\"\")\n", "\n", "        f.write(\"\\n\" + \"=\"*70 + \"\\n\")\n", "        f.write(\"TRANSCRIPT CHUNKS BEGIN BELOW\\n\")\n", "        f.write(\"=\"*70 + \"\\n\")\n", "\n", "        # Export ALL chunks\n", "        for i, chunk in enumerate(chunks):\n", "            f.write(f\"\\n\\n{'='*70}\\n\")\n", "            f.write(f\"CHUNK {i+1} of {len(chunks)}\\n\")\n", "\n", "            # Try to determine time range from timestamps in chunk\n", "            timestamps = re.findall(r'\\[(\\d+:\\d+:\\d+)\\]', chunk)\n", "            if timestamps:\n", "                f.write(f\"Time range: {timestamps[0]} - {timestamps[-1]}\\n\")\n", "            else:\n", "                start_min = i * estimated_minutes_per_chunk\n", "                end_min = (i + 1) * estimated_minutes_per_chunk\n", "                f.write(f\"Estimated coverage: minutes {start_min:.0f}-{end_min:.0f}\\n\")\n", "\n", "            f.write(f\"{'='*70}\\n\\n\")\n", "            f.write(chunk)\n", "            f.write(\"\\n\\n--- END OF CHUNK ---\")\n", "\n", "        # Add compilation template\n", "        f.write(\"\\n\\n\" + \"=\"*70 + \"\\n\")\n", "        f.write(\"ANALYSIS COMPILATION TEMPLATE\\n\")\n", "        f.write(\"=\"*70 + \"\\n\\n\")\n", "        f.write(\"After analyzing all chunks, compile your findings:\\n\\n\")\n", "        f.write(\"COMPREHENSIVE LEGAL ANALYSIS SUMMARY\\n\")\n", "        f.write(\"-\"*40 + \"\\n\\n\")\n", "        f.write(\"1. TOTAL VIOLATIONS BY CATEGORY:\\n\")\n", "        f.write(\"   - Constitutional: [list with counts]\\n\")\n", "        f.write(\"   - Procedural: [list with counts]\\n\")\n", "        f.write(\"   - Dignity/Privacy: [list with counts]\\n\")\n", "        f.write(\"   - Use of Force: [list with counts]\\n\\n\")\n", "        f.write(\"2. MOST SERIOUS VIOLATIONS:\\n\")\n", "        f.write(\"   [List top 5-10 with timestamps and quotes]\\n\\n\")\n", "        f.write(\"3. PATTERN ANALYSIS:\\n\")\n", "        f.write(\"   [Identify systemic issues across chunks]\\n\\n\")\n", "        f.write(\"4. OFFICER CONDUCT:\\n\")\n", "        f.write(\"   [List concerning behaviors by officer]\\n\\n\")\n", "        f.write(\"5. RECOMMENDATIONS:\\n\")\n", "        f.write(\"   [Legal remedies and actions]\\n\")\n", "\n", "    # Download the file\n", "    print(f\"\\n📥 Downloading complete chunk export...\")\n", "    files.download(output_path)\n", "\n", "    # Create a summary file\n", "    summary_path = f\"/content/CHUNK_EXPORT_SUMMARY_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt\"\n", "\n", "    with open(summary_path, \"w\", encoding=\"utf-8\") as f:\n", "        f.write(\"CHUNK EXPORT SUMMARY\\n\")\n", "        f.write(\"=\"*40 + \"\\n\\n\")\n", "        f.write(f\"Video: {video_name}\\n\")\n", "        f.write(f\"Total chunks created: {len(chunks)}\\n\")\n", "        f.write(f\"Characters per chunk: ~{max_chunk_size}\\n\")\n", "        f.write(f\"Export file: {os.path.basename(output_path)}\\n\\n\")\n", "\n", "        f.write(\"QUICK STATS:\\n\")\n", "        f.write(f\"- Total transcript lines: {len(transcript_lines)}\\n\")\n", "        f.write(f\"- Average lines per chunk: {len(transcript_lines) // len(chunks) if chunks else 0}\\n\")\n", "        f.write(f\"- Estimated analysis time: {len(chunks) * 2}-{len(chunks) * 3} minutes\\n\\n\")\n", "\n", "        f.write(\"NEXT STEPS:\\n\")\n", "        f.write(\"1. Open the export file\\n\")\n", "        f.write(\"2. Copy chunks one at a time to AI tool\\n\")\n", "        f.write(\"3. Save each analysis\\n\")\n", "        f.write(\"4. Co<PERSON><PERSON> using provided template\\n\")\n", "\n", "    files.download(summary_path)\n", "\n", "    print(\"\\n✅ EXPORT COMPLETE!\")\n", "    print(f\"📊 Total chunks: {len(chunks)}\")\n", "    print(f\"📁 Files downloaded:\")\n", "    print(f\"   - {os.path.basename(output_path)}\")\n", "    print(f\"   - {os.path.basename(summary_path)}\")\n", "    print(\"\\n🎯 This export includes ALL chunks (1 through {}) for complete analysis\".format(len(chunks)))\n", "\n", "    return True\n", "\n", "# Run the tool\n", "if __name__ == \"__main__\":\n", "    universal_chunk_export_tool()"], "metadata": {"id": "BdrtbHmwCLvO"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["  # Check for extracted video frames\n", "  import os\n", "\n", "  print(\"🖼️ Checking for extracted video frames...\\n\")\n", "\n", "  # Check the frames directory\n", "  frames_dir = \"/content/video_frames\"\n", "  if os.path.exists(frames_dir):\n", "      frame_files = [f for f in os.listdir(frames_dir) if f.endswith('.jpg')]\n", "      print(f\"✅ Found {len(frame_files)} extracted frames!\")\n", "      print(f\"📁 Located in: {frames_dir}\")\n", "\n", "      if frame_files:\n", "          # Show first few frame names\n", "          print(f\"\\n📸 Sample frames:\")\n", "          for frame in sorted(frame_files)[:5]:\n", "              size = os.path.getsize(os.path.join(frames_dir, frame)) / 1024\n", "              print(f\"   - {frame} ({size:.1f} KB)\")\n", "\n", "          # Create a package with frames and analysis\n", "          print(\"\\n📦 Creating visual analysis package...\")\n", "\n", "          # Option 1: Create a zip file with all frames\n", "          import zipfile\n", "          zip_path = \"/content/video_frames_package.zip\"\n", "\n", "          with zipfile.ZipFile(zip_path, 'w') as zipf:\n", "              for frame in frame_files:\n", "                  frame_path = os.path.join(frames_dir, frame)\n", "                  zipf.write(frame_path, frame)\n", "\n", "          print(f\"✅ Created {os.path.getsize(zip_path) / (1024*1024):.1f} MB zip file\")\n", "\n", "          from google.colab import files\n", "          print(\"📥 Downloading frames package...\")\n", "          files.download(zip_path)\n", "  else:\n", "      print(\"❌ No frames directory found\")\n", "      print(\"The frames may have been cleaned up\")"], "metadata": {"id": "uotAMkWtMxhd"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# =============================================================================\n", "# ADDITIONAL RECOVERY AND VALIDATION FUNCTIONS FROM OLD_PIPELINE\n", "# =============================================================================\n", "\n", "def check_pipeline_status():\n", "    \"\"\"Check the status of pipeline execution\"\"\"\n", "    print(\"📊 PIPELINE STATUS CHECK\")\n", "    print(\"=\"*50)\n", "    \n", "    status = {\n", "        'dependencies': <PERSON><PERSON><PERSON>,\n", "        'video_download': <PERSON><PERSON><PERSON>,\n", "        'authentication': <PERSON><PERSON><PERSON>,\n", "        'audio_processing': <PERSON><PERSON><PERSON>,\n", "        'transcription': <PERSON><PERSON><PERSON>,\n", "        'speaker_diarization': <PERSON><PERSON><PERSON>,\n", "        'visual_analysis': <PERSON><PERSON><PERSON>,\n", "        'legal_analysis': <PERSON><PERSON><PERSON>,\n", "        'final_report': <PERSON><PERSON><PERSON>\n", "    }\n", "    \n", "    # Check for evidence of each step\n", "    import os\n", "    \n", "    if 'whisper' in globals() or os.path.exists(\"/content/whisper_transcription.json\"):\n", "        status['transcription'] = True\n", "        \n", "    if 'enhanced_transcript' in globals() or os.path.exists(\"/content/SPEAKER_IDENTIFIED_TRANSCRIPT.txt\"):\n", "        status['speaker_diarization'] = True\n", "        \n", "    if os.path.exists(\"/content/visual_frame_analysis.json\"):\n", "        status['visual_analysis'] = True\n", "        \n", "    if os.path.exists(\"/content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt\"):\n", "        status['final_report'] = True\n", "        \n", "    # Display status\n", "    print(\"\\nPIPELINE COMPLETION STATUS:\")\n", "    for step, completed in status.items():\n", "        icon = \"✅\" if completed else \"❌\"\n", "        print(f\"{icon} {step.replace('_', ' ').title()}\")\n", "    \n", "    completion_pct = (sum(status.values()) / len(status)) * 100\n", "    print(f\"\\n📈 Overall Completion: {completion_pct:.1f}%\")\n", "    \n", "    return status\n", "\n", "def complete_partial_analysis_recovery():\n", "    \"\"\"Recovery function to complete analysis that was interrupted\"\"\"\n", "    print(\"🔧 PARTIAL ANALYSIS RECOVERY TOOL\")\n", "    print(\"=\"*50)\n", "    \n", "    import os\n", "    from google.colab import files\n", "    \n", "    # Check what we have\n", "    print(\"\\n📊 Checking available data...\")\n", "    \n", "    files_found = {}\n", "    check_files = {\n", "        'transcript': \"/content/SPEAKER_IDENTIFIED_TRANSCRIPT.txt\",\n", "        'whisper': \"/content/whisper_transcription.json\",\n", "        'visual': \"/content/visual_frame_analysis.json\",\n", "        'chunks': \"/content/transcript_chunks.json\",\n", "        'analysis': \"/content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt\"\n", "    }\n", "    \n", "    for name, path in check_files.items():\n", "        if os.path.exists(path):\n", "            size = os.path.getsize(path) / 1024\n", "            files_found[name] = {'path': path, 'size': size}\n", "            print(f\"✅ {name}: {size:.1f} KB\")\n", "        else:\n", "            print(f\"❌ {name}: Not found\")\n", "    \n", "    # If we have core files, create recovery summary\n", "    if len(files_found) >= 3:\n", "        print(\"\\n📝 Creating recovery summary...\")\n", "        \n", "        summary_path = \"/content/RECOVERY_SUMMARY.txt\"\n", "        with open(summary_path, 'w') as f:\n", "            f.write(\"ANALYSIS RECOVERY SUMMARY\\n\")\n", "            f.write(\"=\"*50 + \"\\n\\n\")\n", "            f.write(f\"Recovery Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\\n\")\n", "            \n", "            f.write(\"FILES RECOVERED:\\n\")\n", "            for name, info in files_found.items():\n", "                f.write(f\"- {name}: {info['size']:.1f} KB\\n\")\n", "            \n", "            f.write(\"\\n\\nNEXT STEPS:\\n\")\n", "            f.write(\"1. Download all available files\\n\")\n", "            f.write(\"2. Use external analysis for remaining chunks\\n\")\n", "            f.write(\"3. Compile final report manually\\n\")\n", "            \n", "        files.download(summary_path)\n", "        \n", "        # Download all found files\n", "        print(\"\\n📥 Downloading all recovered files...\")\n", "        for name, info in files_found.items():\n", "            print(f\"Downloading {name}...\")\n", "            files.download(info['path'])\n", "            \n", "    return files_found"], "metadata": {"id": "recovery_functions_cell"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# =============================================================================\n", "# ENHANCED USAGE GUIDE AND FINAL SUMMARY\n", "# =============================================================================\n", "\n", "def print_complete_usage_guide():\n", "    \"\"\"Print comprehensive usage guide for the entire pipeline\"\"\"\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"COMPLETE ENHANCED FORENSIC TRANSCRIPTION PIPELINE\")\n", "    print(\"COMPREHENSIVE USAGE GUIDE\")\n", "    print(\"=\"*80)\n", "    \n", "    print(\"\\n📋 QUICK START GUIDE:\")\n", "    print(\"1. Update video file_id and filename in Cell 2\")\n", "    print(\"2. Update API keys in Cell 3\")\n", "    print(\"3. Run cells sequentially\")\n", "    print(\"4. Monitor for rate limits and use recovery functions if needed\")\n", "    \n", "    print(\"\\n⚡ RATE LIMIT STRATEGIES:\")\n", "    print(\"If you encounter rate limits:\")\n", "    print(\"1. Use recovery functions:\")\n", "    print(\"   - recover_failed_analysis()\")\n", "    print(\"   - export_for_external_analysis()\")\n", "    print(\"   - complete_partial_analysis_recovery()\")\n", "    print(\"2. Wait 5-10 minutes and retry\")\n", "    print(\"3. Use external analysis for remaining chunks\")\n", "    \n", "    print(\"\\n📥 PROGRESSIVE DOWNLOADS:\")\n", "    print(\"Files download automatically throughout execution:\")\n", "    print(\"- After transcription: whisper_transcription.json\")\n", "    print(\"- After diarization: SPEAKER_IDENTIFIED_TRANSCRIPT.txt\")\n", "    print(\"- After visual analysis: visual_frame_analysis.json\")\n", "    print(\"- Final: COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt\")\n", "    \n", "    print(\"\\n🔍 TROUBLESHOOTING:\")\n", "    print(\"1. Check pipeline status: check_pipeline_status()\")\n", "    print(\"2. Recover partial work: complete_partial_analysis_recovery()\")\n", "    \n", "    print(\"\\n📊 EXPECTED OUTPUT:\")\n", "    print(\"Complete execution produces:\")\n", "    print(\"- Full transcript with speaker identification\")\n", "    print(\"- Visual analysis of all frames\")\n", "    print(\"- Legal violation detection and timeline\")\n", "    print(\"- Comprehensive forensic analysis document\")\n", "    print(\"- Executive summary with recommendations\")\n", "    \n", "    print(\"\\n⚠️ IMPORTANT NOTES:\")\n", "    print(\"- 20-second delays between chunks prevent rate limits\")\n", "    print(\"- GPT-4o used for enhanced visual analysis\")\n", "    print(\"- All timestamps adjusted for skip_seconds\")\n", "    print(\"- Forensic-grade refers to quality, not legal certification\")\n", "    \n", "    print(\"\\n\" + \"=\"*80)\n", "\n", "# Print the usage guide\n", "print_complete_usage_guide()\n", "\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"ENHANCED FORENSIC TRANSCRIPTION PIPELINE\")\n", "print(\"INTEGRATION COMPLETE\")\n", "print(\"=\"*80)\n", "print(\"\\n✅ KEY IMPROVEMENTS IMPLEMENTED:\")\n", "print(\"   • Forensic-grade analysis clarification added\")\n", "print(\"   • GPT-4o model confirmed for visual analysis\")\n", "print(\"   • 20-second delays added between chunk processing\")\n", "print(\"   • Progressive downloads after key steps\")\n", "print(\"   • Enhanced max_tokens for better visual analysis\")\n", "print(\"   • Recovery and validation functions integrated\")\n", "print(\"   • Comprehensive usage guide included\")\n", "print(\"\\n🎯 READY FOR GOOGLE COLAB EXECUTION\")\n", "print(\"   Optimized for T4 GPU with High RAM\")\n", "print(\"\\n\" + \"=\"*80)"], "metadata": {"id": "usage_guide_cell"}, "execution_count": null, "outputs": []}]}