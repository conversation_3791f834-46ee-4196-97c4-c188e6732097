import json

# Read the complete pipeline code
with open('_complete_enhanced_forensic_transcription_pipeline__1.py', 'r', encoding='utf-8') as f:
    full_code = f.read()

# Split the code into cells based on the Cell markers
cells_content = []
current_cell = []
current_cell_type = 'code'
in_cell = False
skip_header = True

for line in full_code.split('\n'):
    if skip_header and '# Cell 1:' in line:
        skip_header = False
    
    if not skip_header:
        if '# Cell' in line and '======' in line:
            # Save previous cell if exists
            if current_cell:
                cell_content = '\n'.join(current_cell)
                cells_content.append((current_cell_type, cell_content))
                current_cell = []
            
            # Start new cell with comment
            current_cell = [line]
            current_cell_type = 'code'
            in_cell = True
        elif in_cell:
            current_cell.append(line)

# Add the last cell
if current_cell:
    cell_content = '\n'.join(current_cell)
    cells_content.append((current_cell_type, cell_content))

# Create notebook structure
notebook = {
    "cells": [],
    "metadata": {
        "accelerator": "GPU",
        "colab": {
            "gpuType": "T4",
            "provenance": [],
            "collapsed_sections": []
        },
        "kernelspec": {
            "display_name": "Python 3",
            "name": "python3"
        },
        "language_info": {
            "codemirror_mode": {
                "name": "ipython",
                "version": 3
            },
            "file_extension": ".py",
            "mimetype": "text/x-python",
            "name": "python",
            "nbconvert_exporter": "python",
            "pygments_lexer": "ipython3",
            "version": "3.10.12"
        }
    },
    "nbformat": 4,
    "nbformat_minor": 0
}

# Add header markdown cell
header_md = """# COMPLETE ENHANCED FORENSIC TRANSCRIPTION PIPELINE

## Instructions for Processing Multiple Videos

This pipeline is designed to process videos one at a time in Google Colab.

### TO PROCESS EACH VIDEO:
1. Run Cells 1-6 once at the beginning of your session
2. For each video:
   - Update Cell 2 with new file_id and video_filename
   - Update Cell 7 skip_seconds parameter if needed (default is 30)
   - Run Cell 2 to download the new video
   - Run Cell 7 to process and analyze
   - The output file will download automatically when complete

### SKIP_SECONDS PARAMETER:
- Default: 30 seconds (for videos with initial silence/muted sections)
- Set to 0 for videos that start immediately with audio
- Adjust as needed based on each video's characteristics

### CLEARING OUTPUTS:
- To keep notebook tidy: Edit → Clear all outputs
- This does NOT require re-running setup cells
- Only restart runtime if you encounter memory errors

### NOTE: 
- The pipeline uses OpenAI API (gpt-4o for vision, gpt-4 for text)
- Make sure your API key has sufficient credits for processing
- You do NOT need to restart runtime between videos unless you encounter memory errors"""

notebook["cells"].append({
    "cell_type": "markdown",
    "metadata": {},
    "source": header_md.split('\n')
})

# Process and add code cells
for i, (cell_type, content) in enumerate(cells_content):
    # Clean up the content
    lines = content.split('\n')
    
    # Remove empty lines at the beginning
    while lines and not lines[0].strip():
        lines.pop(0)
    
    # Remove empty lines at the end
    while lines and not lines[-1].strip():
        lines.pop()
    
    # Create cell
    cell = {
        "cell_type": cell_type,
        "metadata": {},
        "outputs": [],
        "source": lines
    }
    
    if cell_type == "code":
        cell["execution_count"] = None
    
    notebook["cells"].append(cell)

# Write the notebook
with open('Enhanced_Forensic_Transcription_Pipeline_Complete.ipynb', 'w', encoding='utf-8') as f:
    json.dump(notebook, f, indent=1, ensure_ascii=False)

print("✅ Notebook created successfully: Enhanced_Forensic_Transcription_Pipeline_Complete.ipynb")