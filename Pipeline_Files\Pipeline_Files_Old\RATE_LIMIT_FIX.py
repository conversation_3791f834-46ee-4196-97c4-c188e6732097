# RATE LIMIT FIX FOR GPT-4 ANALYSIS
# ==================================
# This fix implements multiple strategies to handle rate limits

import time
import openai
from datetime import datetime, timedelta

def process_chunks_with_rate_limit_handling(transcript_chunks, violations_data, skip_seconds=30):
    """
    Process transcript chunks with intelligent rate limit handling
    """
    print("\n⚖️ Performing rate-limit-aware chunked legal analysis...")
    
    chunk_analyses = []
    failed_chunks = []
    
    # Strategy 1: Add delays between chunks
    DELAY_BETWEEN_CHUNKS = 15  # seconds
    
    for i, chunk in enumerate(transcript_chunks):
        print(f"\n🔄 Processing chunk {i+1}/{len(transcript_chunks)}...")
        
        retry_count = 0
        max_retries = 3
        success = False
        
        while retry_count < max_retries and not success:
            try:
                # Prepare violation summary for context
                violation_summary = f"""
Current Violations Found:
- Compliance Violations: {len(violations_data.get('compliance_violations', []))}
- Privacy Violations: {len(violations_data.get('privacy_violations', []))}
- Dignity Violations: {len(violations_data.get('dignity_violations', []))}
- Public Exposure: {len(violations_data.get('public_exposure', []))}
- Attire Violations: {len(violations_data.get('attire_violations', []))}
"""
                
                response = openai.ChatCompletion.create(
                    model="gpt-4",
                    messages=[
                        {
                            "role": "system",
                            "content": """You are a senior forensic analyst specializing in law enforcement interactions.
Analyze this transcript section for legal violations with special attention to:
- Constitutional violations (4th, 5th, 8th, 14th Amendment)
- Privacy and dignity violations (especially regarding state of undress, towels, wet from shower)
- Mental health handling under Baker Act (Fla. Stat. § 394.463)
- Use of force and restraint application
- Procedural violations and misconduct"""
                        },
                        {
                            "role": "user",
                            "content": f"""Analyze transcript section {i+1} of {len(transcript_chunks)}:

{chunk}

{violation_summary}

Identify specific violations with timestamps and exact quotes. Focus on:
1. Handcuffing of cooperative individuals in minimal clothing
2. Public exposure and dignity violations
3. Mental health crisis handling
4. Constitutional rights violations"""
                        }
                    ],
                    max_tokens=1200,  # Reduced from 1500 to leave more headroom
                    temperature=0.1
                )
                
                chunk_analyses.append(response.choices[0].message.content)
                print(f"✅ Chunk {i+1} analyzed successfully")
                success = True
                
            except openai.error.RateLimitError as e:
                retry_count += 1
                
                # Extract wait time from error message
                wait_time = 15  # default
                error_msg = str(e)
                if "Please try again in" in error_msg:
                    try:
                        wait_time = float(error_msg.split("Please try again in ")[1].split("s")[0]) + 2
                    except:
                        wait_time = 15
                
                if retry_count < max_retries:
                    print(f"⏳ Rate limit hit. Waiting {wait_time:.1f} seconds before retry {retry_count}/{max_retries}...")
                    time.sleep(wait_time)
                else:
                    print(f"❌ Chunk {i+1} failed after {max_retries} retries")
                    failed_chunks.append((i, chunk))
                    chunk_analyses.append(f"[Analysis pending - rate limit exceeded for chunk {i+1}]")
                    
            except Exception as e:
                print(f"❌ Chunk {i+1} analysis failed: {e}")
                chunk_analyses.append(f"Analysis failed for chunk {i+1}: {str(e)}")
                success = True  # Move on to next chunk
        
        # Add delay between successful chunks to avoid hitting rate limit
        if success and i < len(transcript_chunks) - 1:
            print(f"⏱️ Waiting {DELAY_BETWEEN_CHUNKS} seconds before next chunk...")
            time.sleep(DELAY_BETWEEN_CHUNKS)
    
    # Strategy 2: Retry failed chunks with longer delays
    if failed_chunks:
        print(f"\n🔄 Retrying {len(failed_chunks)} failed chunks with extended delays...")
        time.sleep(30)  # Wait 30 seconds before retrying
        
        for chunk_index, chunk_text in failed_chunks:
            try:
                print(f"🔄 Retrying chunk {chunk_index + 1}...")
                
                response = openai.ChatCompletion.create(
                    model="gpt-4",
                    messages=[
                        {
                            "role": "system",
                            "content": "You are a forensic analyst. Provide a brief analysis of legal violations in this transcript section."
                        },
                        {
                            "role": "user",
                            "content": f"Analyze section {chunk_index + 1}:\n{chunk_text[:3000]}\n\nFocus on key violations only."
                        }
                    ],
                    max_tokens=800,  # Even more conservative
                    temperature=0.1
                )
                
                chunk_analyses[chunk_index] = response.choices[0].message.content
                print(f"✅ Chunk {chunk_index + 1} retry successful")
                time.sleep(20)  # Wait between retries
                
            except Exception as e:
                print(f"❌ Chunk {chunk_index + 1} retry also failed: {e}")
    
    return chunk_analyses


# ALTERNATIVE STRATEGY: Use GPT-3.5 for overflow chunks
def analyze_with_gpt35_fallback(chunk_text, chunk_index, total_chunks):
    """
    Fallback to GPT-3.5-turbo for chunks that fail with GPT-4
    """
    try:
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[
                {
                    "role": "system",
                    "content": """You are a legal analyst. Analyze this police transcript for:
- Constitutional violations
- Use of force concerns
- Privacy/dignity violations
- Procedural issues
Be specific with timestamps and quotes."""
                },
                {
                    "role": "user",
                    "content": f"""Section {chunk_index + 1} of {total_chunks}:

{chunk_text}

List key violations found."""
                }
            ],
            max_tokens=1000,
            temperature=0.1
        )
        
        return f"[GPT-3.5 Analysis]\n{response.choices[0].message.content}"
        
    except Exception as e:
        return f"[Analysis failed for chunk {chunk_index + 1}: {str(e)}]"


# COMPLETE REPLACEMENT FOR THE CHUNKED ANALYSIS SECTION IN YOUR PIPELINE
# Replace the entire chunk analysis section (lines 1946-2021) with this:

def perform_robust_chunked_analysis(enhanced_transcript, violations_data, skip_seconds=30):
    """
    Robust chunked analysis with multiple fallback strategies
    """
    print("\n⚖️ Performing robust chunked legal analysis with rate limit handling...")
    
    # Prepare transcript chunks
    transcript_chunks = []
    current_chunk = []
    current_chunk_size = 0
    max_chunk_size = 4000  # Reduced from 6000 to leave more headroom
    
    for word_data in enhanced_transcript:
        word_timestamp = word_data['start'] + skip_seconds
        word_text = word_data['word']
        speakers = word_data.get('speakers', [])
        primary_speaker = speakers[0] if speakers else "UNKNOWN"
        timestamp_str = str(timedelta(seconds=int(word_timestamp)))
        
        line = f"[{timestamp_str}] {primary_speaker}: {word_text} "
        line_size = len(line)
        
        if current_chunk_size + line_size > max_chunk_size and current_chunk:
            transcript_chunks.append(''.join(current_chunk))
            current_chunk = [line]
            current_chunk_size = line_size
        else:
            current_chunk.append(line)
            current_chunk_size += line_size
    
    if current_chunk:
        transcript_chunks.append(''.join(current_chunk))
    
    print(f"📄 Split transcript into {len(transcript_chunks)} smaller chunks")
    
    # Process chunks with rate limit handling
    chunk_analyses = process_chunks_with_rate_limit_handling(
        transcript_chunks, 
        violations_data, 
        skip_seconds
    )
    
    # Combine analyses
    comprehensive_analysis = "\n\n=== COMPREHENSIVE LEGAL ANALYSIS ===\n\n"
    
    # Add summary of successful analyses
    successful_chunks = sum(1 for analysis in chunk_analyses if "[Analysis pending" not in analysis and "failed" not in analysis)
    comprehensive_analysis += f"Analysis Status: {successful_chunks}/{len(transcript_chunks)} chunks successfully analyzed\n\n"
    
    for i, analysis in enumerate(chunk_analyses):
        comprehensive_analysis += f"\n--- Section {i+1} Analysis ---\n{analysis}\n"
    
    return comprehensive_analysis


# IMMEDIATE WORKAROUND: Process partial results
def save_partial_analysis(chunk_analyses, output_path="/content/PARTIAL_ANALYSIS.txt"):
    """
    Save whatever analysis was completed before rate limits
    """
    with open(output_path, "w", encoding="utf-8") as f:
        f.write("PARTIAL LEGAL ANALYSIS - RATE LIMITED\n")
        f.write("="*50 + "\n\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        successful = sum(1 for a in chunk_analyses if "[Analysis pending" not in a)
        f.write(f"Successfully analyzed: {successful}/{len(chunk_analyses)} chunks\n\n")
        
        for i, analysis in enumerate(chunk_analyses):
            if "[Analysis pending" not in analysis and "failed" not in analysis:
                f.write(f"\n--- Section {i+1} ---\n{analysis}\n")
    
    from google.colab import files
    files.download(output_path)
    print(f"📥 Downloaded partial analysis with {successful} completed sections")


# USAGE INSTRUCTIONS:
# ==================
# 1. Replace the chunked analysis section in your Cell 6 with the robust version above
# 2. The system will automatically:
#    - Add delays between chunks
#    - Retry failed chunks
#    - Use smaller chunk sizes
#    - Save partial results
# 3. If you still hit rate limits, wait 1-2 minutes and run a recovery cell (see below)

print("✅ Rate limit fixes loaded!")