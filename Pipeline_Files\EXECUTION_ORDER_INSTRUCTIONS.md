# EXACT EXECUTION ORDER FOR DUAL FRAME ANALYSIS
# ==============================================

## Step-by-Step Instructions:

### 1. **Run Cell 1** - Install Dependencies
   - Let it complete fully
   - Should see "✅ All dependencies installed successfully!"

### 2. **Run Cell 2** - Download Video
   - Make sure file_id and video_filename are correct
   - Should see "✅ Video file downloaded"

### 3. **Run Cell 3** - Authentication
   - Make sure your API keys are entered
   - Should see "✅ Authentication complete"

### 4. **Run Cell 4** - Load All Functions
   - This loads all the pipeline functions
   - Should see "✅ Core functions loaded"

### 5. **Run Cell 5** - Load Diarization
   - Loads speaker diarization model
   - Should see "✅ Speaker diarization pipeline loaded successfully!"

### 6. **UPLOAD YOUR FRAMES.ZIP** - Add this new cell and run it:
```python
# Cell 5.5: Upload Existing Frames
from google.colab import files

print("📤 Please select your frames.zip file to upload...")
uploaded = files.upload()

# Verify upload
for filename in uploaded.keys():
    print(f"✅ Uploaded: {filename}")
    if filename != 'frames.zip':
        # Rename to expected name
        import os
        os.rename(filename, 'frames.zip')
        print(f"✅ Renamed to frames.zip")
```

### 7. **Replace Cell 6** with the Modified Version
   - Copy the entire content from MODIFIED_CELL_6_WITH_DUAL_FRAMES.py
   - This is the special version that uses BOTH frame sets

### 8. **Run Cell 7** - Execute Analysis
```python
print("🚀 EXECUTING DUAL-FRAME FORENSIC ANALYSIS...")

video_path = f"/content/{video_filename}"
SKIP_SECONDS = 30

# This will use BOTH your existing frames AND extract new ones
result_file = process_complete_enhanced_forensic_analysis_dual_frames(
    video_path,
    skip_seconds=SKIP_SECONDS
)
```

## What Will Happen:

1. **Audio Processing** - Extracts and enhances audio
2. **Transcription** - Downloads immediately after completion
3. **Speaker Diarization** - Identifies speakers
4. **Speaker Transcript** - Downloads immediately
5. **NEW Frame Extraction** - Extracts ~177 frames starting at 40s, 60s, 80s, etc.
6. **Existing Frame Analysis** - Analyzes your uploaded ~177 frames at 30s, 50s, 70s, etc.
7. **Combined Analysis** - Merges both sets for ~354 frames total
8. **Visual Injections** - Creates injections every ~10 seconds instead of 20
9. **Chunk Analysis** - With 20-second delays between chunks
10. **Final Document** - Downloads comprehensive analysis

## Important Notes:

- You'll have DOUBLE the temporal resolution (frames every 10 seconds)
- Visual context injections will be much more frequent
- Total execution time will be longer due to analyzing 354 frames
- All outputs download progressively - you won't lose work

## DO NOT:
- Run Cell 8 (that's for a different workflow)
- Run the original Cell 6 (use the modified version)
- Skip the frame upload step

## Files You'll Receive:
1. whisper_transcription.json
2. SPEAKER_IDENTIFIED_TRANSCRIPT.txt
3. combined_visual_analysis.json (all 354 frame analyses)
4. transcript_chunks.json
5. analysis_chunks_1-5.json, 1-10.json, etc. (progressive)
6. all_chunk_analyses.json
7. COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt (final)