🚀 EXECUTING ENHANCED COMPLETE FORENSIC ANALYSIS...
🏛️ ENHANCED CERTIFIED FORENSIC AUDIOVISUAL ANALYSIS
================================================================================
✅ Raw audio extracted (skipping first 30 seconds)
🔊 Enhanced audio processing for difficult sections...
✅ Enhanced audio saved: /content/enhanced_forensic_audio_v2.wav
🎙️ Loading Whisper Large-v3 for maximum accuracy...
100%|█████████████████████████████████████| 2.88G/2.88G [00:53<00:00, 57.2MiB/s]
🔄 Transcribing with enhanced settings...
 99%|█████████▉| 349999/352999 [15:00<00:07, 388.85frames/s]
✅ Transcription complete: 24381 characters

🎥 Analyzing video frames (with caching)...
🆕 No cache found - performing new frame analysis...
📹 Analyzing video frames with enhanced attire/privacy detection...
🔍 Analyzing 177 video frames with attire focus...
✅ Enhanced frame analysis: 00:30
✅ Enhanced frame analysis: 00:50
✅ Enhanced frame analysis: 01:10
✅ Enhanced frame analysis: 01:30
✅ Enhanced frame analysis: 01:50
✅ Enhanced frame analysis: 02:10
✅ Enhanced frame analysis: 02:30
✅ Enhanced frame analysis: 02:50
✅ Enhanced frame analysis: 03:10
✅ Enhanced frame analysis: 03:30
✅ Enhanced frame analysis: 03:50
✅ Enhanced frame analysis: 04:10
✅ Enhanced frame analysis: 04:30
✅ Enhanced frame analysis: 04:50
✅ Enhanced frame analysis: 05:10
✅ Enhanced frame analysis: 05:30
✅ Enhanced frame analysis: 05:50
✅ Enhanced frame analysis: 06:10
✅ Enhanced frame analysis: 06:30
✅ Enhanced frame analysis: 06:50
✅ Enhanced frame analysis: 07:10
✅ Enhanced frame analysis: 07:30
✅ Enhanced frame analysis: 07:50
✅ Enhanced frame analysis: 08:10
✅ Enhanced frame analysis: 08:30
✅ Enhanced frame analysis: 08:50
✅ Enhanced frame analysis: 09:10
✅ Enhanced frame analysis: 09:30
✅ Enhanced frame analysis: 09:50
✅ Enhanced frame analysis: 10:10
✅ Enhanced frame analysis: 10:30
✅ Enhanced frame analysis: 10:50
✅ Enhanced frame analysis: 11:10
✅ Enhanced frame analysis: 11:30
✅ Enhanced frame analysis: 11:50
✅ Enhanced frame analysis: 12:10
✅ Enhanced frame analysis: 12:30
✅ Enhanced frame analysis: 12:50
✅ Enhanced frame analysis: 13:10
✅ Enhanced frame analysis: 13:30
✅ Enhanced frame analysis: 13:50
✅ Enhanced frame analysis: 14:10
✅ Enhanced frame analysis: 14:30
✅ Enhanced frame analysis: 14:50
✅ Enhanced frame analysis: 15:10
✅ Enhanced frame analysis: 15:30
✅ Enhanced frame analysis: 15:50
✅ Enhanced frame analysis: 16:10
✅ Enhanced frame analysis: 16:30
✅ Enhanced frame analysis: 16:50
✅ Enhanced frame analysis: 17:10
✅ Enhanced frame analysis: 17:30
✅ Enhanced frame analysis: 17:50
✅ Enhanced frame analysis: 18:10
✅ Enhanced frame analysis: 18:30
✅ Enhanced frame analysis: 18:50
✅ Enhanced frame analysis: 19:10
✅ Enhanced frame analysis: 19:30
✅ Enhanced frame analysis: 19:50
✅ Enhanced frame analysis: 20:10
✅ Enhanced frame analysis: 20:30
✅ Enhanced frame analysis: 20:50
✅ Enhanced frame analysis: 21:10
✅ Enhanced frame analysis: 21:30
✅ Enhanced frame analysis: 21:50
✅ Enhanced frame analysis: 22:10
✅ Enhanced frame analysis: 22:30
✅ Enhanced frame analysis: 22:50
✅ Enhanced frame analysis: 23:10
✅ Enhanced frame analysis: 23:30
✅ Enhanced frame analysis: 23:50
✅ Enhanced frame analysis: 24:10
✅ Enhanced frame analysis: 24:30
✅ Enhanced frame analysis: 24:50
✅ Enhanced frame analysis: 25:10
✅ Enhanced frame analysis: 25:30
✅ Enhanced frame analysis: 25:50
✅ Enhanced frame analysis: 26:10
✅ Enhanced frame analysis: 26:30
✅ Enhanced frame analysis: 26:50
✅ Enhanced frame analysis: 27:10
✅ Enhanced frame analysis: 27:30
✅ Enhanced frame analysis: 27:50
✅ Enhanced frame analysis: 28:10
✅ Enhanced frame analysis: 28:30
✅ Enhanced frame analysis: 28:50
✅ Enhanced frame analysis: 29:10
✅ Enhanced frame analysis: 29:30
✅ Enhanced frame analysis: 29:50
✅ Enhanced frame analysis: 30:10
✅ Enhanced frame analysis: 30:30
✅ Enhanced frame analysis: 30:50
✅ Enhanced frame analysis: 31:10
✅ Enhanced frame analysis: 31:30
✅ Enhanced frame analysis: 31:50
✅ Enhanced frame analysis: 32:10
✅ Enhanced frame analysis: 32:30
✅ Enhanced frame analysis: 32:50
✅ Enhanced frame analysis: 33:10
✅ Enhanced frame analysis: 33:30
✅ Enhanced frame analysis: 33:50
✅ Enhanced frame analysis: 34:10
✅ Enhanced frame analysis: 34:30
✅ Enhanced frame analysis: 34:50
✅ Enhanced frame analysis: 35:10
✅ Enhanced frame analysis: 35:30
✅ Enhanced frame analysis: 35:50
✅ Enhanced frame analysis: 36:10
✅ Enhanced frame analysis: 36:30
✅ Enhanced frame analysis: 36:50
✅ Enhanced frame analysis: 37:10
✅ Enhanced frame analysis: 37:30
✅ Enhanced frame analysis: 37:50
✅ Enhanced frame analysis: 38:10
✅ Enhanced frame analysis: 38:30
✅ Enhanced frame analysis: 38:50
✅ Enhanced frame analysis: 39:10
✅ Enhanced frame analysis: 39:30
✅ Enhanced frame analysis: 39:50
✅ Enhanced frame analysis: 40:10
✅ Enhanced frame analysis: 40:30
✅ Enhanced frame analysis: 40:50
✅ Enhanced frame analysis: 41:10
✅ Enhanced frame analysis: 41:30
✅ Enhanced frame analysis: 41:50
✅ Enhanced frame analysis: 42:10
✅ Enhanced frame analysis: 42:30
✅ Enhanced frame analysis: 42:50
✅ Enhanced frame analysis: 43:10
✅ Enhanced frame analysis: 43:30
✅ Enhanced frame analysis: 43:50
✅ Enhanced frame analysis: 44:10
✅ Enhanced frame analysis: 44:30
✅ Enhanced frame analysis: 44:50
✅ Enhanced frame analysis: 45:10
✅ Enhanced frame analysis: 45:30
✅ Enhanced frame analysis: 45:50
✅ Enhanced frame analysis: 46:10
✅ Enhanced frame analysis: 46:30
✅ Enhanced frame analysis: 46:50
✅ Enhanced frame analysis: 47:10
✅ Enhanced frame analysis: 47:30
✅ Enhanced frame analysis: 47:50
✅ Enhanced frame analysis: 48:10
✅ Enhanced frame analysis: 48:30
✅ Enhanced frame analysis: 48:50
✅ Enhanced frame analysis: 49:10
✅ Enhanced frame analysis: 49:30
✅ Enhanced frame analysis: 49:50
✅ Enhanced frame analysis: 50:10
✅ Enhanced frame analysis: 50:30
✅ Enhanced frame analysis: 50:50
✅ Enhanced frame analysis: 51:10
✅ Enhanced frame analysis: 51:30
✅ Enhanced frame analysis: 51:50
✅ Enhanced frame analysis: 52:10
✅ Enhanced frame analysis: 52:30
✅ Enhanced frame analysis: 52:50
✅ Enhanced frame analysis: 53:10
✅ Enhanced frame analysis: 53:30
✅ Enhanced frame analysis: 53:50
✅ Enhanced frame analysis: 54:10
✅ Enhanced frame analysis: 54:30
✅ Enhanced frame analysis: 54:50
✅ Enhanced frame analysis: 55:10
✅ Enhanced frame analysis: 55:30
✅ Enhanced frame analysis: 55:50
✅ Enhanced frame analysis: 56:10
✅ Enhanced frame analysis: 56:30
✅ Enhanced frame analysis: 56:50
✅ Enhanced frame analysis: 57:10
✅ Enhanced frame analysis: 57:30
✅ Enhanced frame analysis: 57:50
✅ Enhanced frame analysis: 58:10
✅ Enhanced frame analysis: 58:30
✅ Enhanced frame analysis: 58:50
✅ Enhanced frame analysis: 59:10
✅ Enhanced visual context analysis complete: 177 frames
💾 Saved 177 frame analyses to cache
👥 Running enhanced speaker diarization...
/usr/local/lib/python3.11/dist-packages/pyannote/audio/utils/reproducibility.py:74: ReproducibilityWarning: TensorFloat-32 (TF32) has been disabled as it might lead to reproducibility issues and lower accuracy.
It can be re-enabled by calling
   >>> import torch
   >>> torch.backends.cuda.matmul.allow_tf32 = True
   >>> torch.backends.cudnn.allow_tf32 = True
See https://github.com/pyannote/pyannote-audio/issues/1370 for more details.

  warnings.warn(
/usr/local/lib/python3.11/dist-packages/pyannote/audio/models/blocks/pooling.py:104: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at /pytorch/aten/src/ATen/native/ReduceOps.cpp:1831.)
  std = sequences.std(dim=-1, correction=1)
👥 Enhanced speaker overlap detection...
✅ Enhanced overlap detection complete: 77 overlaps found
🔗 Enhanced transcription and speaker combination...
✅ Enhanced transcript created: 4753 words

💾 Saving early transcript for immediate download...
📥 Downloading early transcript now...


------------------------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------------------------


✅ Early transcript downloaded! Continuing with full analysis...

📋 Conducting comprehensive legal analysis...
🔍 Cross-referencing utterances with visual behavior...
✅ Found 0 compliance violations
✅ Found 0 behavioral contradictions
🔒 Enhanced privacy and dignity violations analysis...
✅ Found 14 attire/clothing violations
✅ Found 106 privacy violations
✅ Found 5 dignity violations
✅ Found 141 public exposure incidents
⚠️ Analyzing harassment and retaliation patterns...
✅ Found 22 harassment indicators
✅ Found 0 retaliation patterns
🕵️ Analyzing misconduct patterns...
✅ Found 0 narrative shaping incidents
✅ Found 118 coordinated behavior patterns
📹 Analyzing body camera muting patterns...
✅ Found 6 potential camera muting references
👮 Extracting officer identities using BERT NER...
/usr/local/lib/python3.11/dist-packages/huggingface_hub/utils/_auth.py:94: UserWarning: 
The secret `HF_TOKEN` does not exist in your Colab secrets.
To authenticate with the Hugging Face Hub, create a token in your settings tab (https://huggingface.co/settings/tokens), set it as secret in your Google Colab and restart your session.
You will be able to reuse this secret in all of your notebooks.
Please note that authentication is recommended but still optional to access public models or datasets.
  warnings.warn(


------------------------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------------------------


Device set to use cuda:0
✅ Identified 4 unique officer references
📉 Analyzing de-escalation patterns...
✅ Found 17 de-escalation attempts
✅ Found 83 escalation events
📊 Analyzing transcript confidence metrics...
✅ Confidence analysis complete: 64.7% high confidence

⚖️ Performing chunked comprehensive legal analysis...
📄 Split transcript into 23 chunks for analysis
🔄 Analyzing chunk 1/23...
✅ Chunk 1 analyzed successfully
🔄 Analyzing chunk 2/23...
✅ Chunk 2 analyzed successfully
🔄 Analyzing chunk 3/23...
✅ Chunk 3 analyzed successfully
🔄 Analyzing chunk 4/23...
✅ Chunk 4 analyzed successfully
🔄 Analyzing chunk 5/23...
✅ Chunk 5 analyzed successfully
🔄 Analyzing chunk 6/23...
✅ Chunk 6 analyzed successfully
🔄 Analyzing chunk 7/23...
❌ Chunk 7 analysis failed: Rate limit reached for gpt-4 in organization org-OIGqQsD4In0F2R2AeiNDUvI4 on tokens per min (TPM): Limit 10000, Used 10000, Requested 1734. Please try again in 10.404s. Visit https://platform.openai.com/account/rate-limits to learn more.
🔄 Analyzing chunk 8/23...
❌ Chunk 8 analysis failed: Rate limit reached for gpt-4 in organization org-OIGqQsD4In0F2R2AeiNDUvI4 on tokens per min (TPM): Limit 10000, Used 10000, Requested 1735. Please try again in 10.41s. Visit https://platform.openai.com/account/rate-limits to learn more.
🔄 Analyzing chunk 9/23...
❌ Chunk 9 analysis failed: Rate limit reached for gpt-4 in organization org-OIGqQsD4In0F2R2AeiNDUvI4 on tokens per min (TPM): Limit 10000, Used 10000, Requested 1739. Please try again in 10.434s. Visit https://platform.openai.com/account/rate-limits to learn more.
🔄 Analyzing chunk 10/23...
❌ Chunk 10 analysis failed: Rate limit reached for gpt-4 in organization org-OIGqQsD4In0F2R2AeiNDUvI4 on tokens per min (TPM): Limit 10000, Used 10000, Requested 1741. Please try again in 10.446s. Visit https://platform.openai.com/account/rate-limits to learn more.
🔄 Analyzing chunk 11/23...
❌ Chunk 11 analysis failed: Rate limit reached for gpt-4 in organization org-OIGqQsD4In0F2R2AeiNDUvI4 on tokens per min (TPM): Limit 10000, Used 10000, Requested 1739. Please try again in 10.434s. Visit https://platform.openai.com/account/rate-limits to learn more.
🔄 Analyzing chunk 12/23...
❌ Chunk 12 analysis failed: Rate limit reached for gpt-4 in organization org-OIGqQsD4In0F2R2AeiNDUvI4 on tokens per min (TPM): Limit 10000, Used 10000, Requested 1740. Please try again in 10.44s. Visit https://platform.openai.com/account/rate-limits to learn more.
🔄 Analyzing chunk 13/23...
❌ Chunk 13 analysis failed: Rate limit reached for gpt-4 in organization org-OIGqQsD4In0F2R2AeiNDUvI4 on tokens per min (TPM): Limit 10000, Used 10000, Requested 1740. Please try again in 10.44s. Visit https://platform.openai.com/account/rate-limits to learn more.
🔄 Analyzing chunk 14/23...
❌ Chunk 14 analysis failed: Rate limit reached for gpt-4 in organization org-OIGqQsD4In0F2R2AeiNDUvI4 on tokens per min (TPM): Limit 10000, Used 10000, Requested 1738. Please try again in 10.428s. Visit https://platform.openai.com/account/rate-limits to learn more.
🔄 Analyzing chunk 15/23...
❌ Chunk 15 analysis failed: Rate limit reached for gpt-4 in organization org-OIGqQsD4In0F2R2AeiNDUvI4 on tokens per min (TPM): Limit 10000, Used 10000, Requested 1738. Please try again in 10.428s. Visit https://platform.openai.com/account/rate-limits to learn more.
🔄 Analyzing chunk 16/23...
❌ Chunk 16 analysis failed: Rate limit reached for gpt-4 in organization org-OIGqQsD4In0F2R2AeiNDUvI4 on tokens per min (TPM): Limit 10000, Used 9996, Requested 1737. Please try again in 10.398s. Visit https://platform.openai.com/account/rate-limits to learn more.
🔄 Analyzing chunk 17/23...
❌ Chunk 17 analysis failed: Rate limit reached for gpt-4 in organization org-OIGqQsD4In0F2R2AeiNDUvI4 on tokens per min (TPM): Limit 10000, Used 9961, Requested 1738. Please try again in 10.194s. Visit https://platform.openai.com/account/rate-limits to learn more.
🔄 Analyzing chunk 18/23...
❌ Chunk 18 analysis failed: Rate limit reached for gpt-4 in organization org-OIGqQsD4In0F2R2AeiNDUvI4 on tokens per min (TPM): Limit 10000, Used 9928, Requested 1737. Please try again in 9.99s. Visit https://platform.openai.com/account/rate-limits to learn more.
🔄 Analyzing chunk 19/23...
❌ Chunk 19 analysis failed: Rate limit reached for gpt-4 in organization org-OIGqQsD4In0F2R2AeiNDUvI4 on tokens per min (TPM): Limit 10000, Used 9896, Requested 1738. Please try again in 9.804s. Visit https://platform.openai.com/account/rate-limits to learn more.
🔄 Analyzing chunk 20/23...
❌ Chunk 20 analysis failed: Rate limit reached for gpt-4 in organization org-OIGqQsD4In0F2R2AeiNDUvI4 on tokens per min (TPM): Limit 10000, Used 9865, Requested 1737. Please try again in 9.612s. Visit https://platform.openai.com/account/rate-limits to learn more.
🔄 Analyzing chunk 21/23...
❌ Chunk 21 analysis failed: Rate limit reached for gpt-4 in organization org-OIGqQsD4In0F2R2AeiNDUvI4 on tokens per min (TPM): Limit 10000, Used 9832, Requested 1739. Please try again in 9.426s. Visit https://platform.openai.com/account/rate-limits to learn more.
🔄 Analyzing chunk 22/23...
❌ Chunk 22 analysis failed: Rate limit reached for gpt-4 in organization org-OIGqQsD4In0F2R2AeiNDUvI4 on tokens per min (TPM): Limit 10000, Used 9793, Requested 1737. Please try again in 9.18s. Visit https://platform.openai.com/account/rate-limits to learn more.
🔄 Analyzing chunk 23/23...
❌ Chunk 23 analysis failed: Rate limit reached for gpt-4 in organization org-OIGqQsD4In0F2R2AeiNDUvI4 on tokens per min (TPM): Limit 10000, Used 9760, Requested 322. Please try again in 492ms. Visit https://platform.openai.com/account/rate-limits to learn more.
💉 Injecting enhanced contextual annotations...
💉 Injecting visual context into transcript...
✅ Visual context injections prepared: 169 injections
💉 Injecting attire and privacy context annotations...
✅ Attire context annotations prepared: 3529 annotations


------------------------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------------------------


---------------------------------------------------------------------------
KeyError                                  Traceback (most recent call last)
<ipython-input-7-62be731b8a00> in <cell line: 0>()
     13 SKIP_SECONDS = 30 # Adjust based on video
     14 
---> 15 result_file = process_complete_enhanced_forensic_analysis(
     16     video_path,
     17     skip_seconds=SKIP_SECONDS

<ipython-input-6-2ba44e914989> in process_complete_enhanced_forensic_analysis(video_path, skip_seconds)
    372                 f.write(f"   SEVERITY: {violation.get('severity', 'MODERATE')}\n")
    373                 f.write(f"   CONSTITUTIONAL: {violation.get('constitutional_concern', 'General dignity')}\n")
--> 374                 f.write(f"   SPEAKERS: {', '.join(violation['speakers'])}\n\n")
    375 
    376         # Public exposure incidents WITH RESTRAINT STATUS

KeyError: 'speakers'

