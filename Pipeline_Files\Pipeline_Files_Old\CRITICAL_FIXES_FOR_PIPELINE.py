# CRITICAL FIXES FOR LEGAL TRANSCRIPTION PIPELINE
# ================================================
# This file contains ALL necessary fixes for the issues you encountered

# FIX 1: GPT-4 TOKEN LIMIT ISSUE (MOST CRITICAL)
# ==============================================
# Replace your generate_comprehensive_legal_analysis_document function with this chunked version:

def generate_comprehensive_legal_analysis_document_chunked(
    enhanced_transcript, 
    visual_context, 
    comprehensive_analysis, 
    skip_seconds=30,
    max_tokens_per_chunk=8000  # Stay well under 10k limit
):
    """Generate comprehensive legal analysis with token limit handling"""
    print("📚 Generating chunked comprehensive legal analysis document...")
    
    # Prepare transcript text with speaker formatting
    transcript_chunks = []
    current_chunk = []
    current_chunk_tokens = 0
    
    for word_data in enhanced_transcript:
        word_timestamp = word_data['start'] + skip_seconds
        word_text = word_data['word']
        speakers = word_data.get('speakers', [])
        primary_speaker = speakers[0] if speakers else "UNKNOWN"
        timestamp_str = str(timedelta(seconds=int(word_timestamp)))
        
        line = f"[{timestamp_str}] {primary_speaker}: {word_text} "
        # Rough token estimate (1 token ≈ 4 characters)
        line_tokens = len(line) / 4
        
        if current_chunk_tokens + line_tokens > max_tokens_per_chunk:
            transcript_chunks.append(''.join(current_chunk))
            current_chunk = [line]
            current_chunk_tokens = line_tokens
        else:
            current_chunk.append(line)
            current_chunk_tokens += line_tokens
    
    if current_chunk:
        transcript_chunks.append(''.join(current_chunk))
    
    print(f"📄 Split transcript into {len(transcript_chunks)} chunks")
    
    # Process each chunk separately
    chunk_analyses = []
    
    for i, chunk in enumerate(transcript_chunks):
        print(f"🔄 Processing chunk {i+1}/{len(transcript_chunks)}...")
        
        try:
            response = openai.ChatCompletion.create(
                model="gpt-4",
                messages=[
                    {
                        "role": "system",
                        "content": """You are a senior forensic analyst and legal expert specializing in law enforcement interactions. 
                        Analyze this portion of a police body camera transcript for legal violations and procedural issues.
                        Focus on constitutional violations, use of force, mental health handling, and procedural breaches."""
                    },
                    {
                        "role": "user",
                        "content": f"""Analyze this transcript section (Part {i+1} of {len(transcript_chunks)}):

{chunk}

Identify:
1. Constitutional violations (4th, 5th, 8th, 14th Amendment)
2. Procedural violations and policy breaches
3. Use of force concerns
4. Mental health handling issues
5. Privacy and dignity violations
6. Evidence of retaliation or improper conduct

Be specific with timestamps and quotes."""
                    }
                ],
                max_tokens=2000,  # Conservative limit per chunk
                temperature=0.1
            )
            
            chunk_analyses.append(response.choices[0].message.content)
            print(f"✅ Chunk {i+1} analyzed successfully")
            
        except Exception as e:
            print(f"❌ Chunk {i+1} analysis failed: {e}")
            chunk_analyses.append(f"Analysis failed for chunk {i+1}: {str(e)}")
    
    # Combine all analyses
    combined_analysis = "\n\n=== COMBINED LEGAL ANALYSIS ===\n\n"
    for i, analysis in enumerate(chunk_analyses):
        combined_analysis += f"\n--- Section {i+1} Analysis ---\n{analysis}\n"
    
    # If comprehensive_analysis exists, add it
    if comprehensive_analysis:
        combined_analysis += f"\n\n=== COMPREHENSIVE ANALYSIS ===\n{comprehensive_analysis}\n"
    
    return combined_analysis


# FIX 2: KEYERROR FOR 'audio_evidence'
# =====================================
# Replace all instances of direct dictionary access with .get() method:

# Find this line:
# f.write(f"   AUDIO: {violation['audio_evidence']}\n")
# Replace with:
# f.write(f"   AUDIO: {violation.get('audio_evidence', 'N/A')}\n")

# Similarly for all other dictionary accesses:
# violation['visual_evidence'] → violation.get('visual_evidence', 'N/A')
# violation['severity'] → violation.get('severity', 'Unknown')
# etc.


# FIX 3: TENSORLOAT-32 WARNING
# =============================
# Add this at the beginning of Cell 4 after imports:

import torch
# Suppress TF32 warning
torch.backends.cuda.matmul.allow_tf32 = True
torch.backends.cudnn.allow_tf32 = True


# FIX 4: FRAME ANALYSIS CACHING
# ==============================
# Replace your analyze_video_frames_for_context_enhanced_attire function:

def analyze_video_frames_for_context_enhanced_attire_cached(video_path, skip_seconds=30):
    """Enhanced video analysis with caching to avoid duplicate API calls"""
    print("📹 Analyzing video frames with caching...")
    
    # Create cache directory
    cache_dir = "/content/frame_analysis_cache"
    os.makedirs(cache_dir, exist_ok=True)
    
    # Generate unique cache key
    import hashlib
    video_name = os.path.basename(video_path)
    cache_key = hashlib.md5(f"{video_name}_{skip_seconds}".encode()).hexdigest()
    cache_file = os.path.join(cache_dir, f"{cache_key}_analysis.json")
    
    # Check if cached analysis exists
    if os.path.exists(cache_file):
        print("📂 Found cached frame analysis - loading from cache...")
        import json
        with open(cache_file, 'r') as f:
            visual_context = json.load(f)
        print(f"✅ Loaded {len(visual_context)} cached frame analyses")
        return visual_context
    
    print("🆕 No cache found - performing new analysis...")
    
    # Your existing frame analysis code here...
    frames_dir = "/content/video_frames"
    os.makedirs(frames_dir, exist_ok=True)
    
    # Extract frames
    extract_frames_cmd = [
        'ffmpeg', '-y', '-ss', str(skip_seconds), '-i', video_path,
        '-vf', 'fps=1/20', '-q:v', '2', f'{frames_dir}/frame_%04d.jpg'
    ]
    subprocess.run(extract_frames_cmd, capture_output=True)
    
    frame_files = sorted([f for f in os.listdir(frames_dir) if f.endswith('.jpg')])
    visual_context = []
    
    for i, frame_file in enumerate(frame_files):
        frame_path = os.path.join(frames_dir, frame_file)
        timestamp = (i * 20) + skip_seconds
        
        try:
            with open(frame_path, 'rb') as f:
                frame_data = base64.b64encode(f.read()).decode()
            
            response = openai.ChatCompletion.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "Your existing prompt here..."
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{frame_data}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=600,
                temperature=0.1
            )
            
            visual_analysis = response.choices[0].message.content
            visual_context.append({
                'timestamp': timestamp,
                'frame': frame_file,
                'analysis': visual_analysis
            })
            
            print(f"✅ Frame analyzed: {timestamp//60:02d}:{timestamp%60:02d}")
            
        except Exception as e:
            print(f"⚠️ Frame analysis failed for {frame_file}: {e}")
            visual_context.append({
                'timestamp': timestamp,
                'frame': frame_file,
                'analysis': f"Analysis unavailable: {str(e)}"
            })
    
    # Save to cache
    print(f"💾 Saving frame analysis to cache...")
    import json
    with open(cache_file, 'w') as f:
        json.dump(visual_context, f, indent=2)
    print(f"✅ Cached {len(visual_context)} frame analyses")
    
    return visual_context


# FIX 5: EMERGENCY TRANSCRIPT RECOVERY
# ====================================
# Add this as a new cell to run when you need to recover transcript:

def emergency_transcript_recovery():
    """Emergency recovery of transcript data from memory or partial files"""
    import os
    import json
    from datetime import datetime, timedelta
    from google.colab import files
    
    print("🔍 Attempting emergency transcript recovery...")
    
    emergency_path = "/content/EMERGENCY_TRANSCRIPT_RECOVERY.txt"
    
    try:
        # Try to access transcript from globals
        if 'enhanced_transcript' in globals():
            with open(emergency_path, "w", encoding="utf-8") as f:
                f.write("EMERGENCY TRANSCRIPT RECOVERY\n")
                f.write("="*50 + "\n\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Total words: {len(enhanced_transcript)}\n\n")
                
                current_speaker = None
                skip_seconds = 30
                
                for word_data in enhanced_transcript:
                    word_timestamp = word_data['start'] + skip_seconds
                    word_text = word_data['word']
                    speakers = word_data.get('speakers', [])
                    
                    primary_speaker = speakers[0] if speakers else "UNKNOWN"
                    timestamp_str = str(timedelta(seconds=int(word_timestamp)))
                    
                    if primary_speaker != current_speaker:
                        f.write(f"\n[{timestamp_str}] {primary_speaker}: ")
                        current_speaker = primary_speaker
                    f.write(f"{word_text} ")
            
            print("✅ Transcript recovered and saved!")
            files.download(emergency_path)
            return True
            
    except Exception as e:
        print(f"❌ Recovery failed: {e}")
        return False


# FIX 6: EARLY TRANSCRIPT DOWNLOAD
# ================================
# Add this right after transcript creation in your main processing function:

def save_early_transcript(enhanced_transcript, video_path, skip_seconds=30):
    """Save transcript immediately after creation for early download"""
    from datetime import datetime, timedelta
    from google.colab import files
    
    early_path = "/content/EARLY_TRANSCRIPT_ONLY.txt"
    
    with open(early_path, "w", encoding="utf-8") as f:
        f.write("EARLY TRANSCRIPT - SPEAKER IDENTIFIED VERSION\n")
        f.write("="*50 + "\n\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Video: {video_path}\n")
        f.write(f"Total words: {len(enhanced_transcript)}\n\n")
        
        current_speaker = None
        
        for word_data in enhanced_transcript:
            word_timestamp = word_data['start'] + skip_seconds
            word_text = word_data['word']
            speakers = word_data.get('speakers', [])
            
            primary_speaker = speakers[0] if speakers else "UNKNOWN"
            timestamp_str = str(timedelta(seconds=int(word_timestamp)))
            
            if primary_speaker != current_speaker:
                f.write(f"\n[{timestamp_str}] {primary_speaker}: ")
                current_speaker = primary_speaker
            f.write(f"{word_text} ")
        
        f.write("\n\n[END OF TRANSCRIPT]")
    
    print("\n📥 DOWNLOADING EARLY TRANSCRIPT...")
    files.download(early_path)
    print("✅ Early transcript downloaded! Analysis continuing...\n")


# FIX 7: MAIN PROCESSING FUNCTION UPDATE
# ======================================
# Update your main processing function to use these fixes:

def process_complete_enhanced_forensic_analysis_fixed(video_path, skip_seconds=30):
    """Fixed version with all error handling and optimizations"""
    
    # Your existing setup code...
    
    # After creating enhanced_transcript:
    enhanced_transcript = combine_transcription_and_speakers_enhanced(
        whisper_result, diarization_result, overlaps
    )
    
    # SAVE EARLY TRANSCRIPT
    save_early_transcript(enhanced_transcript, video_path, skip_seconds)
    
    # Use cached frame analysis
    visual_context = analyze_video_frames_for_context_enhanced_attire_cached(
        video_path, skip_seconds
    )
    
    # Your other analysis code...
    
    # Use chunked analysis for GPT-4
    comprehensive_analysis = generate_comprehensive_legal_analysis_document_chunked(
        enhanced_transcript, 
        visual_context, 
        None,  # or your existing analysis if available
        skip_seconds
    )
    
    # Continue with rest of processing...


# IMPLEMENTATION INSTRUCTIONS:
# ===========================
# 1. Add the TensorFloat fix to the beginning of Cell 4
# 2. Replace your frame analysis function with the cached version
# 3. Replace your comprehensive analysis function with the chunked version
# 4. Update all dictionary accesses to use .get() method
# 5. Add the emergency recovery function as a new cell
# 6. Update your main processing function to save early transcript

print("✅ All fixes loaded. Update your pipeline with these functions!")