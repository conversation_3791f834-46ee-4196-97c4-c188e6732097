<IPython.core.display.Javascript object>

<IPython.core.display.Javascript object>


config.json:   0%|          | 0.00/829 [00:00<?, ?B/s]

model.safetensors:   0%|          | 0.00/433M [00:00<?, ?B/s]

Some weights of the model checkpoint at dslim/bert-base-NER were not used when initializing BertForTokenClassification: ['bert.pooler.dense.bias', 'bert.pooler.dense.weight']
- This IS expected if you are initializing BertForTokenClassification from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing BertForTokenClassification from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).

tokenizer_config.json:   0%|          | 0.00/59.0 [00:00<?, ?B/s]


vocab.txt:   0%|          | 0.00/213k [00:00<?, ?B/s]


added_tokens.json:   0%|          | 0.00/2.00 [00:00<?, ?B/s]


special_tokens_map.json:   0%|          | 0.00/112 [00:00<?, ?B/s]



