# PART 3: Main Processing Function and Frame Reuse

# =============================================================================
# Cell 5: Load Speaker Diarization Pipeline
# =============================================================================
print("👥 Loading enhanced speaker diarization pipeline...")

try:
    diarization_pipeline = Pipeline.from_pretrained(
        "pyannote/speaker-diarization-3.1",
        use_auth_token=HF_TOKEN
    )
    diarization_pipeline.to(torch.device(device))
    print("✅ Speaker diarization pipeline loaded successfully!")
except Exception as e:
    print(f"❌ Failed to load speaker diarization: {e}")
    print("Please check your HuggingFace token permissions")

# =============================================================================
# Cell 6: Complete Enhanced Forensic Processing Function
# =============================================================================
def process_complete_enhanced_forensic_analysis(video_path, skip_seconds=30):
    """
    Complete forensic pipeline with all fixes:
    - Progressive downloads throughout
    - Rate limit handling with 20s delays
    - Proper visual context injection
    - Clear forensic-grade instructions
    """
    import os
    import json
    import hashlib
    from datetime import datetime, timedelta
    from google.colab import files
    
    print("🏛️ ENHANCED CERTIFIED FORENSIC AUDIOVISUAL ANALYSIS")
    print("="*80)
    print(f"Video: {video_path}")
    print(f"Skip seconds: {skip_seconds}")
    print("="*80 + "\n")
    
    # Step 1: Extract and enhance audio
    print("🎵 Step 1: Extracting and enhancing audio...")
    audio_raw = "/content/extracted_audio_raw.wav"
    audio_enhanced = "/content/enhanced_forensic_audio_v2.wav"
    
    extract_cmd = [
        'ffmpeg', '-y', '-ss', str(skip_seconds), '-i', video_path,
        '-acodec', 'pcm_s16le', '-ar', '16000', '-ac', '1', audio_raw
    ]
    subprocess.run(extract_cmd, capture_output=True)
    
    enhanced_audio_processing_for_difficult_sections(audio_raw, audio_enhanced)
    
    # Step 2: Transcribe with Whisper
    print("\n📝 Step 2: Transcribing with Whisper Large-v3...")
    whisper_result = transcribe_with_maximum_accuracy_enhanced(audio_enhanced)
    print(f"✅ Transcription complete: {len(whisper_result['segments'])} segments")
    
    # Save raw whisper result
    whisper_path = "/content/whisper_transcription.json"
    with open(whisper_path, 'w') as f:
        json.dump(whisper_result, f, indent=2)
    print("📥 Downloading raw transcription...")
    files.download(whisper_path)
    
    # Step 3: Speaker diarization
    print("\n👥 Step 3: Performing speaker diarization...")
    diarization_result = diarization_pipeline(audio_enhanced)
    
    # Step 4: Detect overlaps
    overlaps = detect_speaker_overlaps_and_separate_enhanced(
        audio_enhanced, diarization_result, whisper_result
    )
    
    # Step 5: Combine transcription with speakers
    print("\n🔗 Step 5: Combining transcription with speaker identification...")
    enhanced_transcript = combine_transcription_and_speakers_enhanced(
        whisper_result, diarization_result, overlaps
    )
    
    # CRITICAL: Save early transcript for immediate download
    print("\n💾 Saving speaker-identified transcript...")
    early_transcript_path = "/content/SPEAKER_IDENTIFIED_TRANSCRIPT.txt"
    
    with open(early_transcript_path, "w", encoding="utf-8") as f:
        f.write("SPEAKER-IDENTIFIED FORENSIC TRANSCRIPT\n")
        f.write("="*60 + "\n\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Video: {os.path.basename(video_path)}\n")
        f.write(f"Skip seconds: {skip_seconds}\n")
        f.write(f"Total words: {len(enhanced_transcript)}\n\n")
        
        f.write("TRANSCRIPT:\n")
        f.write("-"*60 + "\n\n")
        
        current_speaker = None
        for word_data in enhanced_transcript:
            word_timestamp = word_data['start'] + skip_seconds
            word_text = word_data['word']
            speakers = word_data.get('speakers', [])
            confidence = word_data.get('confidence', 0.0)
            
            primary_speaker = speakers[0] if speakers else "UNKNOWN"
            timestamp_str = str(timedelta(seconds=int(word_timestamp)))
            
            if primary_speaker != current_speaker:
                f.write(f"\n[{timestamp_str}] {primary_speaker}: ")
                current_speaker = primary_speaker
            
            if confidence < 0.7:
                f.write(f"[{word_text}?] ")
            else:
                f.write(f"{word_text} ")
    
    print("📥 Downloading speaker-identified transcript...")
    files.download(early_transcript_path)
    
    # Step 6: Analyze video frames
    print("\n🎥 Step 6: Analyzing video frames...")
    visual_context = analyze_video_frames_for_context_enhanced_attire(
        video_path, skip_seconds
    )
    
    # Step 7: Inject visual context
    print("\n💉 Step 7: Creating visual context injections...")
    visual_injections = inject_visual_context_into_transcript(
        enhanced_transcript, visual_context, skip_seconds
    )
    
    # Step 8: Create transcript chunks
    print("\n📄 Step 8: Creating transcript chunks for analysis...")
    chunks = process_transcript_chunks_with_rate_limiting(
        enhanced_transcript, skip_seconds
    )
    
    # Step 9: Analyze violations (quick scan for summary)
    print("\n🔍 Step 9: Quick violation scan...")
    violations_summary = {
        'privacy': 0,
        'dignity': 0,
        'force': 0,
        'procedure': 0
    }
    
    # Quick scan for violations
    for word_data in enhanced_transcript:
        word_lower = word_data['word'].lower()
        if any(term in word_lower for term in ['towel', 'naked', 'undressed']):
            violations_summary['privacy'] += 1
        if any(term in word_lower for term in ['cuff', 'handcuff', 'restrain']):
            violations_summary['force'] += 1
    
    # Step 10: Analyze chunks with GPT-4
    print("\n⚖️ Step 10: Performing legal analysis with rate limiting...")
    chunk_analyses = analyze_chunks_with_gpt4(chunks, violations_summary)
    
    # Step 11: Generate final comprehensive document
    print("\n📝 Step 11: Generating comprehensive forensic document...")
    
    output_path = "/content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt"
    
    with open(output_path, "w", encoding="utf-8") as f:
        # Header
        f.write("COMPREHENSIVE FORENSIC LEGAL ANALYSIS\n")
        f.write("="*80 + "\n\n")
        f.write("FORENSIC-GRADE ANALYSIS - HIGHEST STANDARDS OF ACCURACY\n")
        f.write("This document applies forensic-grade quality standards\n")
        f.write("(maximum rigor and precision) to the analysis\n\n")
        
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Video: {os.path.basename(video_path)}\n")
        f.write(f"Duration Analyzed: {len(enhanced_transcript)} words\n")
        f.write(f"Frames Analyzed: {len(visual_context)}\n\n")
        
        # Executive Summary
        f.write("EXECUTIVE SUMMARY:\n")
        f.write("="*40 + "\n")
        f.write(f"Total Chunks Analyzed: {len(chunk_analyses)}\n")
        f.write(f"Visual Context Injections: {len(visual_injections)}\n")
        f.write(f"Speaker Overlaps Detected: {len(overlaps)}\n\n")
        
        # Legal Analysis
        f.write("LEGAL ANALYSIS BY CHUNK:\n")
        f.write("="*40 + "\n\n")
        
        for i, analysis in enumerate(chunk_analyses):
            f.write(f"--- Chunk {i+1} of {len(chunks)} ---\n")
            f.write(analysis)
            f.write("\n\n")
        
        # Visual Context Summary
        f.write("VISUAL CONTEXT SUMMARY:\n")
        f.write("="*40 + "\n\n")
        
        for i, ctx in enumerate(visual_context[:10]):  # First 10 frames
            timestamp = ctx['timestamp']
            f.write(f"[{timestamp//60:02d}:{timestamp%60:02d}] Frame {ctx['frame']}:\n")
            f.write(f"{ctx['analysis'][:300]}...\n\n")
        
        # Full Transcript with Visual Injections
        f.write("\nFULL TRANSCRIPT WITH VISUAL CONTEXT:\n")
        f.write("="*40 + "\n\n")
        
        current_speaker = None
        for i, word_data in enumerate(enhanced_transcript):
            word_timestamp = word_data['start'] + skip_seconds
            word_text = word_data['word']
            speakers = word_data.get('speakers', [])
            primary_speaker = speakers[0] if speakers else "UNKNOWN"
            timestamp_str = str(timedelta(seconds=int(word_timestamp)))
            
            # Inject visual context if available
            if i in visual_injections:
                f.write(visual_injections[i] + "\n")
            
            # Write transcript
            if primary_speaker != current_speaker:
                f.write(f"\n[{timestamp_str}] {primary_speaker}: ")
                current_speaker = primary_speaker
            f.write(f"{word_text} ")
        
        f.write("\n\n[END OF ANALYSIS]")
    
    print(f"\n📥 Downloading comprehensive analysis...")
    files.download(output_path)
    
    print("\n✅ FORENSIC ANALYSIS COMPLETE!")
    print(f"Downloaded files:")
    print("- Whisper transcription (JSON)")
    print("- Speaker-identified transcript (TXT)")
    print("- Visual frame analyses (JSON)")
    print("- Frame batches (ZIP files)")
    print("- Transcript chunks (JSON)")
    print("- Chunk analyses (JSON)")
    print("- Comprehensive analysis (TXT)")
    
    return output_path

print("✅ Main processing function ready!")

# =============================================================================
# Cell 7: Execute Analysis
# =============================================================================
print("🚀 EXECUTING ENHANCED COMPLETE FORENSIC ANALYSIS...")

video_path = f"/content/{video_filename}"  # Uses filename from Cell 2
SKIP_SECONDS = 30  # Adjust based on video

result_file = process_complete_enhanced_forensic_analysis(
    video_path,
    skip_seconds=SKIP_SECONDS
)

# =============================================================================
# Cell 8: SPECIAL - Reuse Existing Frames (For Current Video Only)
# =============================================================================
"""
SPECIAL CELL FOR CURRENT VIDEO ONLY
Use this to upload and reuse your existing 177 frames
to avoid duplicate frame extraction and API calls
"""

def reuse_existing_frames_for_analysis(frames_zip_path, skip_seconds=30):
    """
    Special function to reuse existing frames you already have
    """
    print("🔄 REUSING EXISTING FRAMES - SPECIAL MODE")
    print("="*60)
    
    # Extract uploaded frames
    frames_dir = "/content/video_frames_reused"
    os.makedirs(frames_dir, exist_ok=True)
    
    print(f"📦 Extracting frames from: {frames_zip_path}")
    
    import zipfile
    with zipfile.ZipFile(frames_zip_path, 'r') as zip_ref:
        zip_ref.extractall(frames_dir)
    
    # Get frame files
    frame_files = sorted([f for f in os.listdir(frames_dir) if f.endswith('.jpg')])
    print(f"✅ Found {len(frame_files)} existing frames")
    
    # Check if we have cached analysis
    cache_file = "/content/frame_analysis_cache/existing_frames_analysis.json"
    
    if os.path.exists(cache_file):
        print("📂 Found cached analysis for these frames!")
        with open(cache_file, 'r') as f:
            visual_context = json.load(f)
        print(f"✅ Loaded {len(visual_context)} cached analyses")
    else:
        print("🔍 No cache found - analyzing frames with forensic-grade standards...")
        
        visual_context = []
        
        for i, frame_file in enumerate(frame_files):
            frame_path = os.path.join(frames_dir, frame_file)
            actual_timestamp = (i * 20) + skip_seconds
            
            try:
                with open(frame_path, 'rb') as f:
                    frame_data = base64.b64encode(f.read()).decode()
                
                # Use same forensic-grade prompt
                response = openai.ChatCompletion.create(
                    model="gpt-4o",
                    messages=[
                        {
                            "role": "system",
                            "content": "You are performing FORENSIC-GRADE analysis. This means highest standards of detail and accuracy."
                        },
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": "Perform FORENSIC-GRADE analysis of this frame. Focus on clothing status, restraints, officer positions, and dignity concerns."
                                },
                                {
                                    "type": "image_url",
                                    "image_url": {"url": f"data:image/jpeg;base64,{frame_data}"}
                                }
                            ]
                        }
                    ],
                    max_tokens=800,
                    temperature=0.1
                )
                
                visual_context.append({
                    'timestamp': actual_timestamp,
                    'frame': frame_file,
                    'analysis': response.choices[0].message.content
                })
                
                print(f"✅ Analyzed frame {i+1}/{len(frame_files)}")
                time.sleep(1)  # Rate limit protection
                
            except Exception as e:
                print(f"❌ Frame {frame_file} failed: {e}")
                visual_context.append({
                    'timestamp': actual_timestamp,
                    'frame': frame_file,
                    'analysis': f"Analysis failed: {str(e)}"
                })
        
        # Save cache
        os.makedirs("/content/frame_analysis_cache", exist_ok=True)
        with open(cache_file, 'w') as f:
            json.dump(visual_context, f, indent=2)
        print("💾 Saved analysis to cache")
    
    # Download the analysis
    files.download(cache_file)
    print("✅ Frame analysis complete!")
    
    return visual_context

# To use this special function:
# 1. Upload your frames zip file to Colab
# 2. Run: visual_context = reuse_existing_frames_for_analysis("/content/your_frames.zip", skip_seconds=30)

print("✅ Complete pipeline ready with all fixes!")