# FORENSIC-<PERSON><PERSON>DE EXTERNAL ANALYSIS SYSTEM
# ========================================
# This provides the FULL context and sophisticated prompts needed for superior analysis

import os
from datetime import datetime
from google.colab import files

def create_forensic_analysis_package():
    """
    Create a comprehensive analysis package with full context and sophisticated prompts
    """
    
    print("🔬 CREATING FORENSIC-GRADE EXTERNAL ANALYSIS PACKAGE...\n")
    
    # First, let's gather ALL available context
    print("📊 Gathering comprehensive context...")
    
    # Read all available files to build context
    context_data = {
        'transcript': None,
        'visual_analysis': None,
        'violations_found': None,
        'case_summary': None
    }
    
    # Read the comprehensive analysis file
    if os.path.exists("/content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt"):
        with open("/content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt", "r") as f:
            analysis_content = f.read()
            
            # Extract successful analyses from chunks 1-6
            if "Section 1 Analysis" in analysis_content:
                context_data['prior_analysis'] = analysis_content
            
            # Extract violation counts
            if "EXECUTIVE SUMMARY" in analysis_content:
                summary_section = analysis_content.split("EXECUTIVE SUMMARY")[1].split("DETAILED VIOLATION")[0]
                context_data['violations_found'] = summary_section
    
    # Read the early transcript for full context
    if os.path.exists("/content/EARLY_TRANSCRIPT_ONLY.txt"):
        with open("/content/EARLY_TRANSCRIPT_ONLY.txt", "r") as f:
            context_data['transcript'] = f.read()
    
    print("✅ Context gathered\n")
    
    # Create the comprehensive analysis package
    output_path = "/content/FORENSIC_EXTERNAL_ANALYSIS_PACKAGE.txt"
    
    with open(output_path, "w", encoding="utf-8") as f:
        f.write("FORENSIC-GRADE EXTERNAL ANALYSIS PACKAGE\n")
        f.write("="*80 + "\n\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # SECTION 1: COMPREHENSIVE CASE CONTEXT
        f.write("SECTION 1: COMPREHENSIVE CASE CONTEXT\n")
        f.write("="*60 + "\n\n")
        
        f.write("CASE OVERVIEW:\n")
        f.write("-"*40 + "\n")
        f.write("""
This is a 59-minute police body camera video documenting a mental health response that escalated 
into a potential civil rights violation case. Key contextual elements:

1. SUBJECT PROFILE:
   - Mental health crisis (Baker Act situation)
   - Reportedly cooperative throughout
   - In state of undress (towel only) when law enforcement arrived
   - Appears to have been interrupted during shower/private activity
   - Female subject in vulnerable state

2. INCIDENT PROGRESSION:
   - Initial response for welfare check
   - Multiple officers responded (escalation)
   - Subject handcuffed despite cooperation
   - Public exposure concerns (neighbors present)
   - Transportation in minimal clothing
   - Multiple references to humiliation/dignity violations

3. LEGAL FRAMEWORK:
   - Florida Baker Act (Fla. Stat. § 394.463) requirements
   - Constitutional protections (4th, 5th, 8th, 14th Amendments)
   - Case law: Graham v. Connor, Tennessee v. Garner, York v. Story
   - Department policies on mental health response
   - Dignity and privacy protections during detention

4. KEY PATTERNS IDENTIFIED IN INITIAL ANALYSIS:
""")
        
        if context_data.get('violations_found'):
            f.write(context_data['violations_found'][:1000])  # First 1000 chars of violations
        
        f.write("\n\n")
        
        # SECTION 2: FORENSIC ANALYSIS FRAMEWORK
        f.write("SECTION 2: FORENSIC ANALYSIS FRAMEWORK\n")
        f.write("="*60 + "\n\n")
        
        f.write("MASTER ANALYSIS PROMPT FOR EACH CHUNK:\n")
        f.write("-"*40 + "\n")
        f.write("""
You are a certified forensic audiovisual analyst with 25+ years experience in criminal procedure, 
constitutional law (42 U.S.C. § 1983), and court-appointed expert testimony. You are analyzing 
section [X] of 23 from a 59-minute police body camera video.

CRITICAL CASE CONTEXT:
- Female subject in mental health crisis
- Subject was wearing only a towel (interrupted during shower)
- Multiple officers responded to single cooperative individual
- Handcuffing applied despite cooperation and state of undress
- Public exposure with neighbors present
- Escalation tactics used (SWAT mentioned, weapons displayed)

YOUR FORENSIC ANALYSIS MUST IDENTIFY:

1. CONSTITUTIONAL VIOLATIONS:
   A. 4th Amendment - Unreasonable Search and Seizure
      - Warrantless home entry justification
      - Scope of search exceeding exigent circumstances
      - Privacy expectations in home
      - Seizure reasonableness for mental health

   B. 5th Amendment - Due Process
      - Miranda requirements and timing
      - Coercive interrogation environment
      - Self-incrimination while in crisis

   C. 8th Amendment - Cruel and Unusual Treatment
      - Dignity violations during detention
      - Unnecessary humiliation (towel/nakedness)
      - Disproportionate restraints for mental health
      - Deliberate indifference to basic human dignity

   D. 14th Amendment - Equal Protection and Due Process
      - Discriminatory treatment
      - Substantive due process violations
      - Procedural due process in Baker Act application

2. STATUTORY VIOLATIONS:
   A. Florida Baker Act (§ 394.463)
      - Criteria for involuntary examination NOT met
      - Failure to attempt voluntary compliance
      - Improper transportation methods
      - Excessive restraint for mental health transport
      - Failure to protect dignity during transport

   B. Department Policies
      - Crisis intervention team protocols
      - De-escalation requirements
      - Mental health response procedures
      - Body camera policies (muting references)

3. SPECIFIC FORENSIC MARKERS TO IDENTIFY:

   A. Dignity and Privacy Violations
      - Exact quotes about towel/clothing/nakedness
      - Officer acknowledgment of undress state
      - Failure to provide clothing/covering
      - Public exposure duration and witnesses
      - Humiliation tactics or statements

   B. Force and Restraint Analysis
      - Restraint application on minimally clothed person
      - Justification for handcuffs on cooperative subject
      - Force escalation despite compliance
      - Weapon display to person in crisis

   C. Mental Health Response Failures
      - Failure to follow CIT protocols
      - Escalation instead of de-escalation
      - Treating medical crisis as criminal
      - Ignoring subject's stated needs

   D. Evidence of Misconduct
      - Retaliatory statements or actions
      - Cover-up discussions
      - Camera manipulation references
      - Coordination of stories
      - Punitive rather than protective actions

4. PATTERN RECOGNITION ACROSS CHUNKS:
   [Note: This is chunk X of 23. Previous chunks showed: (summarize)]
   [Watch for continuation or escalation of these patterns]

5. FORENSIC DOCUMENTATION REQUIREMENTS:
   - Exact timestamp for each violation
   - Verbatim quotes (no paraphrasing)
   - Speaker identification
   - Context before/after statement
   - Correlation with visual evidence mentions
   - Severity assessment (1-10 scale)

6. LEGAL CASE CITATIONS TO APPLY:
   - Graham v. Connor (1989) - Objective reasonableness
   - Tennessee v. Garner (1985) - Force standards
   - York v. Story (1963) - Dignity/privacy violations
   - Youngberg v. Romeo (1982) - Mental health detainee rights
   - Estate of Corey Hill v. Miracle (2017) - Taser on mental health subject
   - Roell v. Hamilton County (2012) - Excessive force in mental health response

ANALYZE THIS TRANSCRIPT SECTION:
[Insert chunk here]

REQUIRED OUTPUT FORMAT:
1. Violations identified with exact quotes and timestamps
2. Constitutional/statutory basis for each violation
3. Severity score and justification
4. Connection to overall pattern of misconduct
5. Specific evidence for civil litigation
6. Criminal law violations by officers (if any)
""")
        
        f.write("\n\n")
        
        # SECTION 3: CHUNK INTERCONNECTION GUIDE
        f.write("SECTION 3: CHUNK INTERCONNECTION GUIDE\n")
        f.write("="*60 + "\n\n")
        
        f.write("HOW TO MAINTAIN CONTEXT ACROSS CHUNKS:\n")
        f.write("-"*40 + "\n")
        f.write("""
For EACH chunk analysis, include this context bridge:

"CONTEXT FROM PREVIOUS CHUNKS:
- Chunk 1: [Key events/violations]
- Chunk 2: [Key events/violations]
- [Continue for each analyzed chunk]

ANALYZING CHUNK X WITH THIS CONTEXT IN MIND:"

This ensures each analysis builds on previous findings rather than analyzing in isolation.
""")
        
        # SECTION 4: QUALITY CONTROL CHECKLIST
        f.write("\n\nSECTION 4: FORENSIC QUALITY CONTROL CHECKLIST\n")
        f.write("="*60 + "\n\n")
        
        f.write("FOR EACH CHUNK, VERIFY:\n")
        f.write("""
□ All speakers identified by title/badge/name
□ All timestamps included for violations
□ Exact quotes provided (no paraphrasing)
□ Constitutional basis cited for each violation
□ Severity scored 1-10 with justification
□ Pattern connections to other chunks noted
□ Visual evidence correlated where mentioned
□ Criminal statutes violated identified
□ Department policy violations noted
□ Evidence preservation recommendations made
□ Litigation value assessed
□ Officer intent/state of mind analyzed
□ Victim impact documented
□ Witness identification completed
□ Cover-up attempts flagged
""")
        
        # SECTION 5: CHUNKS WITH CONTEXT
        f.write("\n\nSECTION 5: TRANSCRIPT CHUNKS WITH ENHANCED CONTEXT\n")
        f.write("="*60 + "\n\n")
        
        # Now include the actual chunks but with context markers
        # First, read the chunks
        if os.path.exists("/content/ALL_23_CHUNKS_EXPORT.txt"):
            with open("/content/ALL_23_CHUNKS_EXPORT.txt", "r") as chunk_file:
                chunk_content = chunk_file.read()
                
                # Extract just the chunks section
                if "CHUNK 1 of 23" in chunk_content:
                    chunks_section = chunk_content.split("CHUNK 1 of 23")[1]
                    f.write("CHUNK 1 of 23")
                    f.write(chunks_section)
    
    print("📥 Downloading forensic analysis package...")
    files.download(output_path)
    
    # Create a tracking spreadsheet template
    tracking_path = "/content/ANALYSIS_TRACKING_SHEET.txt"
    
    with open(tracking_path, "w") as f:
        f.write("FORENSIC ANALYSIS TRACKING SHEET\n")
        f.write("="*60 + "\n\n")
        f.write("CHUNK | STATUS | KEY VIOLATIONS FOUND | SEVERITY | NOTES\n")
        f.write("-"*60 + "\n")
        
        for i in range(1, 24):
            f.write(f"{i:3d}   | [ ]    |                      |          |\n")
    
    files.download(tracking_path)
    
    print("\n✅ FORENSIC PACKAGE COMPLETE!")
    print("\n📋 You now have:")
    print("1. Complete forensic analysis framework")
    print("2. Sophisticated prompts matching pipeline quality")
    print("3. Full case context for each chunk")
    print("4. Pattern recognition guidance")
    print("5. Quality control checklist")
    print("6. Tracking sheet for progress")
    
    print("\n🎯 This approach will produce SUPERIOR results because:")
    print("- Full context provided for each chunk")
    print("- Forensic-grade analysis framework")
    print("- Pattern recognition across chunks")
    print("- Interactive ability to ask follow-ups")
    print("- Multiple AI perspectives possible")

# Run the forensic package creation
create_forensic_analysis_package()