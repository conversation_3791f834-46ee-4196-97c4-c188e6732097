# UNIVERSAL CHUNK EXPORT TOOL FOR ANY VIDEO
# =========================================
# This tool will work with any video transcript to create and export ALL chunks

import os
import re
from datetime import datetime, timedelta
from google.colab import files

def universal_chunk_export_tool():
    """
    Comprehensive tool to extract transcript from saved files and export ALL chunks
    Works with any video length and automatically handles all chunks
    """
    
    print("🔧 UNIVERSAL CHUNK EXPORT TOOL")
    print("="*60)
    print("This tool will extract and export ALL chunks for external analysis\n")
    
    # Step 1: Find and read transcript
    print("📄 Step 1: Looking for transcript files...")
    
    transcript_files = [
        "/content/EARLY_TRANSCRIPT_ONLY.txt",
        "/content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt",
        "/content/CERTIFIED_FORENSIC_LEGAL_TRANSCRIPT.txt"
    ]
    
    transcript_content = None
    source_file = None
    
    for file_path in transcript_files:
        if os.path.exists(file_path):
            print(f"✅ Found: {file_path}")
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
                # Check if this file contains a transcript
                if "TRANSCRIPT" in content and ("[" in content or "SPEAKER" in content):
                    transcript_content = content
                    source_file = file_path
                    break
    
    if not transcript_content:
        print("❌ No transcript file found!")
        return False
    
    print(f"✅ Using transcript from: {source_file}")
    
    # Step 2: Extract video info and transcript text
    print("\n📊 Step 2: Extracting transcript information...")
    
    # Extract video filename if available
    video_name = "Unknown"
    if "Video:" in transcript_content:
        video_match = re.search(r'Video:\s*(.+?)\n', transcript_content)
        if video_match:
            video_name = video_match.group(1).strip()
    
    # Extract skip seconds if available
    skip_seconds = 30  # default
    if "Skip seconds:" in transcript_content:
        skip_match = re.search(r'Skip seconds:\s*(\d+)', transcript_content)
        if skip_match:
            skip_seconds = int(skip_match.group(1))
    
    # Extract total words if available
    total_words = "Unknown"
    if "Total words:" in transcript_content:
        words_match = re.search(r'Total words:\s*(\d+)', transcript_content)
        if words_match:
            total_words = words_match.group(1)
    
    print(f"📹 Video: {video_name}")
    print(f"⏱️ Skip seconds: {skip_seconds}")
    print(f"📝 Total words: {total_words}")
    
    # Step 3: Extract the actual transcript
    print("\n✂️ Step 3: Extracting and chunking transcript...")
    
    # Find the transcript section
    lines = transcript_content.split('\n')
    transcript_lines = []
    in_transcript = False
    
    # Look for various transcript start markers
    transcript_markers = [
        "FULL TRANSCRIPT",
        "TRANSCRIPT WITH SPEAKER",
        "ANNOTATED TRANSCRIPT",
        "SPEAKER-IDENTIFIED TRANSCRIPT"
    ]
    
    for line in lines:
        # Check if we're entering the transcript section
        if any(marker in line.upper() for marker in transcript_markers):
            in_transcript = True
            continue
        
        # Check if we've reached the end
        if in_transcript and any(end in line for end in ["[END OF TRANSCRIPT]", "===", "LEGAL ANALYSIS", "CERTIFICATION"]):
            break
        
        # Collect transcript lines
        if in_transcript and line.strip():
            # Only include lines that look like transcript (have timestamps or speaker labels)
            if re.match(r'^\[[\d:]+\]', line) or 'SPEAKER' in line or ': ' in line:
                transcript_lines.append(line)
    
    if not transcript_lines:
        print("⚠️ No transcript lines found. Attempting alternative extraction...")
        # Fallback: look for any lines with timestamp format
        for line in lines:
            if re.match(r'^\[[\d:]+\]', line):
                transcript_lines.append(line)
    
    print(f"✅ Extracted {len(transcript_lines)} transcript lines")
    
    # Step 4: Create chunks
    chunks = []
    current_chunk = []
    current_size = 0
    max_chunk_size = 5000  # Characters per chunk
    
    for line in transcript_lines:
        line_size = len(line)
        
        # Start new chunk if size exceeded
        if current_size + line_size > max_chunk_size and current_chunk:
            chunks.append('\n'.join(current_chunk))
            current_chunk = [line]
            current_size = line_size
        else:
            current_chunk.append(line)
            current_size += line_size
    
    # Add final chunk
    if current_chunk:
        chunks.append('\n'.join(current_chunk))
    
    print(f"✅ Created {len(chunks)} chunks")
    
    # Step 5: Calculate approximate time coverage per chunk
    # Estimate based on video length and chunk count
    if len(chunks) > 0:
        estimated_minutes_per_chunk = 60 / len(chunks)  # Assuming ~60 min video
    else:
        estimated_minutes_per_chunk = 0
    
    # Step 6: Export ALL chunks
    print(f"\n💾 Step 4: Exporting all {len(chunks)} chunks...")
    
    output_path = f"/content/ALL_CHUNKS_EXPORT_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    with open(output_path, "w", encoding="utf-8") as f:
        # Header
        f.write("COMPLETE TRANSCRIPT CHUNKS FOR EXTERNAL LEGAL ANALYSIS\n")
        f.write("="*70 + "\n\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Source: {os.path.basename(source_file)}\n")
        f.write(f"Video: {video_name}\n")
        f.write(f"Total chunks: {len(chunks)}\n")
        f.write(f"Estimated coverage: ~{estimated_minutes_per_chunk:.1f} minutes per chunk\n\n")
        
        # Instructions
        f.write("INSTRUCTIONS FOR EXTERNAL ANALYSIS:\n")
        f.write("-"*50 + "\n")
        f.write("1. Copy each chunk to your preferred AI tool (ChatGPT, Claude, etc.)\n")
        f.write("2. Use the analysis prompt below for each chunk\n")
        f.write("3. Save each chunk's analysis with its number\n")
        f.write("4. Compile all analyses into final report\n\n")
        
        # Analysis prompt
        f.write("ANALYSIS PROMPT FOR EACH CHUNK:\n")
        f.write("-"*50 + "\n")
        f.write("""
You are a forensic legal analyst reviewing police body camera transcript.
Analyze this transcript section for:

1. CONSTITUTIONAL VIOLATIONS:
   - 4th Amendment (unreasonable search/seizure, home entry)
   - 5th Amendment (Miranda rights, self-incrimination)
   - 8th Amendment (cruel treatment, dignity violations)
   - 14th Amendment (due process, equal protection)

2. SPECIFIC CONCERNS:
   - Handcuffing/restraining person in towel or minimal clothing
   - Public exposure and dignity violations
   - Mental health crisis mishandling
   - Use of force on cooperative subjects
   - Baker Act procedural violations
   - Privacy invasions in home

3. IDENTIFY AND QUOTE:
   - Exact quotes showing violations
   - Timestamps of concerning events
   - Officer statements showing intent/bias
   - Evidence of retaliation or escalation
   - Attempts to cover up or coordinate stories

4. PATTERN RECOGNITION:
   - Repeated violations
   - Escalation patterns
   - De-escalation failures
   - Policy breaches

Provide specific timestamps and exact quotes for any violations found.
Note any concerning patterns or systemic issues.
""")
        
        f.write("\n" + "="*70 + "\n")
        f.write("TRANSCRIPT CHUNKS BEGIN BELOW\n")
        f.write("="*70 + "\n")
        
        # Export ALL chunks
        for i, chunk in enumerate(chunks):
            f.write(f"\n\n{'='*70}\n")
            f.write(f"CHUNK {i+1} of {len(chunks)}\n")
            
            # Try to determine time range from timestamps in chunk
            timestamps = re.findall(r'\[(\d+:\d+:\d+)\]', chunk)
            if timestamps:
                f.write(f"Time range: {timestamps[0]} - {timestamps[-1]}\n")
            else:
                start_min = i * estimated_minutes_per_chunk
                end_min = (i + 1) * estimated_minutes_per_chunk
                f.write(f"Estimated coverage: minutes {start_min:.0f}-{end_min:.0f}\n")
            
            f.write(f"{'='*70}\n\n")
            f.write(chunk)
            f.write("\n\n--- END OF CHUNK ---")
        
        # Add compilation template
        f.write("\n\n" + "="*70 + "\n")
        f.write("ANALYSIS COMPILATION TEMPLATE\n")
        f.write("="*70 + "\n\n")
        f.write("After analyzing all chunks, compile your findings:\n\n")
        f.write("COMPREHENSIVE LEGAL ANALYSIS SUMMARY\n")
        f.write("-"*40 + "\n\n")
        f.write("1. TOTAL VIOLATIONS BY CATEGORY:\n")
        f.write("   - Constitutional: [list with counts]\n")
        f.write("   - Procedural: [list with counts]\n")
        f.write("   - Dignity/Privacy: [list with counts]\n")
        f.write("   - Use of Force: [list with counts]\n\n")
        f.write("2. MOST SERIOUS VIOLATIONS:\n")
        f.write("   [List top 5-10 with timestamps and quotes]\n\n")
        f.write("3. PATTERN ANALYSIS:\n")
        f.write("   [Identify systemic issues across chunks]\n\n")
        f.write("4. OFFICER CONDUCT:\n")
        f.write("   [List concerning behaviors by officer]\n\n")
        f.write("5. RECOMMENDATIONS:\n")
        f.write("   [Legal remedies and actions]\n")
    
    # Download the file
    print(f"\n📥 Downloading complete chunk export...")
    files.download(output_path)
    
    # Create a summary file
    summary_path = f"/content/CHUNK_EXPORT_SUMMARY_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    with open(summary_path, "w", encoding="utf-8") as f:
        f.write("CHUNK EXPORT SUMMARY\n")
        f.write("="*40 + "\n\n")
        f.write(f"Video: {video_name}\n")
        f.write(f"Total chunks created: {len(chunks)}\n")
        f.write(f"Characters per chunk: ~{max_chunk_size}\n")
        f.write(f"Export file: {os.path.basename(output_path)}\n\n")
        
        f.write("QUICK STATS:\n")
        f.write(f"- Total transcript lines: {len(transcript_lines)}\n")
        f.write(f"- Average lines per chunk: {len(transcript_lines) // len(chunks) if chunks else 0}\n")
        f.write(f"- Estimated analysis time: {len(chunks) * 2}-{len(chunks) * 3} minutes\n\n")
        
        f.write("NEXT STEPS:\n")
        f.write("1. Open the export file\n")
        f.write("2. Copy chunks one at a time to AI tool\n")
        f.write("3. Save each analysis\n")
        f.write("4. Compile using provided template\n")
    
    files.download(summary_path)
    
    print("\n✅ EXPORT COMPLETE!")
    print(f"📊 Total chunks: {len(chunks)}")
    print(f"📁 Files downloaded:")
    print(f"   - {os.path.basename(output_path)}")
    print(f"   - {os.path.basename(summary_path)}")
    print("\n🎯 This export includes ALL chunks (1 through {}) for complete analysis".format(len(chunks)))
    
    return True

# Run the tool
if __name__ == "__main__":
    universal_chunk_export_tool()