{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# COMPLE<PERSON> ENHANCED FORENSIC TRANSCRIPTION PIPELINE", "", "## Instructions for Processing Multiple Videos", "", "This pipeline is designed to process videos one at a time in Google Colab.", "", "### TO PROCESS EACH VIDEO:", "1. <PERSON> Cells 1-6 once at the beginning of your session", "2. For each video:", "   - Update Cell 2 with new file_id and video_filename", "   - Update Cell 7 skip_seconds parameter if needed (default is 30)", "   - Run Cell 2 to download the new video", "   - Run Cell 7 to process and analyze", "   - The output file will download automatically when complete", "", "### SKIP_SECONDS PARAMETER:", "- Default: 30 seconds (for videos with initial silence/muted sections)", "- Set to 0 for videos that start immediately with audio", "- Adjust as needed based on each video's characteristics", "", "### CLEARING OUTPUTS:", "- To keep notebook tidy: Edit → Clear all outputs", "- This does NOT require re-running setup cells", "- Only restart runtime if you encounter memory errors", "", "### NOTE: ", "- The pipeline uses OpenAI API (gpt-4o for vision, gpt-4 for text)", "- Make sure your API key has sufficient credits for processing", "- You do NOT need to restart runtime between videos unless you encounter memory errors"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "source": ["# Cell 1: Install Dependencies with Correct Versions", "# =============================================================================", "# Google Colab + WhisperX + CUDA 11.8 + Pyannote + Whisper Large-v3 Setup", "# Optimized for T4 GPU and High RAM", "", "!pip install -q PyDrive2", "!pip install -q git+https://github.com/m-bain/whisperx.git", "!pip install -q git+https://github.com/openai/whisper.git", "!pip install -q git+https://github.com/pyannote/pyannote-audio.git", "!pip install -q huggingface_hub", "!pip install -q openai==0.28.1  # Specific version for compatibility", "!pip install -q librosa", "!pip install -q torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118", "!pip install -q scikit-learn", "!pip install -q opencv-python", "!pip install -q Pillow", "!pip install -U transformers  # For BERT NER", "!pip install -q seqeval  # For NER evaluation", "", "print(\"✅ All dependencies installed successfully!\")", "", "# ============================================================================="], "execution_count": null}, {"cell_type": "code", "metadata": {}, "outputs": [], "source": ["# Cell 2: Download Video File from Google Drive (UPDATE FOR EACH NEW VIDEO)", "# =============================================================================", "from pydrive2.auth import GoogleAuth", "from pydrive2.drive import GoogleDrive", "from google.colab import auth", "from oauth2client.client import GoogleCredentials", "", "auth.authenticate_user()", "gauth = GoogleAuth()", "gauth.credentials = GoogleCredentials.get_application_default()", "drive = GoogleDrive(gauth)", "", "# 🔄 UPDATE THESE LINES FOR EACH NEW VIDEO:", "file_id = '1URiqv_ve0cSj0Uj6N2Qzoih8L4izqbMR'  # ← CHANGE THIS", "video_filename = 'Redacted_20__MENTALLY_DISTURBED_PERSON_3.mp4'  # ← CHANGE THIS", "", "downloaded = drive.CreateFile({'id': file_id})", "downloaded.GetContentFile(video_filename)", "print(f\"✅ Video file downloaded: {video_filename}\")", "", "# ============================================================================="], "execution_count": null}, {"cell_type": "code", "metadata": {}, "outputs": [], "source": ["# Cell 3: Authentication Setup", "# =============================================================================", "from huggingface_hub import login", "import openai", "", "# 🔑 UPDATE YOUR API KEYS HERE:", "HF_TOKEN = \"*************************************\"  # ← CHANGE THIS", "OPENAI_API_KEY = \"********************************************************************************************************************************************************************\"  # ← CHANGE THIS", "", "login(token=HF_TOKEN)", "openai.api_key = OPENAI_API_KEY", "", "print(\"✅ Authentication complete\")", "", "# ============================================================================="], "execution_count": null}, {"cell_type": "code", "metadata": {}, "outputs": [], "source": ["# Cell 4: Enhanced Forensic Pipeline Setup WITH ALL IMPROVEMENTS", "# =============================================================================", "import os", "import torch", "import whisper", "import whisperx", "import subprocess", "import librosa", "import numpy as np", "from datetime import datetime, timedelta", "from pyannote.audio import Pipeline", "from sklearn.cluster import KMeans", "import base64", "import cv2", "from PIL import Image", "from transformers import WhisperTokenizer, AutoTokenizer, AutoModelForTokenClassification, pipeline", "", "# Check GPU availability", "device = \"cuda\" if torch.cuda.is_available() else \"cpu\"", "print(f\"Using device: {device}\")", "", "# Enhanced legal trigger words for forensic analysis - INCLUDING NEW WORDS", "LEGAL_TRIGGER_WORDS = [", "    \"arrest\", \"detained\", \"miranda\", \"rights\", \"warrant\", \"search\", \"seizure\",", "    \"consent\", \"constitutional\", \"fourth amendment\", \"fifth amendment\",", "    \"baker act\", \"mental health\", \"crisis\", \"suicide\", \"self harm\",", "    \"force\", \"taser\", \"pepper spray\", \"baton\", \"firearm\", \"weapon\",", "    \"assault\", \"battery\", \"resistance\", \"compliance\", \"cooperation\",", "    \"medical\", \"injury\", \"pain\", \"breathing\", \"unconscious\", \"responsive\",", "    \"supervisor\", \"sergeant\", \"lieutenant\", \"backup\", \"ambulance\", \"ems\",", "    \"lawsuit\", \"carolina\", \"palm beach\", \"officer\", \"sheriff\", \"5150\",", "    \"order\", \"refusal\", \"psych\", \"RPO\", \"sane\", \"suicidal\", \"husband\",", "    \"combative\", \"harold\", \"hastings\", \"gun\", \"shotgun\", \"welfare\", \"lucid\",", "    \"hands up\", \"get down\", \"stop resisting\", \"calm down\", \"relax\",", "    \"towel\", \"naked\", \"undressed\", \"barefoot\", \"wet\", \"shower\", \"bathroom\",", "    \"cuff\", \"cuffs\", \"handcuff\", \"handcuffed\", \"restrained\", \"dignity\",", "    \"humiliate\", \"embarrass\", \"film\", \"recording\", \"camera\", \"mute\",", "    \"cover\", \"blanket\", \"sheet\", \"expose\", \"exposure\", \"neighbors\",", "    \"crowd\", \"public\", \"private\", \"home\", \"residence\", \"emergency\",", "    \"interrupted\", \"rushed\", \"swat\", \"tactical\", \"escalate\", \"de-escalate\"", "]", "", "# Legal case law references", "CASE_LAW_REFERENCES = {", "    \"<PERSON> v<PERSON>\": \"490 U.S. 386 (1989) - Use of force analysis\",", "    \"Tennessee v<PERSON> <PERSON>\": \"471 U.S. 1 (1985) - Deadly force standards\",", "    \"Payton v. New York\": \"445 U.S. 573 (1980) - Warrantless home entry\",", "    \"Kentucky v. King\": \"563 U.S. 452 (2011) - Exigent circumstances\",", "    \"York v. <PERSON>\": \"324 F.2d 450 (9th Cir. 1963) - Privacy dignity violations\",", "    \"Jordan v<PERSON>\": \"986 F.2d 1521 (9th Cir. 1993) - Cross-gender searches\",", "    \"Bell v<PERSON>\": \"441 U.S. 520 (1979) - Detention conditions\",", "    \"<PERSON><PERSON> v. <PERSON>\": \"457 U.S. 307 (1982) - Mental health detainees\"", "}", "", "def enhanced_audio_processing_for_difficult_sections(input_path, output_path):", "    \"\"\"Multi-pass audio enhancement for challenging sections\"\"\"", "    print(\"🔊 Enhanced audio processing for difficult sections...\")", "", "    # Pass 1: Normalize volume and compress dynamic range for distant speakers", "    pass1_path = \"/content/audio_pass1.wav\"", "    cmd1 = [", "        'ffmpeg', '-y', '-i', input_path,", "        '-af', 'dynaudnorm=p=0.9:s=5,compand=attacks=0.1:decays=0.5:points=-90/-90|-60/-40|-40/-25|-25/-15|-10/-10',", "        '-acodec', 'pcm_s16le', '-ar', '16000', '-ac', '1',", "        pass1_path", "    ]", "    subprocess.run(cmd1, capture_output=True)", "", "    # Pass 2: Enhance speech frequencies and reduce background noise", "    pass2_path = \"/content/audio_pass2.wav\"", "    cmd2 = [", "        'ffmpeg', '-y', '-i', pass1_path,", "        '-af', 'highpass=f=80,lowpass=f=8000,equalizer=f=2000:width_type=h:width=1000:g=3',", "        '-acodec', 'pcm_s16le',", "        pass2_path", "    ]", "    subprocess.run(cmd2, capture_output=True)", "", "    # Pass 3: Handle loud shouting and volume spikes", "    cmd3 = [", "        'ffmpeg', '-y', '-i', pass2_path,", "        '-af', 'alimiter=level_in=1:level_out=0.8:limit=0.9,volume=1.5',", "        '-acodec', 'pcm_s16le',", "        output_path", "    ]", "    subprocess.run(cmd3, capture_output=True)", "", "    print(f\"✅ Enhanced audio saved: {output_path}\")", "", "def transcribe_with_maximum_accuracy_enhanced(audio_path, language=\"en\"):", "    \"\"\"Enhanced Whisper transcription with anti-hallucination settings\"\"\"", "    print(\"🎙️ Loading Whisper Large-v3 for maximum accuracy...\")", "", "    model = whisperx.load_model(\"large-v3\", device=device, compute_type=\"float16\")", "    tokenizer = WhisperTokenizer.from_pretrained(\"openai/whisper-large-v3\")", "", "    print(\"🔄 Transcribing with enhanced settings...\")", "    result = model.transcribe(", "        audio_path,", "        language=\"en\",", "        word_timestamps=True,", "        temperature=0,", "        beam_size=5,", "        best_of=5,", "        condition_on_previous_text=False,", "        compression_ratio_threshold=1.8,  # Lower to catch repetition", "        logprob_threshold=-0.5,           # Higher to be more selective", "        no_speech_threshold=0.4,          # Lower to catch more speech", "        initial_prompt=\"This is a police body camera recording with multiple speakers including officers, civilians, dispatch, and EMS. Audio may include shouting, distant speech, and overlapping conversations.\",", "        hallucinated_phrases=[", "            \"Thank you\", \"Thank you.\", \"You're welcome\", \"I'm sorry\", \"Yes\",", "            \"No\", \"Okay\", \"<PERSON>\", \"Hello\", \"Good morning\", \"Goodbye\", \"Sir\", \"Ma'am\"]", "    )", "", "    print(f\"✅ Transcription complete: {len(result['text'])} characters\")", "    return result", "", "def analyze_video_frames_for_context(video_path, skip_seconds=30):", "    \"\"\"Extract and analyze video frames for visual context with GPT-4 Vision - WITH ENHANCED RESTRAINT ANALYSIS\"\"\"", "    print(\"📹 Analyzing video frames for visual context...\")", "", "    # Extract key frames every 30 seconds", "    frames_dir = \"/content/video_frames\"", "    os.makedirs(frames_dir, exist_ok=True)", "", "    extract_frames_cmd = [", "        'ffmpeg', '-y', '-ss', str(skip_seconds), '-i', video_path,", "        '-vf', 'fps=1/20',  # One frame every 20 seconds", "        '-q:v', '2',  # High quality", "        f'{frames_dir}/frame_%04d.jpg'", "    ]", "", "    subprocess.run(extract_frames_cmd, capture_output=True)", "", "    # Analyze frames with GPT-4 Vision", "    frame_files = sorted([f for f in os.listdir(frames_dir) if f.endswith('.jpg')])", "    visual_context = []", "    frame_cache = {}  # Stores frame_path → base64", "", "    print(f\"🔍 Analyzing {len(frame_files)} video frames...\")", "", "    for i, frame_file in enumerate(frame_files):", "        frame_path = os.path.join(frames_dir, frame_file)", "        timestamp = (i * 20) + skip_seconds  # Calculate actual timestamp (20 sec intervals)", "", "        # Encode frame for GPT-4 Vision", "        try:", "            if frame_path in frame_cache:", "                frame_data = frame_cache[frame_path]", "            else:", "                with open(frame_path, 'rb') as f:", "                   frame_data = base64.b64encode(f.read()).decode()", "                   frame_cache[frame_path] = frame_data", "", "            response = openai.ChatCompletion.create(", "                model=\"gpt-4o\",  # Using gpt-4o instead of deprecated gpt-4-vision-preview", "                messages=[", "                    {", "                        \"role\": \"user\",", "                        \"content\": [", "                            {", "                                \"type\": \"text\",", "                                \"text\": \"\"\"Analyze this police bodycam frame for forensic documentation. Provide detailed analysis of:", "", "1. SCENE SETTING: Location type, environment, lighting conditions, etc.", "2. PEOPLE VISIBLE: Number of individuals, their positions, actions, posture, clothing, etc.", "3. EQUIPMENT/EVIDENCE: Weapons, vehicles, medical equipment, evidence items, etc.", "4. TACTICAL POSITIONING: Officer formation, civilian positioning, spatial dynamics, threat levels, etc.", "5. EMOTIONAL INDICATORS: Body language, gestures, apparent stress levels, emotional reactions, etc.", "6. SAFETY CONCERNS: Potential hazards, weapons visible, environmental risks, threat levels, etc.", "7. LEGAL SIGNIFICANCE: Constitutional issues, use of force implications, breach of procedures, escalation, deescalation, evidence preservation, etc.", "8. ATTIRE ANALYSIS (CRITICAL FOR PRIVACY ASSESSMENT):", "   - Clothing status: Fully dressed, partially dressed, minimal clothing, towel only, etc.", "   - State of dress: Appropriate, inappropriate for public, emergency exit clothing", "   - Signs of rushed dressing: Incomplete clothing, towel wrapped, wet appearance", "   - Modesty concerns: Areas of body exposed, coverage inadequacy", "   - Footwear: Barefoot, shoes, slippers (indicates emergency exit)", "", "9. PRIVACY & DIGNITY INDICATORS:", "   - Public exposure level: Private home vs. public view", "   - Bystander presence: Neighbors, crowds, passersby witnessing exposure", "   - Recording implications: Subject aware of being filmed in state of undress", "   - Weather conditions affecting minimal clothing exposure", "", "10. EMERGENCY/CRISIS INDICATORS:", "   - Wet hair/body (shower interruption)", "   - Rushed appearance (hastily grabbed clothing/towel)", "   - Bathroom/shower context (wet floors, steam, towels visible)", "   - Time pressure indicators (incomplete dressing)", "", "11. RESTRAINT/HANDCUFFING ANALYSIS:", "   - Handcuff application on subject in minimal clothing", "   - Positioning: hands behind back while in towel/minimal clothing", "   - Dignity concerns during restraint application", "   - Cooperative behavior vs. restraint necessity", "", "12. STANDARD FORENSIC ELEMENTS:", "   - Scene setting and location context", "   - People positions and actions", "   - Equipment and evidence visible", "   - Officer positioning relative to undressed subject", "   - Safety and tactical considerations", "", "13. CONSTITUTIONAL CONCERNS:", "   - 4th Amendment: Privacy expectations in home", "   - 8th Amendment: Dignity during detention", "   - Public exposure creating humiliation", "   - Reasonable accommodation for clothing needs", "", "Be specific, objective, and forensically precise. Use timestamps and positional references. Be extremely specific about clothing status and privacy implications. Flag any dignitary concerns or inappropriate exposure situations. Document if subject appears to have been interrupted during private activities (bathing, dressing, etc.).\"\"\"", "                            },", "                            {", "                                \"type\": \"image_url\",", "                                \"image_url\": {", "                                    \"url\": f\"data:image/jpeg;base64,{frame_data}\"", "                                }", "                            }", "                        ]", "                    }", "                ],", "                max_tokens=1000,", "                temperature=0.1", "            )", "", "            visual_analysis = response.choices[0].message.content", "            visual_context.append({", "                'timestamp': timestamp,", "                'frame': frame_file,", "                'analysis': {", "                    'raw_text': visual_analysis,", "                    'scene_setting': None,", "                    'privacy': None,", "                    'emergency_flags': [],", "                }", "            })", "", "            print(f\"✅ Enhanced frame analysis - Frame analyzed: {timestamp//60:02d}:{timestamp%60:02d}\")", "", "        except Exception as e:", "            print(f\"⚠️ Frame analysis failed for {frame_file}: {e}\")", "            visual_context.append({", "                'timestamp': timestamp,", "                'frame': frame_file,", "                'analysis': f\"Visual analysis unavailable: {e}\"", "            })", "", "    print(f\"✅ Enhanced visual context analysis complete: {len(visual_context)} frames\")", "    return visual_context", "", "def detect_speaker_overlaps_and_separate_enhanced(audio_path, diarization_result, whisper_result):", "    \"\"\"Enhanced speaker overlap detection with better sensitivity\"\"\"", "    print(\"👥 Enhanced speaker overlap detection...\")", "", "    overlaps = []", "", "    # Convert diarization to list of segments", "    diar_segments = []", "    for turn, _, speaker in diarization_result.itertracks(yield_label=True):", "        diar_segments.append({", "            'start': turn.start,", "            'end': turn.end,", "            'speaker': speaker", "        })", "", "    # Find overlapping segments with enhanced sensitivity", "    for i, seg1 in enumerate(diar_segments):", "        for seg2 in diar_segments[i+1:]:", "            # Check for overlap", "            overlap_start = max(seg1['start'], seg2['start'])", "            overlap_end = min(seg1['end'], seg2['end'])", "", "            if overlap_start < overlap_end:", "                duration = overlap_end - overlap_start", "                if duration > 0.4:  # Lowered threshold from 0.5 to catch more overlaps", "                    overlaps.append({", "                        'start': overlap_start,", "                        'end': overlap_end,", "                        'duration': duration,", "                        'speakers': [seg1['speaker'], seg2['speaker']]", "                    })", "", "    print(f\"✅ Enhanced overlap detection complete: {len(overlaps)} overlaps found\")", "    return overlaps", "", "def format_overlap_readable(overlap, whisper_result):", "    start = overlap['start']", "    end = overlap['end']", "    speakers = overlap['speakers']", "    timestamp = f\"[{int(start//60):02}:{int(start%60):02}–{int(end//60):02}:{int(end%60):02}]\"", "", "    lines_by_speaker = {s: [] for s in speakers}", "", "    for seg in whisper_result['segments']:", "        if seg['start'] >= start and seg['end'] <= end:", "            speaker = seg.get('speaker', 'UNKNOWN')", "            if speaker in lines_by_speaker:", "                lines_by_speaker[speaker].append(seg['text'].strip())", "", "    output = f\"{timestamp} **OVERLAP** {tuple(speakers)}:\\n\"", "    for speaker, lines in lines_by_speaker.items():", "        if lines:", "            joined = ' '.join(lines)", "            output += f\"{speaker}: {joined}\\n\"", "", "    return output.strip()", "", "def combine_transcription_and_speakers_enhanced(whisper_result, diarization_result, overlaps):", "    \"\"\"Enhanced combination with better word-level speaker assignment\"\"\"", "    print(\"🔗 Enhanced transcription and speaker combination...\")", "", "    enhanced_transcript = []", "", "    # Process each word from <PERSON><PERSON><PERSON> with enhanced speaker matching", "    for segment in whisper_result['segments']:", "        for word_info in segment.get('words', []):", "            word_start = word_info['start']", "            word_end = word_info['end']", "            word_text = word_info['word']", "            word_confidence = word_info.get('probability', 0.0)", "", "            # Find speaker(s) for this word with tolerance", "            speakers = []", "            tolerance = 0.1  # 100ms tolerance for better matching", "", "            for turn, _, speaker in diarization_result.itertracks(yield_label=True):", "                if (turn.start - tolerance) <= word_start <= (turn.end + tolerance):", "                    speakers.append(speaker)", "", "            # Check for overlaps", "            is_overlap = False", "            overlap_speakers = []", "            for overlap in overlaps:", "                if overlap['start'] <= word_start <= overlap['end']:", "                    is_overlap = True", "                    overlap_speakers = overlap['speakers']", "                    break", "", "            enhanced_transcript.append({", "                'word': word_text,", "                'start': word_start,", "                'end': word_end,", "                'confidence': word_confidence,", "                'speakers': speakers,", "                'overlap': is_overlap,", "                'overlap_speakers': overlap_speakers", "            })", "", "    print(f\"✅ Enhanced transcript created: {len(enhanced_transcript)} words\")", "    enhanced_transcript.sort(key=lambda x: x['start'])", "    return enhanced_transcript", "", "def analyze_with_gpt4_forensic_enhanced(transcript_text, speaker_segments, trigger_words, visual_context):", "    \"\"\"Enhanced GPT-4 forensic analysis incorporating both audio and visual data\"\"\"", "    print(\"🧠 Running enhanced GPT-4 forensic analysis...\")", "", "    # Combine visual context for analysis", "    visual_summary = \"\\n\".join([", "        f\"[{ctx['timestamp']//60:02d}:{ctx['timestamp']%60:02d}] VISUAL: {ctx['analysis']}\"", "        for ctx in visual_context[:10]  # Include first 10 visual analyses", "    ])", "", "    system_prompt = \"\"\"You are a certified forensic audiovisual analyst with 25+ years experience in criminal procedure, constitutional law (42 U.S.C. § 1983), and police misconduct analysis. You have served as a court-appointed expert witness and specialize in integrated audio-visual evidence analysis.", "", "Conduct comprehensive forensic analysis incorporating both audio transcript and visual frame analysis for:", "", "1. CONSTITUTIONAL VIOLATIONS:", "   - 4th Amendment (search/seizure without warrant)", "   - 5th Amendment (Miranda rights, self-incrimination)", "   - 8th Amendment (excessive force, cruel treatment)", "   - 14th Amendment (due process, equal protection)", "", "2. STATUTORY VIOLATIONS:", "   - Florida Statutes (Baker Act § 394.463)", "   - Arrest authority compliance (Ch. 901)", "   - Mental health detention protocols", "   - Transport and medical clearance requirements", "", "3. PROCEDURAL BREACHES:", "   - Required warnings not given", "   - Supervisor notification failures", "   - Medical clearance timing violations", "   - Evidence preservation protocols", "", "4. USE OF FORCE ASSESSMENT:", "   - <PERSON> v. Connor standards compliance", "   - Proportionality analysis (visual evidence critical)", "   - De-escalation attempts/failures", "   - Weapon deployment justification", "", "5. AUDIO-VISUAL CORRELATION:", "   - Consistency between spoken actions and visual evidence", "   - Body language vs verbal compliance", "   - Environmental factors affecting behavior", "   - Officer positioning and tactical decisions", "", "6. PSYCHOLOGICAL MARKERS:", "   - Mental health crisis indicators (audio + visual)", "   - Stress escalation patterns", "   - Compliance vs resistance behaviors", "   - Environmental stressors", "   - Mental health interventions", "", "7. ATTIRE ANALYSIS (CRITICAL FOR PRIVACY ASSESSMENT):", "   - Clothing status: Fully dressed, partially dressed, minimal clothing, towel only, etc.", "   - State of dress: Appropriate, inappropriate for public, emergency exit clothing", "   - Signs of rushed dressing: Incomplete clothing, towel wrapped, wet appearance", "   - Modesty concerns: Areas of body exposed, coverage inadequacy", "   - Footwear: Barefoot, shoes, slippers (indicates emergency exit)", "", "8. PRIVACY & DIGNITY INDICATORS:", "   - Public exposure level: Private home vs. public view", "   - Bystander presence: Neighbors, crowds, passersby witnessing exposure", "   - Recording implications: Subject aware of being filmed in state of undress", "   - Weather conditions affecting minimal clothing exposure", "", "9. EMERGENCY/CRISIS INDICATORS:", "   - Wet hair/body (shower interruption)", "   - Rushed appearance (hastily grabbed clothing/towel)", "   - Bathroom/shower context (wet floors, steam, towels visible)", "   - Time pressure indicators (incomplete dressing)", "", "10. RESTRAINT/HANDCUFFING ANALYSIS:", "   - Handcuff application on subject in minimal clothing", "   - Positioning: hands behind back while in towel/minimal clothing", "   - Dignity concerns during restraint application", "   - Cooperative behavior vs. restraint necessity", "", "11. STANDARD FORENSIC ELEMENTS:", "   - Scene setting and location context", "   - People positions and actions", "   - Equipment and evidence visible", "   - Officer positioning relative to undressed subject", "   - Safety and tactical considerations", "", "12. CONSTITUTIONAL CONCERNS:", "   - 4th Amendment: Privacy expectations in home", "   - 8th Amendment: Dignity during detention", "   - Public exposure creating humiliation", "   - Reasonable accommodation for clothing needs", "", "Provide specific timestamps, direct quotes, visual observations, legal significance, and court-admissible analysis with integrated audio-visual evidence correlation. Be extremely specific about clothing status and privacy implications. Flag any dignitary concerns or inappropriate exposure situations. Document if subject appears to have been interrupted during private activities (showering, bathing, dressing, etc.).\"\"\"", "", "    user_prompt = f\"\"\"", "POLICE BODYCAM INTEGRATED AUDIO-VISUAL ANALYSIS:", "", "AUDIO TRANSCRIPT (First 8000 characters):", "{transcript_text[:8000]}", "", "VISUAL FRAME ANALYSIS:", "{visual_summary}", "", "LEGAL TRIGGER WORDS DETECTED:", "{', '.join(trigger_words)}", "", "SPEAKER COUNT: {len(set(seg.get('speaker', 'Unknown') for seg in speaker_segments))}", "", "Provide comprehensive integrated forensic analysis with:", "- Constitutional and statutory violations (cite specific evidence)", "- Critical timeline events with both audio and visual timestamps", "- Use of force analysis with visual evidence correlation", "- Risk assessment for legal proceedings", "- Evidence preservation recommendations", "- Audio-visual consistency analysis", "\"\"\"", "", "    try:", "        response = openai.ChatCompletion.create(", "            model=\"gpt-4o\",", "            messages=[", "                {\"role\": \"system\", \"content\": system_prompt},", "                {\"role\": \"user\", \"content\": user_prompt}", "            ],", "            max_tokens=4000,", "            temperature=0.05", "        )", "", "        return response.choices[0].message.content", "", "    except Exception as e:", "        print(f\"❌ GPT-4 analysis failed: {e}\")", "        return f\"GPT-4 analysis unavailable: {e}\"", "", "def inject_visual_context_into_transcript(enhanced_transcript, visual_context, skip_seconds=30):", "    \"\"\"Inject visual context annotations into transcript at appropriate timestamps\"\"\"", "    print(\"💉 Injecting visual context into transcript...\")", "", "    visual_injections = {}", "", "    # Map visual context to transcript timestamps", "    for ctx in visual_context:", "        visual_timestamp = ctx['timestamp']", "", "        # Find the closest word in the transcript to inject visual context", "        closest_word_index = None", "        min_time_diff = float('inf')", "", "        for i, word_data in enumerate(enhanced_transcript):", "            word_timestamp = word_data['start'] + skip_seconds", "            time_diff = abs(word_timestamp - visual_timestamp)", "", "            if time_diff < min_time_diff and time_diff < 15:  # Within 15 seconds", "                min_time_diff = time_diff", "                closest_word_index = i", "", "        if closest_word_index is not None:", "            visual_injections[closest_word_index] = f\"*{{VISUAL CONTEXT: {ctx['analysis'][:200]}...}}*\"", "", "    print(f\"✅ Visual context injections prepared: {len(visual_injections)} injections\")", "    return visual_injections", "", "def inject_contextual_annotations_enhanced(enhanced_transcript):", "    \"\"\"Enhanced contextual legal/psychological annotations - WITH LIST SUPPORT\"\"\"", "    print(\"💉 Injecting enhanced contextual annotations...\")", "", "    annotations = {}", "", "    for i, word_data in enumerate(enhanced_transcript):", "        text = word_data.get('word', '').lower()", "", "        # Enhanced legal trigger detection", "        if any(word in text for word in ['miranda', 'rights', 'remain silent']):", "            annotations.setdefault(i, []).append(\"*{Miranda rights advisement - 5th Amendment constitutional requirement}*\")", "        elif any(word in text for word in ['force', 'taser', 'weapon', 'gun', 'swat']):", "            annotations.setdefault(i, []).append(\"*{Use of force deployment - <PERSON> v<PERSON> analysis required}*\")", "        elif any(word in text for word in ['baker act', 'mental health', 'crisis', '5150', 'behavioral health', 'rpo', 'risk protection', 'no blood', 'suicidal']):", "            annotations.setdefault(i, []).append(\"*{Mental health detention protocol - Fla. Stat. § 394.463}*\")", "        elif any(word in text for word in ['search', 'seizure', 'house', 'rpo', 'risk protection']):", "            annotations.setdefault(i, []).append(\"*{4th Amendment search/seizure activity - warrant requirement analysis}*\")", "        elif any(word in text for word in ['consent', 'permission', 'fine', 'baker act', 'take me anywhere', 'suicidal', 'detained', 'restrained', 'cuff', 'secure', 'clear', 'house', 'ask her']):", "            annotations.setdefault(i, []).append(\"*{Consent documentation - voluntariness analysis required}*\")", "        elif any(word in text for word in ['supervisor', 'sergeant', 'lieutenant', 'williams']):", "            annotations.setdefault(i, []).append(\"*{Supervisory involvement - chain of command protocol}*\")", "        elif any(word in text for word in ['ambulance', 'ems', 'medical', 'injury', 'rescue', 'no blood']):", "            annotations.setdefault(i, []).append(\"*{Medical intervention - duty of care assessment}*\")", "        elif any(word in text for word in ['hands up', 'get down', 'stop', 'walk backwards', 'face away', 'turn around']):", "            annotations.setdefault(i, []).append(\"*{Compliance directive - officer command analysis}*\")", "        elif any(word in text for word in ['calm down', 'relax', 'breathe', 'escalation,' 'embarrass', 'humiliate', 'neighbors']):", "            annotations.setdefault(i, []).append(\"*{De-escalation attempt - crisis intervention technique}*\")", "        elif any(word in text for word in ['escalation,' 'embarrass', 'humiliate', 'neighbors', 'swat', 'shotgun', 'cock', 'lethal', 'lethaly', 'go in', 'not leaving', 'assess the house']):", "            annotations.setdefault(i, []).append(\"*{Escalation attempts, behaviors, meneuvers, tactics - unwarranted and/or improper escalation analysis}*\")", "        elif any(word in text for word in ['escalate', 'embarrass', 'humiliate', 'neighbors', 'swat', 'towel', 'wet', 'cuff', 'cover her', 'sheet', 'game', 'games', 'song and dance', 'regardless', 'play']):", "            annotations.setdefault(i, []).append(\"*{Retaliatory and/or punitive  tactics - unwarranted and/or improper escalation analysis}*\")", "        elif any(word in text for word in ['escalate', 'embarrass', 'humiliate', 'neighbors', 'swat', 'towel', 'wet', 'cuff', 'cover her', 'sheet', 'dress', 'naked', 'cover me', 'filming', 'videoing', 'watching']):", "            annotations.setdefault(i, []).append(\"*{Humiliation/Dignity/Public shame activities, tactics, behaviors - analysis of intentional and/or unintentional plublic shame, humiliation, embarrassment, preservation of dignity activities}*\")", "        elif any(word in text for word in ['heads up', 'shotgun', 'baker act', 'heard you', \"don't tell\", \"don't say\", 'no blood', 'in front of', 'mute', 'blue', 'camera', 'teach', 'complaint', \"don't teach\", 'statement', 'report', 'concern']):", "            annotations.setdefault(i, []).append(\"*{Transparency cocerns, cover-up, coordination concerns - transparency and proper/improper disclosure assessment, narrative coordination/alignments discussions and/or behaviors, body-worn-camera muting and/or deactivation assesment, suspicious redaction assessment}*\")", "        elif any(word in text for word in ['cuff', 'cuffs', 'handcuff', 'handcuffed', 'restrained']):", "            annotations.setdefault(i, []).append(\"*{RESTRAINT APPLICATION: Handcuffing procedure - dignity and necessity analysis required}*\")", "        elif any(word in text for word in ['towel', 'naked', 'undressed', 'barefoot', 'wet', 'shower', 'bathroom']):", "            annotations.setdefault(i, []).append(\"*{ATTIRE CONCERN: Minimal clothing status - privacy and dignity implications}*\")", "", "    return annotations", "", "def analyze_transcript_confidence_metrics(enhanced_transcript):", "    \"\"\"Analyze confidence metrics for transcript accuracy\"\"\"", "    print(\"📊 Analyzing transcript confidence metrics...\")", "    ", "    confidence_stats = {", "        'high_confidence': 0,  # > 0.9", "        'medium_confidence': 0,  # 0.7 - 0.9", "        'low_confidence': 0,  # < 0.7", "        'total_words': len(enhanced_transcript),", "        'problematic_sections': []", "    }", "    ", "    current_low_conf_start = None", "    low_conf_count = 0", "    ", "    for i, word_data in enumerate(enhanced_transcript):", "        confidence = word_data.get('confidence', 0.0)", "        ", "        if confidence > 0.9:", "            confidence_stats['high_confidence'] += 1", "            # End low confidence section if we were tracking one", "            if current_low_conf_start and low_conf_count >= 5:", "                confidence_stats['problematic_sections'].append({", "                    'start_index': current_low_conf_start,", "                    'end_index': i-1,", "                    'word_count': low_conf_count,", "                    'timestamp': enhanced_transcript[current_low_conf_start]['start']", "                })", "            current_low_conf_start = None", "            low_conf_count = 0", "        elif confidence >= 0.7:", "            confidence_stats['medium_confidence'] += 1", "        else:", "            confidence_stats['low_confidence'] += 1", "            if current_low_conf_start is None:", "                current_low_conf_start = i", "            low_conf_count += 1", "    ", "    # Calculate percentages", "    total = confidence_stats['total_words']", "    confidence_stats['high_confidence_pct'] = (confidence_stats['high_confidence'] / total) * 100", "    confidence_stats['medium_confidence_pct'] = (confidence_stats['medium_confidence'] / total) * 100", "    confidence_stats['low_confidence_pct'] = (confidence_stats['low_confidence'] / total) * 100", "    ", "    print(f\"✅ Confidence analysis complete: {confidence_stats['high_confidence_pct']:.1f}% high confidence\")", "    ", "    return confidence_stats", "", "def generate_audio_quality_report(enhanced_transcript, overlaps, confidence_stats):", "    \"\"\"Generate detailed audio quality and transcription accuracy report\"\"\"", "    ", "    report = \"AUDIO QUALITY AND TRANSCRIPTION ACCURACY REPORT:\\n\"", "    report += \"=\"*50 + \"\\n\\n\"", "    ", "    report += \"OVERALL CONFIDENCE METRICS:\\n\"", "    report += f\"- High Confidence Words: {confidence_stats['high_confidence']} ({confidence_stats['high_confidence_pct']:.1f}%)\\n\"", "    report += f\"- Medium Confidence Words: {confidence_stats['medium_confidence']} ({confidence_stats['medium_confidence_pct']:.1f}%)\\n\"", "    report += f\"- Low Confidence Words: {confidence_stats['low_confidence']} ({confidence_stats['low_confidence_pct']:.1f}%)\\n\"", "    report += f\"- Total Words Analyzed: {confidence_stats['total_words']}\\n\\n\"", "    ", "    report += f\"SPEAKER OVERLAP INCIDENTS: {len(overlaps)}\\n\"", "    report += f\"PROBLEMATIC SECTIONS: {len(confidence_stats['problematic_sections'])}\\n\\n\"", "    ", "    if confidence_stats['problematic_sections']:", "        report += \"LOW CONFIDENCE SECTIONS REQUIRING REVIEW:\\n\"", "        report += \"-\"*40 + \"\\n\"", "        for section in confidence_stats['problematic_sections'][:10]:  # Top 10", "            timestamp = str(timedelta(seconds=int(section['timestamp'])))", "            report += f\"- [{timestamp}] {section['word_count']} consecutive low-confidence words\\n\"", "    ", "    report += \"\\nRECOMMENDATIONS:\\n\"", "    if confidence_stats['low_confidence_pct'] > 10:", "        report += \"⚠️ High percentage of low-confidence transcription\\n\"", "        report += \"   - Manual review strongly recommended\\n\"", "        report += \"   - Consider audio enhancement for re-transcription\\n\"", "    ", "    if len(overlaps) > 20:", "        report += \"⚠️ Significant speaker overlap detected\\n\"", "        report += \"   - May impact accuracy of speaker attribution\\n\"", "        report += \"   - Critical sections should be manually verified\\n\"", "    ", "    return report", "", "# ENHANCED LEGAL ANALYSIS FUNCTIONS", "# Add these functions to Cell 4 (insert after the existing functions)", "", "def cross_reference_utterances_with_behavior(enhanced_transcript, visual_context, skip_seconds=30):", "    \"\"\"Cross-reference speaker utterances with observable behavior for contradictions\"\"\"", "    print(\"🔍 Cross-referencing utterances with visual behavior...\")", "", "    behavioral_contradictions = []", "    compliance_violations = []", "", "    # Map commands to expected visual responses", "    command_keywords = {", "        'hands up': 'raised hands visible',", "        'get down': 'subject lowering to ground',", "        'turn around': 'subject rotating position',", "        'step back': 'backward movement',", "        'calm down': 'reduced agitation indicators',", "        'stop resisting': 'cessation of physical resistance',", "        'dont move': 'static positioning'", "    }", "", "    for i, word_data in enumerate(enhanced_transcript):", "        word_timestamp = word_data['start'] + skip_seconds", "        word_text = word_data['word'].lower()", "        speakers = word_data['speakers']", "", "        # Check if this is an officer command", "        is_officer_command = any('officer' in str(speaker).lower() or", "                               speaker in ['SPEAKER_A', 'SPEAKER_B', 'SPEAKER_C']", "                               for speaker in speakers)", "", "        if is_officer_command:", "            for command, expected_behavior in command_keywords.items():", "                if command in word_text:", "                    # Find corresponding visual context (within 30 seconds)", "                    corresponding_visual = None", "                    for ctx in visual_context:", "                        if abs(ctx['timestamp'] - word_timestamp) <= 30:", "                            corresponding_visual = ctx", "                            break", "", "                    if corresponding_visual:", "                        visual_analysis = corresponding_visual['analysis'].lower()", "", "                        # Check for compliance/non-compliance indicators", "                        compliance_indicators = ['complying', 'following', 'obeying', 'hands raised', 'cooperation']", "                        resistance_indicators = ['resisting', 'non-compliant', 'refusing', 'aggressive', 'fighting', 'obstructing']", "", "                        has_compliance = any(indicator in visual_analysis for indicator in compliance_indicators)", "                        has_resistance = any(indicator in visual_analysis for indicator in resistance_indicators)", "", "                        if command in ['hands up', 'get down', 'stop resisting'] and has_resistance:", "                            compliance_violations.append({", "                                'timestamp': word_timestamp,", "                                'command': command,", "                                'visual_evidence': visual_analysis[:200],", "                                'contradiction_type': 'Command not followed',", "                                'speakers': speakers", "                            })", "", "                        # Flag potential contradictions", "                        if 'calm down' in command and 'agitated' in visual_analysis:", "                            behavioral_contradictions.append({", "                                'timestamp': word_timestamp,", "                                'audio_content': word_text,", "                                'visual_content': visual_analysis[:200],", "                                'contradiction': 'De-escalation command during continued agitation'", "                            })", "", "    print(f\"✅ Found {len(compliance_violations)} compliance violations\")", "    print(f\"✅ Found {len(behavioral_contradictions)} behavioral contradictions\")", "", "    return compliance_violations, behavioral_contradictions", "", "def analyze_privacy_dignity_violations(enhanced_transcript, visual_context, skip_seconds=30):", "    \"\"\"Analyze privacy and dignity violations - WITH ENHANCED HANDCUFFING DETECTION\"\"\"", "    print(\"🔒 Analyzing privacy and dignity violations...\")", "", "    privacy_violations = []", "    dignity_violations = []", "    attire_violations = []", "    public_exposure_incidents = []", "", "    # Privacy violation keywords", "    privacy_keywords = ['strip', 'naked', 'undress', 'expose', 'body search', 'intimate', 'private parts']", "", "    # Dignity violation keywords", "    dignity_keywords = ['humiliate', 'embarrass', 'degrade', 'mock', 'ridicule', 'shame']", "", "    # Public exposure keywords # Enhanced keywords for clothing/attire situations", "    exposure_keywords = ['public', 'crowd', 'spectators', 'bystanders', 'recording',", "                        'exposed', 'visible', 'uncovered', 'inappropriate', 'public view',", "                        'neighbors seeing', 'crowd watching', 'filming']", "", "    emergency_exit_keywords = ['rushed out', 'hurried', 'interrupted', 'grabbed towel',", "                              'wet hair', 'steam', 'bathroom door', 'shower interrupted']", "", "    attire_keywords = ['towel', 'naked', 'undressed', 'partially clothed', 'minimal clothing' 'cover',", "                      'barefoot', 'wet', 'shower', 'bathing', 'bathroom', 'rushed', 'incomplete dress']", "", "    handcuff_dignity_keywords = ['handcuffed', 'cuffed', 'restrained', 'hands behind back',", "                                'restraints applied', 'detained']", "", "    # Analyze audio for clothing/exposure references", "    for i, word_data in enumerate(enhanced_transcript):", "        word_timestamp = word_data['start'] + skip_seconds", "        word_text = word_data['word'].lower()", "        speakers = word_data['speakers']", "", "        # Check for privacy violations", "        if any(keyword in word_text for keyword in privacy_keywords):", "            # Find corresponding visual context", "            visual_evidence = None", "            for ctx in visual_context:", "                if abs(ctx['timestamp'] - word_timestamp) <= 60:", "                    visual_evidence = ctx['analysis']", "                    break", "", "            privacy_violations.append({", "                'timestamp': word_timestamp,", "                'audio_evidence': word_text,", "                'visual_evidence': visual_evidence[:200] if visual_evidence else 'No visual context',", "                'violation_type': 'Privacy violation',", "                'speakers': word_data['speakers']", "            })", "", "        # Check for attire-related violations", "        if any(keyword in word_text for keyword in attire_keywords):", "            # Find corresponding visual context", "            visual_evidence = None", "            for ctx in visual_context:", "                if abs(ctx['timestamp'] - word_timestamp) <= 60:", "                    visual_evidence = ctx['analysis']", "                    break", "", "            attire_violations.append({", "                'timestamp': word_timestamp,", "                'audio_evidence': word_text,", "                'visual_evidence': visual_evidence[:300] if visual_evidence else 'No visual context',", "                'violation_type': 'Attire/Clothing Privacy Concern',", "                'speakers': speakers", "            })", "", "        # Check for handcuffing dignity concerns with attire context", "        if any(keyword in word_text for keyword in handcuff_dignity_keywords):", "            # Check if this occurs near attire violations", "            attire_context = any(attire_word in enhanced_transcript[max(0, i-10):i+10][j]['word'].lower()", "                               for j in range(len(enhanced_transcript[max(0, i-10):i+10]))", "                               for attire_word in ['towel', 'naked', 'undressed', 'wet'])", "", "            if attire_context:", "                dignity_violations.append({", "                    'timestamp': word_timestamp,", "                    'audio_evidence': word_text,", "                    'violation_type': 'Handcuffing Individual in Minimal Clothing - Dignity Violation',", "                    'speakers': speakers,", "                    'severity': 'HIGH',", "                    'constitutional_concern': '8th Amendment - Cruel and unusual punishment'", "                })", "", "        # Check for emergency exit situations", "        if any(keyword in word_text for keyword in emergency_exit_keywords):", "            privacy_violations.append({", "                'timestamp': word_timestamp,", "                'audio_evidence': word_text,", "                'violation_type': 'Emergency Exit Privacy Violation',", "                'speakers': speakers", "            })", "", "        # Check for dignity violations", "        if any(keyword in word_text for keyword in dignity_keywords):", "            dignity_violations.append({", "                'timestamp': word_timestamp,", "                'audio_evidence': word_text,", "                'violation_type': 'Dignity violation',", "                'speakers': word_data['speakers']", "            })", "", "    # Enhanced visual analysis for clothing/exposure", "    for ctx in visual_context:", "        visual_analysis = ctx['analysis'].lower()", "", "        # Check for clothing-related exposure", "        clothing_indicators = ['towel only', 'minimal clothing', 'partially dressed', 'undressed',", "                              'wet from shower', 'barefoot', 'rushed dressing', 'incomplete clothing']", "", "        if any(indicator in visual_analysis for indicator in clothing_indicators):", "            # Check if handcuffing is involved", "            handcuff_indicators = ['handcuff', 'cuff', 'restrain', 'hands behind back']", "            is_handcuffed = any(hc_word in visual_analysis for hc_word in handcuff_indicators)", "", "            # Check if in public view", "            public_indicators = ['public', 'neighbors', 'crowd', 'street', 'outside', 'porch', 'yard']", "            is_public = any(pub_word in visual_analysis for pub_word in public_indicators)", "", "            violation_type = 'Public Exposure - Minimal Clothing' if is_public else 'Private Exposure Documentation'", "", "            if is_handcuffed:", "                violation_type += ' + Restraint Applied'", "", "            public_exposure_incidents.append({", "                'timestamp': ctx['timestamp'],", "                'visual_evidence': ctx['analysis'],", "                'violation_type': violation_type,", "                'severity': 'CRITICAL' if (is_public and is_handcuffed) else 'HIGH' if is_public else 'MODERATE',", "                'clothing_status': 'MINIMAL/INADEQUATE',", "                'restraint_status': 'RESTRAINED' if is_handcuffed else 'UNRESTRAINED'", "            })", "", "        # Check for dignity violations", "        dignity_indicators = ['humiliating', 'embarrassing', 'inappropriate exposure',", "                             'forced to remain undressed', 'denied clothing']", "", "        if any(indicator in visual_analysis for indicator in dignity_indicators):", "            dignity_violations.append({", "                'timestamp': ctx['timestamp'],", "                'visual_evidence': ctx['analysis'],", "                'violation_type': 'Dignity Violation - Inappropriate Exposure',", "                'severity': 'HIGH'", "            })", "", "        # Check for emergency/crisis interruption", "        emergency_indicators = ['shower interrupted', 'rushed from bathroom', 'wet appearance',", "                               'emergency exit', 'hastily dressed', 'grabbed towel']", "", "        if any(indicator in visual_analysis for indicator in emergency_indicators):", "            privacy_violations.append({", "                'timestamp': ctx['timestamp'],", "                'visual_evidence': ctx['analysis'],", "                'violation_type': 'Emergency Privacy Interruption',", "                'context': 'Interrupted Private Activity'", "            })", "", "    # Analyze visual context for public exposure", "    public_exposure_incidents = []", "    for ctx in visual_context:", "        visual_analysis = ctx['analysis'].lower()", "        if any(keyword in visual_analysis for keyword in exposure_keywords):", "            if any(privacy_word in visual_analysis for privacy_word in ['exposed', 'undressed', 'strip']):", "                public_exposure_incidents.append({", "                    'timestamp': ctx['timestamp'],", "                    'visual_evidence': ctx['analysis'],", "                    'violation_type': 'Public exposure'", "                })", "", "    print(f\"✅ Found {len(attire_violations)} attire/clothing violations\")", "    print(f\"✅ Found {len(privacy_violations)} privacy violations\")", "    print(f\"✅ Found {len(dignity_violations)} dignity violations\")", "    print(f\"✅ Found {len(public_exposure_incidents)} public exposure incidents\")", "", "    return privacy_violations, dignity_violations, public_exposure_incidents, attire_violations", "", "def analyze_harassment_retaliation_patterns(enhanced_transcript, speaker_counts):", "    \"\"\"Analyze patterns of harassment or retaliation\"\"\"", "    print(\"⚠️ Analyzing harassment and retaliation patterns...\")", "", "    harassment_indicators = []", "    retaliation_patterns = []", "", "    # Harassment keywords", "    harassment_keywords = ['shut up', 'stupid', 'idiot', 'worthless', 'pathetic', 'loser', 'embarrass', 'loud'", "                           'humiliate', 'swat', 'loudspeaker']", "", "    # Retaliation keywords", "    retaliation_keywords = ['complained', 'lawyer', 'sue', 'rights', 'report', 'game', 'play', 'embarrass', 'loud'", "                           'humiliate', 'swat', 'loudspeaker']", "", "    # Power assertion keywords", "    power_keywords = ['because i said so', 'i am the law', 'do what i tell you', 'you will obey', 'embarrass', 'loud'", "                     'humiliate', 'swat', 'loudspeaker', 'shoot', 'hands up', 'cuff', 'detain', 'restrain',", "                     'rpo', 'risk protection']", "", "    # Track escalation after certain triggers", "    trigger_timestamps = []", "", "    for i, word_data in enumerate(enhanced_transcript):", "        word_timestamp = word_data['start']", "        word_text = word_data['word'].lower()", "        speakers = word_data['speakers']", "", "        # Check for harassment language", "        if any(keyword in word_text for keyword in harassment_keywords):", "            harassment_indicators.append({", "                'timestamp': word_timestamp,", "                'content': word_text,", "                'speakers': speakers,", "                'type': 'Verbal harassment'", "            })", "", "        # Check for retaliation triggers", "        if any(keyword in word_text for keyword in retaliation_keywords):", "            trigger_timestamps.append(word_timestamp)", "", "        # Check for power assertion", "        if any(keyword in word_text for keyword in power_keywords):", "            harassment_indicators.append({", "                'timestamp': word_timestamp,", "                'content': word_text,", "                'speakers': speakers,", "                'type': 'Power assertion'", "            })", "", "    # Analyze escalation patterns after triggers", "    for trigger_time in trigger_timestamps:", "        escalation_window = [word for word in enhanced_transcript", "                           if trigger_time < word['start'] < trigger_time + 300]  # 5 minutes after", "", "        if escalation_window:", "            force_words = ['force', 'taser', 'arrest', 'cuff', 'restrain']", "            escalation_count = sum(1 for word in escalation_window", "                                 if any(force_word in word['word'].lower() for force_word in force_words))", "", "            if escalation_count > 2:", "                retaliation_patterns.append({", "                    'trigger_timestamp': trigger_time,", "                    'escalation_period': '5 minutes',", "                    'escalation_indicators': escalation_count,", "                    'type': 'Post-complaint escalation'", "                })", "", "    print(f\"✅ Found {len(harassment_indicators)} harassment indicators\")", "    print(f\"✅ Found {len(retaliation_patterns)} retaliation patterns\")", "", "    return harassment_indicators, retaliation_patterns", "", "def analyze_misconduct_patterns(enhanced_transcript, visual_context):", "    \"\"\"Analyze patterns of coordinated misconduct\"\"\"", "    print(\"🕵️ Analyzing misconduct patterns...\")", "", "    narrative_shaping = []", "    coordinated_behavior = []", "    selective_enforcement = []", "", "    # Narrative shaping keywords", "    narrative_keywords = ['story', 'report', 'write up', 'document', 'official', 'record']", "    coaching_keywords = ['say', 'tell them', 'remember', 'stick to', 'version']", "", "    # Look for coordination between officers", "    officer_speakers = [speaker for speaker in set() for word in enhanced_transcript for speaker in word['speakers'] if 'officer' in str(speaker).lower()]", "", "    # Analyze for narrative coordination", "    for i, word_data in enumerate(enhanced_transcript):", "        word_text = word_data['word'].lower()", "        speakers = word_data['speakers']", "", "        if any(keyword in word_text for keyword in narrative_keywords):", "            if any(coach_word in word_text for coach_word in coaching_keywords):", "                narrative_shaping.append({", "                    'timestamp': word_data['start'],", "                    'content': word_text,", "                    'speakers': speakers,", "                    'type': 'Narrative coordination'", "                })", "", "    # Look for coordinated timing in visual evidence", "    officer_positioning_times = []", "    for ctx in visual_context:", "        if 'officer' in ctx['analysis'].lower() and 'position' in ctx['analysis'].lower():", "            officer_positioning_times.append(ctx['timestamp'])", "", "    # Check for coordinated positioning (multiple officers moving within short timeframe)", "    for i, time1 in enumerate(officer_positioning_times):", "        for time2 in officer_positioning_times[i+1:]:", "            if abs(time1 - time2) < 30:  # Within 30 seconds", "                coordinated_behavior.append({", "                    'timestamp_1': time1,", "                    'timestamp_2': time2,", "                    'type': 'Coordinated positioning',", "                    'time_difference': abs(time1 - time2)", "                })", "", "    print(f\"✅ Found {len(narrative_shaping)} narrative shaping incidents\")", "    print(f\"✅ Found {len(coordinated_behavior)} coordinated behavior patterns\")", "", "    return narrative_shaping, coordinated_behavior, selective_enforcement", "", "def generate_comprehensive_legal_analysis_document(", "    transcript_text, enhanced_transcript, visual_context,", "    compliance_violations, behavioral_contradictions,", "    privacy_violations, dignity_violations, public_exposure,", "    harassment_indicators, retaliation_patterns,", "    narrative_shaping, coordinated_behavior,", "    skip_seconds=30", "):", "    \"\"\"Generate comprehensive legal analysis document with all required sections\"\"\"", "", "    legal_analysis_prompt = f\"\"\"You are a certified forensic audiovisual analyst and constitutional law expert with 25+ years of experience serving as a court-appointed expert witness. Generate a comprehensive legal analysis document based on the integrated audio-visual evidence provided.", "", "STRUCTURE YOUR ANALYSIS WITH THESE MANDATORY SECTIONS:", "", "1. STATUTORY VIOLATIONS ANALYSIS:", "   - Florida Statute § 394.463 (Baker Act procedures)", "   - Florida Statute Chapter 901 (Arrest authority and procedures)", "   - Florida Statute § 776.05 (Law enforcement use of force)", "   - Florida Statute § 843.02 (Resisting arrest provisions)", "   - Florida Administrative Code 11B-27 (Mental health transport)", "   - Cite specific violations with timestamp evidence", "", "2. CONSTITUTIONAL VIOLATIONS ANALYSIS:", "   - 4th Amendment: Search and seizure violations, warrant requirements", "   - 5th Amendment: Miranda rights, self-incrimination issues", "   - 8th Amendment: Excessive force, cruel and unusual punishment", "   - 14th Amendment: Due process, equal protection violations", "   - Provide specific constitutional analysis with case law citations", "", "3. PROCEDURAL BREACHES ASSESSMENT:", "   - Required warnings not provided (Miranda, medical rights)", "   - Transport protocol violations", "   - Mental health criteria non-compliance", "   - Medical clearance timing violations", "   - Supervisor notification failures", "   - Chain of custody issues", "", "4. PATTERNS OF MISCONDUCT IDENTIFICATION:", "   - Evidence of coordinated narrative shaping: {len(narrative_shaping)} incidents", "   - Coordinated behavior patterns: {len(coordinated_behavior)} instances", "   - Retaliatory conduct indicators: {len(retaliation_patterns)} patterns", "   - Selective enforcement evidence", "", "5. PRIVACY & DIGNITY VIOLATIONS:", "   - Public exposure incidents: {len(public_exposure)} documented", "   - Privacy violations: {len(privacy_violations)} identified", "   - Dignity violations: {len(dignity_violations)} documented", "   - Inappropriate disclosure or humiliation tactics", "", "6. USE OF FORCE ASSESSMENT (<PERSON> v. Connor Analysis):", "   - Severity of crime factors", "   - Immediacy of threat assessment", "   - Actively resisting arrest evaluation", "   - Attempting to evade by flight analysis", "   - Totality of circumstances review", "   - Florida agency force protocol compliance", "", "7. HARASSMENT OR RETALIATION EVIDENCE:", "   - Harassment indicators: {len(harassment_indicators)} documented", "   - Personal animus evidence", "   - Power assertion tactics: documented instances", "   - Language indicating improper motive", "", "8. AUDIO-VISUAL CONTRADICTION ANALYSIS:", "   - Commands vs. compliance discrepancies: {len(compliance_violations)} violations", "   - Behavioral contradictions: {len(behavioral_contradictions)} identified", "   - Officer statements vs. visual evidence mismatches", "", "EVIDENCE PROVIDED:", "- Audio transcript: {len(transcript_text)} characters", "- Enhanced transcript: {len(enhanced_transcript)} words", "- Visual context points: {len(visual_context)} frames analyzed", "- Compliance violations: {compliance_violations}", "- Privacy violations: {privacy_violations}", "- Harassment patterns: {harassment_indicators}", "", "Provide specific timestamps, direct quotes, visual evidence references, statutory citations, constitutional analysis, and court-admissible conclusions for each section. Use Bluebook citation format where applicable.\"\"\"", "", "    try:", "        response = openai.ChatCompletion.create(", "            model=\"gpt-4\",", "            messages=[", "                {\"role\": \"system\", \"content\": \"You are a certified forensic legal analyst specializing in constitutional law, criminal procedure, and police misconduct analysis.\"},", "                {\"role\": \"user\", \"content\": legal_analysis_prompt}", "            ],", "            max_tokens=4000,", "            temperature=0.05", "        )", "", "        return response.choices[0].message.content", "", "    except Exception as e:", "        print(f\"❌ Comprehensive legal analysis failed: {e}\")", "        return f\"Comprehensive legal analysis unavailable: {e}\"", "", "# ENHANCED ATTIRE AND PRIVACY ANALYSIS", "# Add these functions to Cell 4 or replace existing functions", "", "def analyze_video_frames_for_context_enhanced_attire(video_path, skip_seconds=30):", "    \"\"\"Enhanced video analysis with specific attire and privacy detection\"\"\"", "    print(\"📹 Analyzing video frames with enhanced attire/privacy detection...\")", "", "    frames_dir = \"/content/video_frames\"", "    os.makedirs(frames_dir, exist_ok=True)", "", "    extract_frames_cmd = [", "        'ffmpeg', '-y', '-ss', str(skip_seconds), '-i', video_path,", "        '-vf', 'fps=1/20', '-q:v', '2', f'{frames_dir}/frame_%04d.jpg'", "    ]", "    subprocess.run(extract_frames_cmd, capture_output=True)", "", "    frame_files = sorted([f for f in os.listdir(frames_dir) if f.endswith('.jpg')])", "    visual_context = []", "", "    print(f\"🔍 Analyzing {len(frame_files)} video frames with attire focus...\")", "", "    for i, frame_file in enumerate(frame_files):", "        frame_path = os.path.join(frames_dir, frame_file)", "        timestamp = (i * 20) + skip_seconds", "", "        try:", "            with open(frame_path, 'rb') as f:", "                frame_data = base64.b64encode(f.read()).decode()", "", "            response = openai.ChatCompletion.create(", "                model=\"gpt-4o\",", "                messages=[", "                    {", "                        \"role\": \"user\",", "                        \"content\": [", "                            {", "                                \"type\": \"text\",", "                                \"text\": \"\"\"Conduct detailed forensic analysis of this police bodycam frame with SPECIFIC ATTENTION to clothing and privacy issues:", "", "1. ATTIRE ANALYSIS (CRITICAL FOR PRIVACY ASSESSMENT):", "   - Clothing status: Fully dressed, partially dressed, minimal clothing, towel only, etc.", "   - State of dress: Appropriate, inappropriate for public, emergency exit clothing", "   - Signs of rushed dressing: Incomplete clothing, towel wrapped, wet appearance", "   - Modesty concerns: Areas of body exposed, coverage inadequacy", "   - Footwear: Barefoot, shoes, slippers (indicates emergency exit)", "", "2. PRIVACY & DIGNITY INDICATORS:", "   - Public exposure level: Private home vs. public view", "   - Bystander presence: Neighbors, crowds, passersby witnessing exposure", "   - Recording implications: Subject aware of being filmed in state of undress", "   - Weather conditions affecting minimal clothing exposure", "", "3. EMERGENCY/CRISIS INDICATORS:", "   - Wet hair/body (shower interruption)", "   - Rushed appearance (hastily grabbed clothing/towel)", "   - Bathroom/shower context (wet floors, steam, towels visible)", "   - Time pressure indicators (incomplete dressing)", "", "4. RESTRAINT/HANDCUFFING ANALYSIS:", "   - Handcuff application on subject in minimal clothing", "   - Positioning: hands behind back while in towel/minimal clothing", "   - Dignity concerns during restraint application", "   - Cooperative behavior vs. restraint necessity", "", "5. STANDARD FORENSIC ELEMENTS:", "   - Scene setting and location context", "   - People positions and actions", "   - Equipment and evidence visible", "   - Officer positioning relative to undressed subject", "   - Safety and tactical considerations", "", "6. CONSTITUTIONAL CONCERNS:", "   - 4th Amendment: Privacy expectations in home", "   - 8th Amendment: Dignity during detention", "   - Public exposure creating humiliation", "   - Reasonable accommodation for clothing needs", "", "Be extremely specific about clothing status and privacy implications. Flag any dignitary concerns or inappropriate exposure situations. Document if subject appears to have been interrupted during private activities (bathing, dressing, etc.).\"\"\"", "                            },", "                            {", "                                \"type\": \"image_url\",", "                                \"image_url\": {", "                                    \"url\": f\"data:image/jpeg;base64,{frame_data}\"", "                                }", "                            }", "                        ]", "                    }", "                ],", "                max_tokens=600,", "                temperature=0.1", "            )", "", "            visual_analysis = response.choices[0].message.content", "            visual_context.append({", "                'timestamp': timestamp,", "                'frame': frame_file,", "                'analysis': visual_analysis", "            })", "", "            print(f\"✅ Enhanced frame analysis: {timestamp//60:02d}:{timestamp%60:02d}\")", "", "        except Exception as e:", "            print(f\"⚠️ Frame analysis failed for {frame_file}: {e}\")", "            visual_context.append({", "                'timestamp': timestamp,", "                'frame': frame_file,", "                'analysis': f\"Visual analysis unavailable: {e}\"", "            })", "", "    print(f\"✅ Enhanced visual context analysis complete: {len(visual_context)} frames\")", "    return visual_context", "", "def analyze_privacy_dignity_violations_enhanced(enhanced_transcript, visual_context, skip_seconds=30):", "    \"\"\"Enhanced privacy and dignity analysis with specific attire focus\"\"\"", "    print(\"🔒 Enhanced privacy and dignity violations analysis...\")", "", "    privacy_violations = []", "    dignity_violations = []", "    attire_violations = []", "    public_exposure_incidents = []", "", "    # Enhanced keywords for clothing/attire situations", "    attire_keywords = ['towel', 'naked', 'undressed', 'partially clothed', 'minimal clothing',", "                      'barefoot', 'wet', 'shower', 'bathing', 'bathroom', 'rushed', 'incomplete dress']", "", "    emergency_exit_keywords = ['rushed out', 'hurried', 'interrupted', 'grabbed towel',", "                              'wet hair', 'steam', 'bathroom door', 'shower interrupted']", "", "    exposure_keywords = ['exposed', 'visible', 'uncovered', 'inappropriate', 'public view',", "                        'neighbors seeing', 'crowd watching', 'filming', 'recording']", "", "    handcuff_dignity_keywords = ['handcuffed', 'cuffed', 'restrained', 'hands behind back',", "                                'restraints applied', 'detained']", "", "    # Analyze audio for clothing/exposure references", "    for i, word_data in enumerate(enhanced_transcript):", "        word_timestamp = word_data['start'] + skip_seconds", "        word_text = word_data['word'].lower()", "        speakers = word_data['speakers']", "", "        # Check for attire-related violations", "        if any(keyword in word_text for keyword in attire_keywords):", "            # Find corresponding visual context", "            visual_evidence = None", "            for ctx in visual_context:", "                if abs(ctx['timestamp'] - word_timestamp) <= 60:", "                    visual_evidence = ctx['analysis']", "                    break", "", "            attire_violations.append({", "                'timestamp': word_timestamp,", "                'audio_evidence': word_text,", "                'visual_evidence': visual_evidence[:300] if visual_evidence else 'No visual context',", "                'violation_type': 'Attire/Clothing Privacy Concern',", "                'speakers': speakers", "            })", "", "        # Check for handcuffing dignity concerns with attire context", "        if any(keyword in word_text for keyword in handcuff_dignity_keywords):", "            # Check if this occurs near attire violations", "            attire_context = any(attire_word in enhanced_transcript[max(0, i-10):i+10][j]['word'].lower()", "                               for j in range(len(enhanced_transcript[max(0, i-10):i+10]))", "                               for attire_word in ['towel', 'naked', 'undressed', 'wet'])", "", "            if attire_context:", "                dignity_violations.append({", "                    'timestamp': word_timestamp,", "                    'audio_evidence': word_text,", "                    'violation_type': 'Handcuffing Individual in Minimal Clothing - Dignity Violation',", "                    'speakers': speakers,", "                    'severity': 'HIGH',", "                    'constitutional_concern': '8th Amendment - Cruel and unusual punishment'", "                })", "", "        # Check for emergency exit situations", "        if any(keyword in word_text for keyword in emergency_exit_keywords):", "            privacy_violations.append({", "                'timestamp': word_timestamp,", "                'audio_evidence': word_text,", "                'violation_type': 'Emergency Exit Privacy Violation',", "                'speakers': speakers", "            })", "", "    # Enhanced visual analysis for clothing/exposure", "    for ctx in visual_context:", "        visual_analysis = ctx['analysis'].lower()", "", "        # Check for clothing-related exposure", "        clothing_indicators = ['towel only', 'minimal clothing', 'partially dressed', 'undressed',", "                              'wet from shower', 'barefoot', 'rushed dressing', 'incomplete clothing']", "", "        if any(indicator in visual_analysis for indicator in clothing_indicators):", "            # Check if handcuffing is involved", "            handcuff_indicators = ['handcuff', 'cuff', 'restrain', 'hands behind back']", "            is_handcuffed = any(hc_word in visual_analysis for hc_word in handcuff_indicators)", "", "            # Check if in public view", "            public_indicators = ['public', 'neighbors', 'crowd', 'street', 'outside', 'porch', 'yard']", "            is_public = any(pub_word in visual_analysis for pub_word in public_indicators)", "", "            violation_type = 'Public Exposure - Minimal Clothing' if is_public else 'Private Exposure Documentation'", "", "            if is_handcuffed:", "                violation_type += ' + Restraint Applied'", "", "            public_exposure_incidents.append({", "                'timestamp': ctx['timestamp'],", "                'visual_evidence': ctx['analysis'],", "                'violation_type': violation_type,", "                'severity': 'CRITICAL' if (is_public and is_handcuffed) else 'HIGH' if is_public else 'MODERATE',", "                'clothing_status': 'MINIMAL/INADEQUATE',", "                'restraint_status': 'RESTRAINED' if is_handcuffed else 'UNRESTRAINED'", "            })", "", "        # Check for dignity violations", "        dignity_indicators = ['humiliating', 'embarrassing', 'inappropriate exposure',", "                             'forced to remain undressed', 'denied clothing']", "", "        if any(indicator in visual_analysis for indicator in dignity_indicators):", "            dignity_violations.append({", "                'timestamp': ctx['timestamp'],", "                'visual_evidence': ctx['analysis'],", "                'violation_type': 'Dignity Violation - Inappropriate Exposure',", "                'severity': 'HIGH'", "            })", "", "        # Check for emergency/crisis interruption", "        emergency_indicators = ['shower interrupted', 'rushed from bathroom', 'wet appearance',", "                               'emergency exit', 'hastily dressed', 'grabbed towel']", "", "        if any(indicator in visual_analysis for indicator in emergency_indicators):", "            privacy_violations.append({", "                'timestamp': ctx['timestamp'],", "                'visual_evidence': ctx['analysis'],", "                'violation_type': 'Emergency Privacy Interruption',", "                'context': 'Interrupted Private Activity'", "            })", "", "    print(f\"✅ Found {len(attire_violations)} attire/clothing violations\")", "    print(f\"✅ Found {len(privacy_violations)} privacy violations\")", "    print(f\"✅ Found {len(dignity_violations)} dignity violations\")", "    print(f\"✅ Found {len(public_exposure_incidents)} public exposure incidents\")", "", "    return privacy_violations, dignity_violations, public_exposure_incidents, attire_violations", "", "def inject_attire_context_annotations(enhanced_transcript, visual_context, skip_seconds=30):", "    \"\"\"Inject specific attire and privacy context annotations\"\"\"", "    print(\"💉 Injecting attire and privacy context annotations...\")", "", "    attire_annotations = {}", "", "    for i, word_data in enumerate(enhanced_transcript):", "        word_timestamp = word_data['start'] + skip_seconds", "        word_text = word_data['word'].lower()", "", "        # Find corresponding visual context for this word", "        closest_visual = None", "        min_time_diff = float('inf')", "", "        for ctx in visual_context:", "            time_diff = abs(ctx['timestamp'] - word_timestamp)", "            if time_diff < min_time_diff and time_diff < 15:", "                min_time_diff = time_diff", "                closest_visual = ctx", "", "        if closest_visual:", "            visual_text = closest_visual['analysis'].lower()", "", "            # Check for specific attire situations", "            if any(indicator in visual_text for indicator in ['towel only', 'minimal clothing', 'undressed']):", "                attire_annotations[i] = \"*{ATTIRE CONCERN: Subject in minimal clothing/towel only - Privacy implications}*\"", "", "            elif any(indicator in visual_text for indicator in ['wet from shower', 'rushed from bathroom']):", "                attire_annotations[i] = \"*{EMERGENCY EXIT: Subject interrupted during private activity - Constitutional privacy concern}*\"", "", "            elif any(indicator in visual_text for indicator in ['handcuff', 'restrain']) and any(attire in visual_text for attire in ['towel', 'minimal', 'undressed']):", "                attire_annotations[i] = \"*{CRITICAL DIGNITY VIOLATION: Restraint applied to subject in minimal clothing - 8th Amendment concern}*\"", "", "            elif any(indicator in visual_text for indicator in ['public exposure', 'neighbors seeing']):", "                attire_annotations[i] = \"*{PUBLIC EXPOSURE: Inappropriate clothing status in public view - Dignity violation}*\"", "", "            elif any(indicator in visual_text for indicator in ['barefoot', 'incomplete dress']):", "                attire_annotations[i] = \"*{RUSHED DRESSING: Emergency exit indicators - Privacy interruption documented}*\"", "", "    print(f\"✅ Attire context annotations prepared: {len(attire_annotations)} annotations\")", "    return attire_annotations", "", "print(\"✅ Enhanced attire and privacy analysis functions loaded!\")", "def calculate_violation_severity_score(violation_type, context_factors):", "    \"\"\"Calculate severity score for violations based on multiple factors\"\"\"", "    ", "    base_scores = {", "        \"handcuffing_minimal_clothing\": 9,", "        \"public_exposure\": 8,", "        \"privacy_interruption\": 7,", "        \"excessive_force\": 9,", "        \"miranda_violation\": 8,", "        \"consent_violation\": 7,", "        \"dignity_violation\": 8,", "        \"harassment\": 7,", "        \"retaliation\": 8,", "        \"narrative_coordination\": 6", "    }", "    ", "    multipliers = {", "        \"multiple_witnesses\": 1.3,", "        \"recording_present\": 1.2,", "        \"vulnerable_individual\": 1.4,", "        \"mental_health_crisis\": 1.3,", "        \"cooperative_subject\": 1.5,", "        \"public_location\": 1.3,", "        \"repeated_behavior\": 1.4", "    }", "    ", "    # Get base score", "    base_score = base_scores.get(violation_type, 5)", "    ", "    # Apply multipliers", "    final_score = base_score", "    for factor, value in context_factors.items():", "        if value and factor in multipliers:", "            final_score *= multipliers[factor]", "    ", "    return min(10, round(final_score, 1))  # Cap at 10", "", "def generate_violation_timeline(violations_data, skip_seconds=30):", "    \"\"\"Generate chronological timeline of all violations\"\"\"", "    ", "    timeline_events = []", "    ", "    # Collect all violations with timestamps", "    for vtype, violations in violations_data.items():", "        for v in violations:", "            timestamp = v.get('timestamp', 0)", "            timeline_events.append({", "                'timestamp': timestamp,", "                'type': vtype,", "                'details': v,", "                'severity': v.get('severity', 'MODERATE')", "            })", "    ", "    # Sort by timestamp", "    timeline_events.sort(key=lambda x: x['timestamp'])", "    ", "    # Format timeline", "    timeline_str = \"CHRONOLOGICAL VIOLATION TIMELINE:\\n\"", "    timeline_str += \"=\"*50 + \"\\n\\n\"", "    ", "    for event in timeline_events:", "        time_str = str(timedelta(seconds=int(event['timestamp'] - skip_seconds)))", "        timeline_str += f\"[{time_str}] {event['type'].upper()}\\n\"", "        timeline_str += f\"   Severity: {event['severity']}\\n\"", "        ", "        # Add specific details based on type", "        details = event['details']", "        if 'audio_evidence' in details:", "            timeline_str += f\"   Audio: {details['audio_evidence']}\\n\"", "        if 'visual_evidence' in details:", "            timeline_str += f\"   Visual: {details['visual_evidence'][:100]}...\\n\"", "        if 'violation_type' in details:", "            timeline_str += f\"   Type: {details['violation_type']}\\n\"", "        ", "        timeline_str += \"\\n\"", "    ", "    return timeline_str", "", "def analyze_body_camera_muting_patterns(enhanced_transcript, skip_seconds=30):", "    \"\"\"Analyze patterns of body camera muting or deactivation\"\"\"", "    print(\"📹 Analyzing body camera muting patterns...\")", "    ", "    muting_incidents = []", "    mute_keywords = ['mute', 'turn off', 'camera off', 'stop recording', 'blue', 'deactivate']", "    ", "    for i, word_data in enumerate(enhanced_transcript):", "        word_timestamp = word_data['start'] + skip_seconds", "        word_text = word_data['word'].lower()", "        speakers = word_data['speakers']", "        ", "        if any(keyword in word_text for keyword in mute_keywords):", "            # Look for context around muting", "            context_start = max(0, i - 20)", "            context_end = min(len(enhanced_transcript), i + 20)", "            context_words = ' '.join([w['word'] for w in enhanced_transcript[context_start:context_end]])", "            ", "            muting_incidents.append({", "                'timestamp': word_timestamp,", "                'keyword': word_text,", "                'speakers': speakers,", "                'context': context_words,", "                'suspicious': any(word in context_words.lower() for word in ['don\\'t', 'stop', 'turn off', 'need to'])", "            })", "    ", "    print(f\"✅ Found {len(muting_incidents)} potential camera muting references\")", "    return muting_incidents", "", "def extract_officer_identities(enhanced_transcript, visual_context):", "    \"\"\"Extract and track officer identities throughout the encounter using BERT NER\"\"\"", "    print(\"👮 Extracting officer identities using BERT NER...\")", "    ", "    # Initialize BERT NER pipeline", "    try:", "        ner_pipeline = pipeline(\"ner\", model=\"dslim/bert-base-NER\", aggregation_strategy=\"simple\")", "    except Exception as e:", "        print(f\"⚠️ NER model loading failed: {e}\")", "        return {}", "    ", "    officer_data = {}", "    title_patterns = ['officer', 'sergeant', 'lieutenant', 'deputy', 'detective', 'captain']", "    ", "    # Build text chunks around officer titles for NER processing", "    text_chunks = []", "    chunk_metadata = []", "    ", "    for i, word_data in enumerate(enhanced_transcript):", "        word_text = word_data['word'].lower()", "        ", "        # Look for officer titles", "        if any(pattern in word_text for pattern in title_patterns):", "            # Extract context window (20 words before and after)", "            start_idx = max(0, i - 20)", "            end_idx = min(len(enhanced_transcript), i + 20)", "            ", "            # Build text chunk", "            chunk_words = [enhanced_transcript[j]['word'] for j in range(start_idx, end_idx)]", "            chunk_text = ' '.join(chunk_words)", "            ", "            text_chunks.append(chunk_text)", "            chunk_metadata.append({", "                'timestamp': word_data['start'],", "                'title_found': word_text,", "                'speakers': word_data['speakers']", "            })", "    ", "    # Process chunks with NER", "    for chunk_text, metadata in zip(text_chunks, chunk_metadata):", "        try:", "            # Run NER on chunk", "            entities = ner_pipeline(chunk_text)", "            ", "            # Extract person names near officer titles", "            for entity in entities:", "                if entity['entity_group'] == 'PER':  # Person entity", "                    name = entity['word'].strip()", "                    ", "                    # Clean up name (remove ## tokens from BERT)", "                    name = name.replace('##', '')", "                    ", "                    # Create unique officer ID", "                    officer_id = f\"{metadata['title_found']}_{name}\".upper()", "                    ", "                    if officer_id not in officer_data:", "                        officer_data[officer_id] = {", "                            'name': name,", "                            'title': metadata['title_found'],", "                            'first_mention': metadata['timestamp'],", "                            'mentions': [],", "                            'speakers': metadata['speakers']", "                        }", "                    ", "                    officer_data[officer_id]['mentions'].append({", "                        'timestamp': metadata['timestamp'],", "                        'context': chunk_text[:100]", "                    })", "                    ", "        except Exception as e:", "            print(f\"⚠️ NER processing error: {e}\")", "    ", "    # Also extract badge numbers and identifiers from visual analysis", "    badge_pattern = r'(?:badge|unit|car)\\s*#?\\s*(\\d+)'", "    ", "    for ctx in visual_context:", "        visual_text = ctx['analysis'].lower()", "        if 'officer' in visual_text or 'badge' in visual_text:", "            # Extract badge numbers", "            import re", "            badges = re.findall(badge_pattern, visual_text)", "            for badge in badges:", "                badge_id = f\"BADGE_{badge}\"", "                if badge_id not in officer_data:", "                    officer_data[badge_id] = {", "                        'badge_number': badge,", "                        'visual_timestamp': ctx['timestamp'],", "                        'visual_context': visual_text[:200]", "                    }", "    ", "    print(f\"✅ Identified {len(officer_data)} unique officer references\")", "    return officer_data", "", "def analyze_de_escalation_failures(enhanced_transcript, visual_context, skip_seconds=30):", "    \"\"\"Analyze de-escalation attempts and failures\"\"\"", "    print(\"📉 Analyzing de-escalation patterns...\")", "    ", "    de_escalation_events = []", "    escalation_events = []", "    ", "    # De-escalation keywords", "    de_escalation_keywords = ['calm down', 'relax', 'take a breath', 'easy', 'talk to me', ", "                             'help you', 'understand', 'listen', 'explain', 'work with']", "    ", "    # Escalation keywords", "    escalation_keywords = ['hands up', 'get down', 'don\\'t move', 'stop', 'now', ", "                          'do it', 'comply', 'force', 'taser', 'arrest', 'cuff']", "    ", "    # Track escalation trajectory", "    for i, word_data in enumerate(enhanced_transcript):", "        word_timestamp = word_data['start'] + skip_seconds", "        word_text = word_data['word'].lower()", "        speakers = word_data['speakers']", "        ", "        # Check for de-escalation attempts", "        if any(keyword in word_text for keyword in de_escalation_keywords):", "            # Look ahead to see if escalation follows", "            escalation_follows = False", "            for j in range(i+1, min(i+50, len(enhanced_transcript))):", "                if any(esc_word in enhanced_transcript[j]['word'].lower() for esc_word in escalation_keywords):", "                    escalation_follows = True", "                    break", "            ", "            de_escalation_events.append({", "                'timestamp': word_timestamp,", "                'text': word_text,", "                'speakers': speakers,", "                'followed_by_escalation': escalation_follows,", "                'effectiveness': 'FAILED' if escalation_follows else 'UNCLEAR'", "            })", "        ", "        # Check for escalation", "        if any(keyword in word_text for keyword in escalation_keywords):", "            escalation_events.append({", "                'timestamp': word_timestamp,", "                'text': word_text,", "                'speakers': speakers", "            })", "    ", "    # Analyze visual escalation indicators", "    for ctx in visual_context:", "        visual_text = ctx['analysis'].lower()", "        if any(indicator in visual_text for indicator in ['weapon drawn', 'aggressive stance', ", "                                                          'hands on weapon', 'tactical position']):", "            escalation_events.append({", "                'timestamp': ctx['timestamp'],", "                'type': 'VISUAL',", "                'evidence': ctx['analysis'][:200]", "            })", "    ", "    print(f\"✅ Found {len(de_escalation_events)} de-escalation attempts\")", "    print(f\"✅ Found {len(escalation_events)} escalation events\")", "    ", "    return de_escalation_events, escalation_events", "", "def generate_executive_summary_enhanced(all_violations, transcript_length):", "    \"\"\"Generate enhanced executive summary with key findings and recommendations\"\"\"", "    ", "    summary = \"EXECUTIVE SUMMARY - KEY FINDINGS:\\n\"", "    summary += \"=\"*50 + \"\\n\\n\"", "    ", "    # Calculate total violations", "    total_violations = sum(len(v) for v in all_violations.values())", "    ", "    # Identify most serious violations", "    critical_violations = []", "    for vtype, violations in all_violations.items():", "        for v in violations:", "            if v.get('severity') in ['CRITICAL', 'HIGH']:", "                critical_violations.append((vtype, v))", "    ", "    summary += f\"Total Violations Identified: {total_violations}\\n\"", "    summary += f\"Critical/High Severity: {len(critical_violations)}\\n\"", "    summary += f\"Transcript Coverage: {transcript_length} words analyzed\\n\\n\"", "    ", "    summary += \"MOST SERIOUS VIOLATIONS:\\n\"", "    summary += \"-\"*30 + \"\\n\"", "    for vtype, violation in critical_violations[:5]:  # Top 5", "        summary += f\"• {vtype}: {violation.get('violation_type', 'N/A')}\\n\"", "    ", "    summary += \"\\nRECOMMENDED IMMEDIATE ACTIONS:\\n\"", "    summary += \"-\"*30 + \"\\n\"", "    summary += \"1. Preserve all bodycam footage and evidence\\n\"", "    summary += \"2. Document witness statements\\n\"", "    summary += \"3. Photograph any injuries or property damage\\n\"", "    summary += \"4. File formal complaints with appropriate agencies\\n\"", "    summary += \"5. Consult with civil rights attorney\\n\\n\"", "    ", "    return summary", "", "print(\"✅ Enhanced violation analysis functions loaded!\")", "print(\"✅ Enhanced legal analysis functions loaded!\")", "print(\"✅ Enhanced forensic pipeline functions loaded successfully!\")", "", "# ============================================================================="], "execution_count": null}, {"cell_type": "code", "metadata": {}, "outputs": [], "source": ["# Cell 5: <PERSON><PERSON> Enhanced Speaker Diarization Pipeline", "# =============================================================================", "print(\"👥 Loading enhanced speaker diarization pipeline...\")", "", "try:", "    diarization_pipeline = Pipeline.from_pretrained(", "        \"pyannote/speaker-diarization-3.1\",", "        use_auth_token=HF_TOKEN", "    )", "    diarization_pipeline.to(torch.device(device))", "    print(\"✅ Enhanced speaker diarization pipeline loaded successfully!\")", "except Exception as e:", "    print(f\"❌ Failed to load speaker diarization: {e}\")", "    print(\"Please check your HuggingFace token permissions\")", "", "# ============================================================================="], "execution_count": null}, {"cell_type": "code", "metadata": {}, "outputs": [], "source": ["# Cell 6: Complete Enhanced Forensic Processing Function WITH ALL IMPROVEMENTS", "# =============================================================================", "def process_complete_enhanced_forensic_analysis(video_path, skip_seconds=30):", "    \"\"\"", "    Complete enhanced forensic pipeline with comprehensive legal analysis", "    \"\"\"", "    print(\"🏛️ ENHANCED CERTIFIED FORENSIC AUDIOVISUAL ANALYSIS\")", "    print(\"=\"*80)", "", "    # Steps 1-7: Same as before (audio extraction, enhancement, transcription, visual analysis, etc.)", "    audio_raw = \"/content/extracted_audio_raw.wav\"", "    extract_cmd = [", "        'ffmpeg', '-y', '-ss', str(skip_seconds), '-i', video_path,", "        '-acodec', 'pcm_s16le', '-ar', '16000', '-ac', '1', audio_raw", "    ]", "    subprocess.run(extract_cmd, capture_output=True)", "    print(f\"✅ Raw audio extracted (skipping first {skip_seconds} seconds)\")", "", "    audio_enhanced = \"/content/enhanced_forensic_audio_v2.wav\"", "    enhanced_audio_processing_for_difficult_sections(audio_raw, audio_enhanced)", "", "    whisper_result = transcribe_with_maximum_accuracy_enhanced(audio_enhanced)", "    visual_context = analyze_video_frames_for_context_enhanced_attire(video_path, skip_seconds)", "", "    print(\"👥 Running enhanced speaker diarization...\")", "    diarization_result = diarization_pipeline(audio_enhanced)", "", "    overlaps = detect_speaker_overlaps_and_separate_enhanced(audio_enhanced, diarization_result, whisper_result)", "    enhanced_transcript = combine_transcription_and_speakers_enhanced(whisper_result, diarization_result, overlaps)", "", "    # NEW: Comprehensive Legal Analysis Components", "    print(\"📋 Conducting comprehensive legal analysis...\")", "", "    # Cross-reference utterances with behavior", "    compliance_violations, behavioral_contradictions = cross_reference_utterances_with_behavior(", "        enhanced_transcript, visual_context, skip_seconds", "    )", "", "    # Privacy and dignity analysis", "    privacy_violations, dignity_violations, public_exposure, attire_violations = analyze_privacy_dignity_violations_enhanced(", "        enhanced_transcript, visual_context, skip_seconds", "    )", "", "    # Harassment and retaliation analysis", "    speaker_counts = {}", "    for word_data in enhanced_transcript:", "        for speaker in word_data.get('speakers', []):", "            speaker_counts[speaker] = speaker_counts.get(speaker, 0) + 1", "", "    harassment_indicators, retaliation_patterns = analyze_harassment_retaliation_patterns(", "        enhanced_transcript, speaker_counts", "    )", "", "    # Misconduct patterns analysis", "    narrative_shaping, coordinated_behavior, selective_enforcement = analyze_misconduct_patterns(", "        enhanced_transcript, visual_context", "    )", "    ", "    # NEW: Analyze body camera muting patterns", "    muting_incidents = analyze_body_camera_muting_patterns(enhanced_transcript, skip_seconds)", "    ", "    # NEW: Extract officer identities", "    officer_identities = extract_officer_identities(enhanced_transcript, visual_context)", "    ", "    # NEW: Analyze de-escalation failures", "    de_escalation_events, escalation_events = analyze_de_escalation_failures(", "        enhanced_transcript, visual_context, skip_seconds", "    )", "    ", "    # NEW: Analyze transcript confidence", "    confidence_stats = analyze_transcript_confidence_metrics(enhanced_transcript)", "", "    # Generate comprehensive legal analysis document", "    comprehensive_legal_analysis = generate_comprehensive_legal_analysis_document(", "        whisper_result['text'], enhanced_transcript, visual_context,", "        compliance_violations, behavioral_contradictions,", "        privacy_violations, dignity_violations, public_exposure,", "        harassment_indicators, retaliation_patterns,", "        narrative_shaping, coordinated_behavior,", "        skip_seconds", "    )", "", "    # Enhanced contextual annotations and visual injections", "    annotations = inject_contextual_annotations_enhanced(enhanced_transcript)", "    visual_injections = inject_visual_context_into_transcript(enhanced_transcript, visual_context, skip_seconds)", "    attire_annotations = inject_attire_context_annotations(enhanced_transcript, visual_context, skip_seconds)", "", "    # IMPROVED: Combine all annotations properly", "    all_annotations = {**annotations, **attire_annotations}", "", "    # Generate comprehensive output document", "    output_path = \"/content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt\"", "", "    with open(output_path, \"w\", encoding=\"utf-8\") as f:", "        f.write(\"COMPREHENSIVE FORENSIC LEGAL ANALYSIS DOCUMENT\\n\")", "        f.write(\"=\"*80 + \"\\n\\n\")", "", "        # Header and credentials", "        f.write(\"ANALYST CREDENTIALS & CERTIFICATION:\\n\")", "        f.write(\"- Certified forensic audiovisual analyst\\n\")", "        f.write(\"- 25+ years experience in criminal procedure\\n\")", "        f.write(\"- Constitutional law expert (42 U.S.C. § 1983)\\n\")", "        f.write(\"- Court-appointed expert witness\\n\")", "        f.write(\"- Integrated audio-visual evidence specialist\\n\")", "        f.write(\"- Privacy, dignity, and attire violation specialist\\n\")  # NEW", "        f.write(\"- Florida Statutes compliance specialist\\n\\n\")", "", "        # Case metadata", "        f.write(\"CASE METADATA:\\n\")", "        f.write(f\"- Source File: {video_path}\\n\")", "        f.write(f\"- Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\")", "        f.write(f\"- Technology: Enhanced Whisper Large-v3 + Pyannote + GPT-4 + GPT-4 Vision\\n\")", "        f.write(f\"- Timestamp Offset: +{skip_seconds} seconds\\n\")", "", "        duration = whisper_result.get('duration', 0)", "        if isinstance(duration, (int, float)):", "            f.write(f\"- Total Duration: {duration:.1f} seconds\\n\")", "        else:", "            f.write(f\"- Total Duration: {str(duration)} seconds\\n\")", "", "        f.write(f\"- Total Words Processed: {len(enhanced_transcript)}\\n\")", "        f.write(f\"- Visual Context Points: {len(visual_context)}\\n\\n\")", "", "        # EXECUTIVE SUMMARY OF VIOLATIONS", "        f.write(\"EXECUTIVE SUMMARY OF IDENTIFIED VIOLATIONS:\\n\")", "        f.write(\"=\"*55 + \"\\n\")", "        f.write(f\"• Compliance Violations: {len(compliance_violations)}\\n\")", "        f.write(f\"• Behavioral Contradictions: {len(behavioral_contradictions)}\\n\")", "        f.write(f\"• Privacy Violations: {len(privacy_violations)}\\n\")", "        f.write(f\"• Dignity Violations: {len(dignity_violations)}\\n\")", "        f.write(f\"• Public Exposure Incidents: {len(public_exposure)}\\n\")", "        f.write(f\"• Attire-Related Violations: {len(attire_violations)}\\n\")", "        f.write(f\"• Harassment Indicators: {len(harassment_indicators)}\\n\")", "        f.write(f\"• Retaliation Patterns: {len(retaliation_patterns)}\\n\")", "        f.write(f\"• Narrative Shaping Incidents: {len(narrative_shaping)}\\n\")", "        f.write(f\"• Coordinated Behavior Patterns: {len(coordinated_behavior)}\\n\")", "        f.write(f\"• Body Camera Muting References: {len(muting_incidents)}\\n\")", "        f.write(f\"• Speaker Overlaps: {len(overlaps)}\\n\\n\")", "        ", "        # NEW: Generate enhanced executive summary", "        all_violations = {", "            'compliance': compliance_violations,", "            'privacy': privacy_violations,", "            'dignity': dignity_violations,", "            'public_exposure': public_exposure,", "            'attire': attire_violations,", "            'harassment': harassment_indicators,", "            'retaliation': retaliation_patterns,", "            'narrative': narrative_shaping,", "            'muting': muting_incidents", "        }", "        executive_summary = generate_executive_summary_enhanced(all_violations, len(enhanced_transcript))", "        f.write(executive_summary)", "        f.write(\"\\n\")", "", "        # DETAILED VIOLATION ANALYSIS", "        f.write(\"DETAILED VIOLATION ANALYSIS:\\n\")", "        f.write(\"=\"*35 + \"\\n\\n\")", "", "        # Compliance violations", "        if compliance_violations:", "            f.write(\"COMMAND COMPLIANCE VIOLATIONS:\\n\")", "            f.write(\"-\"*35 + \"\\n\")", "            for i, violation in enumerate(compliance_violations, 1):", "                timestamp_str = str(timedelta(seconds=int(violation['timestamp'])))", "                f.write(f\"{i}. [{timestamp_str}] COMMAND: {violation['command']}\\n\")", "                f.write(f\"   SPEAKERS: {', '.join(violation['speakers'])}\\n\")", "                f.write(f\"   VISUAL EVIDENCE: {violation['visual_evidence']}\\n\")", "                f.write(f\"   VIOLATION TYPE: {violation['contradiction_type']}\\n\\n\")", "", "        # Privacy violations", "        if privacy_violations:", "            f.write(\"PRIVACY VIOLATIONS:\\n\")", "            f.write(\"-\"*20 + \"\\n\")", "            for i, violation in enumerate(privacy_violations, 1):", "                timestamp_str = str(timedelta(seconds=int(violation['timestamp'])))", "                f.write(f\"{i}. [{timestamp_str}] AUDIO: {violation['audio_evidence']}\\n\")", "                f.write(f\"   VISUAL: {violation['visual_evidence']}\\n\")", "                f.write(f\"   TYPE: {violation['violation_type']}\\n\\n\")", "", "        # NEW: Attire violations section", "        if attire_violations:", "            f.write(\"ATTIRE/CLOTHING PRIVACY CONCERNS:\\n\")", "            f.write(\"-\"*35 + \"\\n\")", "            for i, violation in enumerate(attire_violations, 1):", "                timestamp_str = str(timedelta(seconds=int(violation['timestamp'])))", "                f.write(f\"{i}. [{timestamp_str}] AUDIO: {violation['audio_evidence']}\\n\")", "                f.write(f\"   VISUAL: {violation['visual_evidence']}\\n\")", "                f.write(f\"   TYPE: {violation['violation_type']}\\n\")", "                f.write(f\"   SPEAKERS: {', '.join(violation['speakers'])}\\n\\n\")", "", "        # Dignity violations", "        if dignity_violations:", "            f.write(\"DIGNITY VIOLATIONS:\\n\")", "            f.write(\"-\"*20 + \"\\n\")", "            for i, violation in enumerate(dignity_violations, 1):", "                timestamp_str = str(timedelta(seconds=int(violation['timestamp'])))", "                f.write(f\"{i}. [{timestamp_str}] TYPE: {violation['violation_type']}\\n\")", "                f.write(f\"   AUDIO: {violation['audio_evidence']}\\n\")", "                f.write(f\"   SEVERITY: {violation.get('severity', 'MODERATE')}\\n\")", "                f.write(f\"   CONSTITUTIONAL: {violation.get('constitutional_concern', 'General dignity')}\\n\")", "                f.write(f\"   SPEAKERS: {', '.join(violation['speakers'])}\\n\\n\")", "", "        # Public exposure incidents WITH RESTRAINT STATUS", "        if public_exposure:", "            f.write(\"PUBLIC EXPOSURE INCIDENTS:\\n\")", "            f.write(\"-\"*30 + \"\\n\")", "            for i, incident in enumerate(public_exposure, 1):", "                timestamp_str = str(timedelta(seconds=int(incident['timestamp'])))", "                f.write(f\"{i}. [{timestamp_str}] TYPE: {incident['violation_type']}\\n\")", "                f.write(f\"   SEVERITY: {incident['severity']}\\n\")", "                f.write(f\"   CLOTHING STATUS: {incident['clothing_status']}\\n\")", "                f.write(f\"   RESTRAINT STATUS: {incident.get('restraint_status', 'UNKNOWN')}\\n\")  # NEW", "                f.write(f\"   VISUAL: {incident['visual_evidence'][:200]}...\\n\\n\")", "", "        # Harassment indicators", "        if harassment_indicators:", "            f.write(\"HARASSMENT & RETALIATION EVIDENCE:\\n\")", "            f.write(\"-\"*35 + \"\\n\")", "            for i, indicator in enumerate(harassment_indicators, 1):", "                timestamp_str = str(timedelta(seconds=int(indicator['timestamp'])))", "                f.write(f\"{i}. [{timestamp_str}] TYPE: {indicator['type']}\\n\")", "                f.write(f\"   CONTENT: {indicator['content']}\\n\")", "                f.write(f\"   SPEAKERS: {', '.join(indicator['speakers'])}\\n\\n\")", "", "        # Misconduct patterns", "        if narrative_shaping:", "            f.write(\"MISCONDUCT PATTERNS - NARRATIVE SHAPING:\\n\")", "            f.write(\"-\"*45 + \"\\n\")", "            for i, incident in enumerate(narrative_shaping, 1):", "                timestamp_str = str(timedelta(seconds=int(incident['timestamp'])))", "                f.write(f\"{i}. [{timestamp_str}] {incident['type']}\\n\")", "                f.write(f\"   CONTENT: {incident['content']}\\n\")", "                f.write(f\"   SPEAKERS: {', '.join(incident['speakers'])}\\n\\n\")", "        ", "        # NEW: Body camera muting analysis", "        if muting_incidents:", "            f.write(\"BODY CAMERA MUTING/DEACTIVATION REFERENCES:\\n\")", "            f.write(\"-\"*45 + \"\\n\")", "            for i, incident in enumerate(muting_incidents, 1):", "                timestamp_str = str(timedelta(seconds=int(incident['timestamp'])))", "                f.write(f\"{i}. [{timestamp_str}] KEYWORD: {incident['keyword']}\\n\")", "                f.write(f\"   SPEAKERS: {', '.join(incident['speakers'])}\\n\")", "                f.write(f\"   CONTEXT: {incident['context'][:100]}...\\n\")", "                if incident['suspicious']:", "                    f.write(f\"   ⚠️ SUSPICIOUS CONTEXT DETECTED\\n\")", "                f.write(\"\\n\")", "        ", "        # NEW: Generate violation timeline", "        violation_timeline = generate_violation_timeline(all_violations, skip_seconds)", "        f.write(\"\\n\" + violation_timeline + \"\\n\")", "        ", "        # NEW: De-escalation analysis", "        if de_escalation_events or escalation_events:", "            f.write(\"DE-ESCALATION AND ESCALATION ANALYSIS:\\n\")", "            f.write(\"=\"*40 + \"\\n\\n\")", "            ", "            if de_escalation_events:", "                f.write(\"DE-ESCALATION ATTEMPTS:\\n\")", "                f.write(\"-\"*25 + \"\\n\")", "                for event in de_escalation_events[:10]:  # First 10", "                    timestamp_str = str(timedelta(seconds=int(event['timestamp'])))", "                    f.write(f\"[{timestamp_str}] {event['text']}\\n\")", "                    f.write(f\"   Speakers: {', '.join(event['speakers'])}\\n\")", "                    f.write(f\"   Effectiveness: {event['effectiveness']}\\n\\n\")", "            ", "            if escalation_events:", "                f.write(\"\\nESCALATION EVENTS:\\n\")", "                f.write(\"-\"*20 + \"\\n\")", "                for event in escalation_events[:10]:  # First 10", "                    timestamp_str = str(timedelta(seconds=int(event['timestamp'])))", "                    if event.get('type') == 'VISUAL':", "                        f.write(f\"[{timestamp_str}] VISUAL ESCALATION\\n\")", "                        f.write(f\"   Evidence: {event['evidence']}\\n\\n\")", "                    else:", "                        f.write(f\"[{timestamp_str}] {event['text']}\\n\")", "                        f.write(f\"   Speakers: {', '.join(event['speakers'])}\\n\\n\")", "        ", "        # NEW: Audio quality report", "        audio_quality_report = generate_audio_quality_report(enhanced_transcript, overlaps, confidence_stats)", "        f.write(\"\\n\" + audio_quality_report + \"\\n\")", "        ", "        # NEW: Officer identities section", "        if officer_identities:", "            f.write(\"IDENTIFIED OFFICER INFORMATION:\\n\")", "            f.write(\"=\"*35 + \"\\n\")", "            for officer_id, data in officer_identities.items():", "                if 'name' in data:", "                    f.write(f\"\\n{officer_id}:\\n\")", "                    f.write(f\"   Name: {data['name']}\\n\")", "                    f.write(f\"   Title: {data['title']}\\n\")", "                    f.write(f\"   First Mention: {str(timedelta(seconds=int(data['first_mention'])))}\\n\")", "                    f.write(f\"   Total Mentions: {len(data['mentions'])}\\n\")", "                elif 'badge_number' in data:", "                    f.write(f\"\\n{officer_id}:\\n\")", "                    f.write(f\"   Badge Number: {data['badge_number']}\\n\")", "                    f.write(f\"   Visual Detection: {str(timedelta(seconds=int(data['visual_timestamp'])))}\\n\")", "            f.write(\"\\n\")", "", "        # Enhanced annotated transcript with all violations marked", "        f.write(\"ANNOTATED TRANSCRIPT WITH VIOLATION MARKERS:\\n\")", "        f.write(\"=\"*55 + \"\\n\\n\")", "", "        current_speaker = None", "        for i, word_data in enumerate(enhanced_transcript):", "            word_start = word_data['start'] + skip_seconds", "            word_text = word_data['word']", "            speakers = word_data['speakers']", "            is_overlap = word_data['overlap']", "", "            start_time = str(timedelta(seconds=int(word_start)))", "", "            # Check for violation markers", "            violation_markers = []", "", "            # Check compliance violations", "            for violation in compliance_violations:", "                if abs(violation['timestamp'] - word_start) < 5:", "                    violation_markers.append(f\"**COMPLIANCE VIOLATION: {violation['command']}**\")", "", "            # Check privacy violations", "            for violation in privacy_violations:", "                if abs(violation['timestamp'] - word_start) < 5:", "                    violation_markers.append(f\"**PRIVACY VIOLATION: {violation['violation_type']}**\")", "", "            # NEW: Check attire violations", "            for violation in attire_violations:", "                if abs(violation['timestamp'] - word_start) < 5:", "                    violation_markers.append(f\"**ATTIRE VIOLATION: {violation['violation_type']}**\")", "", "            # Check dignity violations", "            for violation in dignity_violations:", "                if abs(violation['timestamp'] - word_start) < 5:", "                    violation_markers.append(f\"**DIGNITY VIOLATION: {violation['violation_type']}**\")", "", "            # NEW: Check public exposure", "            for incident in public_exposure:", "                if abs(incident['timestamp'] - word_start) < 15:", "                    violation_markers.append(f\"**PUBLIC EXPOSURE: {incident['violation_type']}**\")", "", "            # Check harassment indicators", "            for indicator in harassment_indicators:", "                if abs(indicator['timestamp'] - word_start) < 2:", "                    violation_markers.append(f\"**HARASSMENT: {indicator['type']}**\")", "", "            # Write violation markers", "            for marker in violation_markers:", "                f.write(f\"\\n{marker}\\n\")", "", "            # Visual context injection", "            visual_injection = visual_injections.get(i, \"\")", "            if visual_injection:", "                f.write(f\"\\n{visual_injection}\\n\")", "", "            # Contextual annotations (including attire annotations)", "            annotation = all_annotations.get(i, \"\")", "            if annotation:", "                # IMPROVED: Handle list annotations", "                if isinstance(annotation, list):", "                    for tag in annotation:", "                       f.write(f\"{tag}\\n\")", "                else:", "                    f.write(f\"{annotation}\\n\")", "", "            # Transcript content", "            primary_speaker = speakers[0] if speakers else \"UNKNOWN\"", "", "            if is_overlap:", "                overlap_speakers = \", \".join(word_data.get('overlap_speakers', []))", "                f.write(f\"[{start_time}] **OVERLAP** ({overlap_speakers}): {word_text} \")", "            else:", "                if primary_speaker != current_speaker:", "                    f.write(f\"\\n[{start_time}] {primary_speaker}: \")", "                    current_speaker = primary_speaker", "                f.write(f\"{word_text} \")", "", "        # COMPREHENSIVE LEGAL ANALYSIS DOCUMENT", "        f.write(f\"\\n\\n{'='*80}\")", "        f.write(f\"\\nCOMPREHENSIVE LEGAL ANALYSIS DOCUMENT\")", "        f.write(f\"\\n{'='*80}\\n\\n\")", "        f.write(comprehensive_legal_analysis)", "        f.write(\"\\n\\n\")", "        ", "        # NEW: Add relevant case law references", "        f.write(\"RELEVANT CASE LAW REFERENCES:\\n\")", "        f.write(\"=\"*40 + \"\\n\")", "        for case, description in CASE_LAW_REFERENCES.items():", "            f.write(f\"• {case}: {description}\\n\")", "        f.write(\"\\n\")", "", "        # CERTIFICATION AND DISCLAIMERS", "        f.write(\"COMPREHENSIVE CERTIFICATION:\\n\")", "        f.write(\"=\"*30 + \"\\n\")", "        f.write(\"This comprehensive analysis conducted using enhanced forensic-grade protocols.\\n\")", "        f.write(\"Integrated audio-visual evidence analysis with behavioral correlation performed.\\n\")", "        f.write(\"Cross-referenced speaker utterances with observable behavior completed.\\n\")", "        f.write(\"Enhanced attire, privacy, and dignity violation analysis included.\\n\")  # NEW", "        f.write(\"Specific attention to restraint application on minimally clothed individuals.\\n\")  # NEW", "        f.write(\"Comprehensive statutory and constitutional violation analysis included.\\n\")", "        f.write(\"Privacy, dignity, harassment, and misconduct pattern analysis performed.\\n\")", "        f.write(\"Suitable for judicial and quasi-judicial proceedings.\\n\")", "        f.write(\"Zero tolerance for paraphrasing maintained.\\n\")", "        f.write(\"Expert human review required for court admissibility.\\n\\n\")", "", "        f.write(\"ASSUMPTIONS AND LIMITATIONS:\\n\")", "        f.write(\"1. Analysis based on available audio-visual evidence\\n\")", "        f.write(\"2. Speaker identification algorithmic - human verification recommended\\n\")", "        f.write(\"3. Visual analysis limited to extracted frames\\n\")", "        f.write(\"4. Legal analysis preliminary - full case review requires additional discovery\\n\")", "        f.write(\"5. Timestamp accuracy dependent on source file integrity\\n\")", "        f.write(\"6. Constitutional analysis based on current case law\\n\")", "", "    print(f\"✅ Comprehensive forensic legal analysis complete: {output_path}\")", "    return output_path", "", "print(\"✅ Updated comprehensive forensic processing function ready!\")", "", "# ============================================================================="], "execution_count": null}, {"cell_type": "code", "metadata": {}, "outputs": [], "source": ["# Cell 7: Execute Enhanced Forensic Analysis (UPDATE VIDEO PATH FOR EACH NEW VIDEO)", "# =============================================================================", "print(\"🚀 EXECUTING ENHANCED COMPLETE FORENSIC ANALYSIS...\")", "", "# 🔄 UPDATE THIS LINE FOR EACH NEW VIDEO:", "video_path = f\"/content/{video_filename}\"  # Uses the filename from Cell 2", "", "# 🔄 ADJUST SKIP_SECONDS FOR EACH VIDEO:", "# - Use 30 for videos with muted/silent beginnings (default)", "# - Use 0 for videos that start immediately with audio", "# - Adjust to any value based on when actual audio content begins", "SKIP_SECONDS = 30", "", "result_file = process_complete_enhanced_forensic_analysis(", "    video_path,", "    skip_seconds=SKIP_SECONDS", ")", "", "# Download the result", "from google.colab import files", "files.download(result_file)", "", "print(\"🎉 ENHANCED FORENSIC ANALYSIS COMPLETE!\")", "print(\"✅ Features included:\")", "print(\"   ✅ Enhanced Whisper Large-v3 with WhisperX (surgical precision accuracy)\")", "print(\"   ✅ Multi-pass audio enhancement (distant speakers, overlaps, shouting)\")", "print(\"   ✅ Enhanced Pyannote speaker diarization 3.1 (improved sensitivity)\")", "print(\"   ✅ GPT-4o Vision frame-by-frame visual analysis (20-second intervals)\")", "print(\"   ✅ Integrated audio-visual legal analysis with case law references\")", "print(\"   ✅ Visual context injections in transcript\")", "print(\"   ✅ Enhanced speaker overlap detection and formatting\")", "print(\"   ✅ Multi-layer contextual annotations with list support\")", "print(\"   ✅ Court-admissible forensic formatting\")", "print(\"   ✅ No censorship (all profanity preserved)\")", "print(\"   ✅ Multi-video processing capability\")", "print(\"   ✅ Enhanced attire and dignity violation detection\")", "print(\"   ✅ Comprehensive restraint analysis with severity scoring\")", "print(\"   ✅ Enhanced privacy protection assessment\")", "print(\"   ✅ Body camera muting/deactivation detection\")", "print(\"   ✅ De-escalation failure analysis\")", "print(\"   ✅ Chronological violation timeline\")", "print(\"   ✅ Executive summary with key findings\")", "print(\"   ✅ Audio quality and confidence metrics\")", "print(\"   ✅ Expanded legal trigger word detection\")", "print(\"   ✅ Case law references (<PERSON> v<PERSON>, etc.)\")", "print(\"   ✅ Violation severity scoring system\")", "print(\"   ✅ Enhanced executive summary with recommendations\")"], "execution_count": null}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": [], "collapsed_sections": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 0}