# SECURITY - BEST PRACTICES
- **Before deploying a node/web app, always use ```bash npm run audit```, and resolve any active vulnerabilities if possible, especially if they’re **high** or **critical** severity**
- **Use managed auth solutions like Clerk + read their docs carefully to ensure you are implementing auth correctly! Don’t forget to protect public endpoints with auth**
- **Always set up IP + user-based rate limiting, DDoS protection, firewalls, monitoring and analytics. Platforms like Vercel offer monitoring, analytics and a firewall but you should also consider using cloudflare and captchas for added security**
- **Always write test automation for parts of your codebase where the cost of failure is high (payments, subscription mgmt, usage tracking, etc.)**
- **When writing tests for third party integrations like Stripe, ALWAYS reference official examples and docs.**
- **Rate limit all api endpoints**
- **Use row level security always (RLS)**
- **Captcha on all auth routes/signup pages**
- **If using hosting solution like vercel, enable attack challenge on their WAF**