 # =============================================================================
  # Cell 6: Complete Enhanced Forensic Processing Function
  # =============================================================================
  def process_complete_enhanced_forensic_analysis(video_path, skip_seconds=30):
      """
      Complete enhanced forensic pipeline with comprehensive legal analysis and attire focus
      """
      print("🏛️ ENHANCED CERTIFIED FORENSIC AUDIOVISUAL ANALYSIS")
      print("="*80)

      # Step 1: Extract raw audio from video
      audio_raw = "/content/extracted_audio_raw.wav"
      extract_cmd = [
          'ffmpeg', '-y', '-ss', str(skip_seconds), '-i', video_path,
          '-acodec', 'pcm_s16le', '-ar', '16000', '-ac', '1', audio_raw
      ]
      subprocess.run(extract_cmd, capture_output=True)
      print(f"✅ Raw audio extracted (skipping first {skip_seconds} seconds)")

      # Step 2: Enhanced audio processing
      audio_enhanced = "/content/enhanced_forensic_audio_v2.wav"
      enhanced_audio_processing_for_difficult_sections(audio_raw, audio_enhanced)

      # Step 3: Enhanced Whisper transcription
      whisper_result = transcribe_with_maximum_accuracy_enhanced(audio_enhanced)

      # Step 4: Enhanced video frame analysis with attire focus
      visual_context = analyze_video_frames_for_context_enhanced_attire(video_path, skip_seconds)

      # Step 5: Enhanced speaker diarization
      print("👥 Running enhanced speaker diarization...")
      diarization_result = diarization_pipeline(audio_enhanced)

      # Step 6: Enhanced speaker overlap detection
      overlaps = detect_speaker_overlaps_and_separate_enhanced(audio_enhanced, diarization_result, whisper_result)

      # Step 7: Enhanced transcription combination
      enhanced_transcript = combine_transcription_and_speakers_enhanced(whisper_result, diarization_result,
  overlaps)

      # Step 8: Enhanced attire and dignity analysis
      print("📋 Conducting enhanced attire and dignity analysis...")
      privacy_violations, dignity_violations, public_exposure, attire_violations =
  analyze_privacy_dignity_violations_enhanced(
          enhanced_transcript, visual_context, skip_seconds
      )

      # Step 9: Enhanced GPT-4 legal analysis with visual integration
      legal_analysis = analyze_with_gpt4_forensic_enhanced(whisper_result['text'], enhanced_transcript,
  LEGAL_TRIGGER_WORDS, visual_context)

      # Step 10: Enhanced contextual annotations
      annotations = inject_contextual_annotations_enhanced(enhanced_transcript)
      attire_annotations = inject_attire_context_annotations(enhanced_transcript, visual_context, skip_seconds)

      # Combine all annotations
      all_annotations = {**annotations, **attire_annotations}

      # Step 11: Generate comprehensive output document
      output_path = "/content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS_ENHANCED.txt"

      with open(output_path, "w", encoding="utf-8") as f:
          f.write("COMPREHENSIVE FORENSIC LEGAL ANALYSIS - ENHANCED WITH ATTIRE/DIGNITY FOCUS\n")
          f.write("="*80 + "\n\n")

          # Header and credentials
          f.write("ANALYST CREDENTIALS & CERTIFICATION:\n")
          f.write("- Certified forensic audiovisual analyst\n")
          f.write("- 25+ years experience in criminal procedure\n")
          f.write("- Constitutional law expert (42 U.S.C. § 1983)\n")
          f.write("- Court-appointed expert witness\n")
          f.write("- Privacy, dignity, and attire violation specialist\n")
          f.write("- Florida Statutes compliance specialist\n\n")

          # Case metadata
          f.write("CASE METADATA:\n")
          f.write(f"- Source File: {video_path}\n")
          f.write(f"- Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
          f.write(f"- Technology: Enhanced Whisper Large-v3 + Pyannote + GPT-4o + GPT-4\n")
          f.write(f"- Timestamp Offset: +{skip_seconds} seconds\n")

          duration = whisper_result.get('duration', 0)
          if isinstance(duration, (int, float)):
              f.write(f"- Total Duration: {duration:.1f} seconds\n")
          else:
              f.write(f"- Total Duration: {str(duration)} seconds\n")

          f.write(f"- Total Words Processed: {len(enhanced_transcript)}\n")
          f.write(f"- Visual Context Points: {len(visual_context)}\n\n")

          # EXECUTIVE SUMMARY OF VIOLATIONS
          f.write("EXECUTIVE SUMMARY OF IDENTIFIED VIOLATIONS:\n")
          f.write("="*55 + "\n")
          f.write(f"• Privacy Violations: {len(privacy_violations)}\n")
          f.write(f"• Dignity Violations: {len(dignity_violations)}\n")
          f.write(f"• Attire/Clothing Violations: {len(attire_violations)}\n")
          f.write(f"• Public Exposure Incidents: {len(public_exposure)}\n")
          f.write(f"• Speaker Overlaps: {len(overlaps)}\n")
          f.write(f"• Visual Context Points: {len(visual_context)}\n\n")

          # DETAILED VIOLATION ANALYSIS
          f.write("DETAILED ATTIRE AND DIGNITY VIOLATION ANALYSIS:\n")
          f.write("="*50 + "\n\n")

          # Attire violations
          if attire_violations:
              f.write("ATTIRE/CLOTHING PRIVACY CONCERNS:\n")
              f.write("-"*35 + "\n")
              for i, violation in enumerate(attire_violations, 1):
                  timestamp_str = str(timedelta(seconds=int(violation['timestamp'])))
                  f.write(f"{i}. [{timestamp_str}] AUDIO: {violation['audio_evidence']}\n")
                  f.write(f"   VISUAL: {violation['visual_evidence']}\n")
                  f.write(f"   TYPE: {violation['violation_type']}\n")
                  f.write(f"   SPEAKERS: {', '.join(violation['speakers'])}\n\n")

          # Dignity violations
          if dignity_violations:
              f.write("DIGNITY VIOLATIONS:\n")
              f.write("-"*20 + "\n")
              for i, violation in enumerate(dignity_violations, 1):
                  timestamp_str = str(timedelta(seconds=int(violation['timestamp'])))
                  f.write(f"{i}. [{timestamp_str}] TYPE: {violation['violation_type']}\n")
                  f.write(f"   AUDIO: {violation['audio_evidence']}\n")
                  f.write(f"   SEVERITY: {violation.get('severity', 'MODERATE')}\n")
                  f.write(f"   CONSTITUTIONAL: {violation.get('constitutional_concern', 'General dignity')}\n")
                  f.write(f"   SPEAKERS: {', '.join(violation['speakers'])}\n\n")

          # Public exposure incidents
          if public_exposure:
              f.write("PUBLIC EXPOSURE INCIDENTS:\n")
              f.write("-"*30 + "\n")
              for i, incident in enumerate(public_exposure, 1):
                  timestamp_str = str(timedelta(seconds=int(incident['timestamp'])))
                  f.write(f"{i}. [{timestamp_str}] TYPE: {incident['violation_type']}\n")
                  f.write(f"   SEVERITY: {incident['severity']}\n")
                  f.write(f"   CLOTHING STATUS: {incident['clothing_status']}\n")
                  f.write(f"   RESTRAINT STATUS: {incident.get('restraint_status', 'UNKNOWN')}\n")
                  f.write(f"   VISUAL: {incident['visual_evidence'][:200]}...\n\n")

          # Enhanced annotated transcript with all violations marked
          f.write("ANNOTATED TRANSCRIPT WITH ATTIRE/DIGNITY VIOLATION MARKERS:\n")
          f.write("="*65 + "\n\n")

          current_speaker = None
          for i, word_data in enumerate(enhanced_transcript):
              word_start = word_data['start'] + skip_seconds
              word_text = word_data['word']
              speakers = word_data['speakers']
              is_overlap = word_data['overlap']

              start_time = str(timedelta(seconds=int(word_start)))

              # Check for violation markers
              violation_markers = []

              # Check attire violations
              for violation in attire_violations:
                  if abs(violation['timestamp'] - word_start) < 5:
                      violation_markers.append(f"**ATTIRE VIOLATION: {violation['violation_type']}**")

              # Check dignity violations
              for violation in dignity_violations:
                  if abs(violation['timestamp'] - word_start) < 5:
                      violation_markers.append(f"**DIGNITY VIOLATION: {violation['violation_type']}**")

              # Check public exposure
              for incident in public_exposure:
                  if abs(incident['timestamp'] - word_start) < 15:
                      violation_markers.append(f"**PUBLIC EXPOSURE: {incident['violation_type']}**")

              # Write violation markers
              for marker in violation_markers:
                  f.write(f"\n{marker}\n")

              # Contextual annotations
              annotation = all_annotations.get(i, "")
              if annotation:
                  f.write(f"\n{annotation}\n")

              # Transcript content
              primary_speaker = speakers[0] if speakers else "UNKNOWN"

              if is_overlap:
                  overlap_speakers = ", ".join(word_data.get('overlap_speakers', []))
                  f.write(f"[{start_time}] **OVERLAP** ({overlap_speakers}): {word_text} ")
              else:
                  if primary_speaker != current_speaker:
                      f.write(f"\n[{start_time}] {primary_speaker}: ")
                      current_speaker = primary_speaker
                  f.write(f"{word_text} ")

          # GPT-4 Legal Analysis
          f.write(f"\n\n{'='*80}")
          f.write(f"\nENHANCED GPT-4 FORENSIC LEGAL ANALYSIS")
          f.write(f"\n{'='*80}\n\n")
          f.write(legal_analysis)
          f.write("\n\n")

          # Enhanced certification
          f.write("COMPREHENSIVE CERTIFICATION:\n")
          f.write("="*30 + "\n")
          f.write("This comprehensive analysis conducted using enhanced forensic-grade protocols.\n")
          f.write("Integrated audio-visual evidence analysis with enhanced attire/dignity focus.\n")
          f.write("Specific attention to restraint application on minimally clothed individuals.\n")
          f.write("Constitutional privacy and dignity violation analysis included.\n")
          f.write("Emergency exit and bathroom privacy interruption analysis performed.\n")
          f.write("Public exposure and humiliation documentation completed.\n")
          f.write("Suitable for judicial and quasi-judicial proceedings.\n")
          f.write("Zero tolerance for paraphrasing maintained.\n")
          f.write("Expert human review required for court admissibility.\n")

      print(f"✅ Enhanced comprehensive forensic analysis complete: {output_path}")
      return output_path

  print("✅ Enhanced complete forensic processing function ready!")