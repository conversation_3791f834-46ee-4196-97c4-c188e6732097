# PART 2: Additional Functions and Main Processing

# Add to Cell 4 (continued):

def detect_speaker_overlaps_and_separate_enhanced(audio_path, diarization_result, whisper_result):
    """Enhanced speaker overlap detection"""
    print("👥 Enhanced speaker overlap detection...")
    
    overlaps = []
    
    # Convert diarization to list of segments
    diar_segments = []
    for turn, _, speaker in diarization_result.itertracks(yield_label=True):
        diar_segments.append({
            'start': turn.start,
            'end': turn.end,
            'speaker': speaker
        })
    
    # Find overlapping segments
    for i, seg1 in enumerate(diar_segments):
        for seg2 in diar_segments[i+1:]:
            overlap_start = max(seg1['start'], seg2['start'])
            overlap_end = min(seg1['end'], seg2['end'])
            
            if overlap_start < overlap_end:
                duration = overlap_end - overlap_start
                if duration > 0.4:
                    overlaps.append({
                        'start': overlap_start,
                        'end': overlap_end,
                        'duration': duration,
                        'speakers': [seg1['speaker'], seg2['speaker']]
                    })
    
    print(f"✅ Overlap detection complete: {len(overlaps)} overlaps found")
    return overlaps

def combine_transcription_and_speakers_enhanced(whisper_result, diarization_result, overlaps):
    """Enhanced combination with word-level speaker assignment"""
    print("🔗 Combining transcription with speaker identification...")
    
    enhanced_transcript = []
    
    for segment in whisper_result['segments']:
        for word_info in segment.get('words', []):
            word_start = word_info['start']
            word_end = word_info['end']
            word_text = word_info['word']
            word_confidence = word_info.get('probability', 0.0)
            
            # Find speakers for this word
            speakers = []
            tolerance = 0.1
            
            for turn, _, speaker in diarization_result.itertracks(yield_label=True):
                if (turn.start - tolerance) <= word_start <= (turn.end + tolerance):
                    speakers.append(speaker)
            
            # Check for overlaps
            is_overlap = False
            overlap_speakers = []
            for overlap in overlaps:
                if overlap['start'] <= word_start <= overlap['end']:
                    is_overlap = True
                    overlap_speakers = overlap['speakers']
                    break
            
            enhanced_transcript.append({
                'start': word_start,
                'end': word_end,
                'word': word_text,
                'confidence': word_confidence,
                'speakers': speakers if speakers else ['UNKNOWN'],
                'overlap': is_overlap,
                'overlap_speakers': overlap_speakers
            })
    
    print(f"✅ Enhanced transcript created: {len(enhanced_transcript)} words")
    return enhanced_transcript

def inject_visual_context_into_transcript(enhanced_transcript, visual_context, skip_seconds=30):
    """
    Inject visual context at appropriate timestamps
    FIXED: Now actually creates contextual injections
    """
    print("💉 Injecting visual context into transcript...")
    
    visual_injections = {}
    
    for ctx in visual_context:
        visual_timestamp = ctx['timestamp']
        
        # Find closest word in transcript
        closest_word_index = None
        min_time_diff = float('inf')
        
        for i, word_data in enumerate(enhanced_transcript):
            word_timestamp = word_data['start'] + skip_seconds
            time_diff = abs(word_timestamp - visual_timestamp)
            
            if time_diff < min_time_diff and time_diff < 15:  # Within 15 seconds
                min_time_diff = time_diff
                closest_word_index = i
        
        if closest_word_index is not None:
            # Extract key information from visual analysis
            analysis_text = ctx.get('analysis', '')
            if isinstance(analysis_text, dict):
                analysis_text = analysis_text.get('raw_text', str(analysis_text))
            
            # Create contextual injection
            injection_text = f"\n[VISUAL CONTEXT at {visual_timestamp//60:02d}:{visual_timestamp%60:02d}]: "
            
            # Extract key visual elements
            if "towel" in analysis_text.lower():
                injection_text += "Subject in towel only. "
            if "handcuff" in analysis_text.lower():
                injection_text += "Handcuffs visible. "
            if "weapon" in analysis_text.lower() or "gun" in analysis_text.lower():
                injection_text += "Weapons displayed. "
            if "multiple officers" in analysis_text.lower():
                injection_text += "Multiple officers present. "
            if "neighbor" in analysis_text.lower() or "bystander" in analysis_text.lower():
                injection_text += "Public exposure with witnesses. "
            
            # Add first 200 chars of analysis
            injection_text += f"Details: {analysis_text[:200]}..."
            
            visual_injections[closest_word_index] = injection_text
    
    print(f"✅ Visual context injections prepared: {len(visual_injections)} injections")
    return visual_injections

def process_transcript_chunks_with_rate_limiting(enhanced_transcript, skip_seconds=30, chunk_delay=20):
    """
    Process transcript in chunks with rate limit handling
    - 20 second delay between chunks
    - Progressive saving
    - Automatic retry on rate limit
    """
    print("\n📄 Preparing transcript chunks for analysis...")
    
    # Create chunks
    chunks = []
    current_chunk = []
    current_size = 0
    max_chunk_size = 5000  # Conservative size to stay under token limits
    
    for word_data in enhanced_transcript:
        word_timestamp = word_data['start'] + skip_seconds
        word_text = word_data['word']
        speakers = word_data.get('speakers', [])
        primary_speaker = speakers[0] if speakers else "UNKNOWN"
        timestamp_str = str(timedelta(seconds=int(word_timestamp)))
        
        line = f"[{timestamp_str}] {primary_speaker}: {word_text} "
        line_size = len(line)
        
        if current_size + line_size > max_chunk_size and current_chunk:
            chunks.append(''.join(current_chunk))
            current_chunk = [line]
            current_size = line_size
        else:
            current_chunk.append(line)
            current_size += line_size
    
    if current_chunk:
        chunks.append(''.join(current_chunk))
    
    print(f"✅ Created {len(chunks)} chunks for analysis")
    
    # Progressive save of chunks
    chunks_path = "/content/transcript_chunks.json"
    with open(chunks_path, 'w') as f:
        json.dump(chunks, f, indent=2)
    
    print("📥 Downloading transcript chunks...")
    files.download(chunks_path)
    
    return chunks

def analyze_chunks_with_gpt4(chunks, violations_summary, chunk_delay=20):
    """
    Analyze chunks with GPT-4 including rate limit handling
    """
    print(f"\n⚖️ Analyzing {len(chunks)} chunks with GPT-4...")
    print(f"⏱️ Using {chunk_delay} second delay between chunks")
    
    chunk_analyses = []
    
    for i, chunk in enumerate(chunks):
        print(f"\n🔄 Analyzing chunk {i+1}/{len(chunks)}...")
        
        retry_count = 0
        max_retries = 3
        success = False
        
        while retry_count < max_retries and not success:
            try:
                response = openai.ChatCompletion.create(
                    model="gpt-4",
                    messages=[
                        {
                            "role": "system",
                            "content": """You are a senior forensic analyst specializing in law enforcement interactions.
                            Analyze transcript sections for legal violations with focus on:
                            - Constitutional violations (4th, 5th, 8th, 14th Amendment)
                            - Privacy/dignity violations (especially minimal clothing/towel)
                            - Mental health crisis handling
                            - Use of force and procedural violations"""
                        },
                        {
                            "role": "user",
                            "content": f"""Analyze transcript chunk {i+1} of {len(chunks)}:

{chunk}

Current violations found: {violations_summary}

Identify specific violations with exact quotes and timestamps.
Focus on: handcuffing in towel, public exposure, mental health mishandling."""
                        }
                    ],
                    max_tokens=1200,
                    temperature=0.1
                )
                
                analysis = response.choices[0].message.content
                chunk_analyses.append(analysis)
                print(f"✅ Chunk {i+1} analyzed successfully")
                success = True
                
                # Progressive save every 5 chunks
                if (i + 1) % 5 == 0:
                    partial_path = f"/content/analysis_chunks_1-{i+1}.json"
                    with open(partial_path, 'w') as f:
                        json.dump(chunk_analyses, f, indent=2)
                    print(f"📥 Downloading analyses for chunks 1-{i+1}...")
                    files.download(partial_path)
                
            except openai.error.RateLimitError as e:
                retry_count += 1
                wait_time = 20  # Default
                
                # Try to parse wait time from error
                error_msg = str(e)
                if "Please try again in" in error_msg:
                    try:
                        wait_time = float(error_msg.split("Please try again in ")[1].split("s")[0]) + 2
                    except:
                        wait_time = 20
                
                if retry_count < max_retries:
                    print(f"⏳ Rate limit hit. Waiting {wait_time:.1f} seconds...")
                    time.sleep(wait_time)
                else:
                    print(f"❌ Chunk {i+1} failed after {max_retries} retries")
                    chunk_analyses.append(f"Analysis failed due to rate limit")
                    
            except Exception as e:
                print(f"❌ Chunk {i+1} analysis error: {e}")
                chunk_analyses.append(f"Analysis error: {str(e)}")
                success = True  # Move on
        
        # Delay between chunks (even successful ones)
        if i < len(chunks) - 1:  # Don't delay after last chunk
            print(f"⏱️ Waiting {chunk_delay} seconds before next chunk...")
            time.sleep(chunk_delay)
    
    # Save final analyses
    final_path = "/content/all_chunk_analyses.json"
    with open(final_path, 'w') as f:
        json.dump(chunk_analyses, f, indent=2)
    
    print("\n📥 Downloading complete chunk analyses...")
    files.download(final_path)
    
    return chunk_analyses

# More functions continue in Part 3...
print("✅ Part 2 functions loaded. Continue to next part...")