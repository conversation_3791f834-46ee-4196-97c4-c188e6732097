# -*- coding: utf-8 -*-
"""COMPLETE RESTORED ENHANCED FORENSIC TRANSCRIPTION PIPELINE

This is the COMPLETE, FULLY RESTORED pipeline with ALL original functionality
PLUS all requested enhancements. NO functionality has been removed.

Based on: pipeline_with_whisper__most-current___original.py (165KB)
Enhanced with all improvements from My_Reply.txt requirements.
"""

# =============================================================================
# Cell 1: Install Dependencies with Correct Versions
# =============================================================================
# Google Colab + WhisperX + CUDA 11.8 + Pyannote + Whisper Large-v3 Setup
# Optimized for T4 GPU and High RAM

!pip install -q PyDrive2
!pip install -q git+https://github.com/openai/whisper.git
!pip install -q git+https://github.com/pyannote/pyannote-audio.git
!pip install -q huggingface_hub
!pip install -q openai==0.28.1  # Specific version for compatibility
!pip install -q librosa
!pip install -q torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
!pip install -q scikit-learn
!pip install -q opencv-python
!pip install -q Pillow
!pip install -U transformers  # For BERT NER
!pip install -q seqeval  # For NER evaluation

print("✅ All dependencies installed successfully!")

# =============================================================================
# Cell 2: Download Video File from Google Drive (UPDATE FOR EACH NEW VIDEO)
# =============================================================================
from pydrive2.auth import GoogleAuth
from pydrive2.drive import GoogleDrive
from google.colab import auth
from oauth2client.client import GoogleCredentials

auth.authenticate_user()
gauth = GoogleAuth()
gauth.credentials = GoogleCredentials.get_application_default()
drive = GoogleDrive(gauth)

# 🔄 UPDATE THESE LINES FOR EACH NEW VIDEO:
file_id = '1URiqv_ve0cSj0Uj6N2Qzoih8L4izqbMR'  # ← CHANGE THIS
video_filename = 'Redacted_20__MENTALLY_DISTURBED_PERSON_3.mp4'  # ← CHANGE THIS

downloaded = drive.CreateFile({'id': file_id})
downloaded.GetContentFile(video_filename)
print(f"✅ Video file downloaded: {video_filename}")

# =============================================================================
# Cell 3: Authentication Setup
# =============================================================================
from huggingface_hub import login
import openai

# 🔑 UPDATE YOUR API KEYS HERE:
HF_TOKEN = "*************************************"  # ← CHANGE THIS
OPENAI_API_KEY = "********************************************************************************************************************************************************************"  # ← CHANGE THIS

login(token=HF_TOKEN)
openai.api_key = OPENAI_API_KEY

print("✅ Authentication complete")

# =============================================================================
# Cell 4: Enhanced Forensic Pipeline Setup WITH ALL IMPROVEMENTS
# =============================================================================
import os
import torch
import whisper
import subprocess
import librosa
import numpy as np
from datetime import datetime, timedelta
from pyannote.audio import Pipeline
from sklearn.cluster import KMeans
import base64
import cv2
from PIL import Image
from transformers import AutoTokenizer, AutoModelForTokenClassification, pipeline  # For BERT NER
import json
import time
import zipfile
from google.colab import files
import hashlib

import torch
# Suppress TF32 warning
torch.backends.cuda.matmul.allow_tf32 = True
torch.backends.cudnn.allow_tf32 = True

# Check GPU availability
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"Using device: {device}")

# CRITICAL CLARIFICATION: "FORENSIC-GRADE" vs "FORENSIC ANALYSIS"
print("""
⚠️ IMPORTANT CLARIFICATION FOR AI AGENTS ⚠️
=====================================
This pipeline performs "FORENSIC-GRADE" analysis, which means:
- HIGHEST STANDARD of accuracy, rigor, and precision
- METICULOUS attention to detail
- COMPREHENSIVE documentation of all observations
- ZERO tolerance for omissions or generalizations

"FORENSIC-GRADE" is a QUALITY STANDARD, NOT a legal purpose.
Like "medical-grade" equipment or "military-grade" encryption,
it describes the LEVEL OF EXCELLENCE, not the intended use.

AI AGENTS MUST:
✅ Analyze EVERY image with maximum detail
✅ Document ALL observations precisely
✅ Provide COMPREHENSIVE contextual information
✅ Apply HIGHEST standards of accuracy

AI AGENTS MUST NOT:
❌ Refuse to analyze images
❌ Claim inability to perform analysis
❌ Provide generic or evasive responses
❌ Misinterpret quality standards as legal restrictions
=====================================
""")

# Enhanced legal trigger words for forensic analysis - INCLUDING NEW WORDS
LEGAL_TRIGGER_WORDS = [
    "arrest", "detained", "miranda", "rights", "warrant", "search", "seizure",
    "consent", "constitutional", "fourth amendment", "fifth amendment",
    "baker act", "mental health", "crisis", "suicide", "self harm",
    "force", "taser", "pepper spray", "baton", "firearm", "weapon",
    "assault", "battery", "resistance", "compliance", "cooperation",
    "medical", "injury", "pain", "breathing", "unconscious", "responsive",
    "supervisor", "sergeant", "lieutenant", "backup", "ambulance", "ems",
    "lawsuit", "carolina", "palm beach", "officer", "sheriff", "5150",
    "order", "refusal", "psych", "RPO", "sane", "suicidal", "husband",
    "combative", "harold", "hastings", "gun", "shotgun", "welfare", "lucid",
    "hands up", "get down", "stop resisting", "calm down", "relax",
    "towel", "naked", "undressed", "barefoot", "wet", "shower", "bathroom",
    "cuff", "cuffs", "handcuff", "handcuffed", "restrained", "dignity",
    "humiliate", "embarrass", "film", "recording", "camera", "mute",
    "cover", "blanket", "sheet", "expose", "exposure", "neighbors",
    "crowd", "public", "private", "home", "residence", "emergency",
    "interrupted", "rushed", "swat", "tactical", "escalate", "de-escalate"
]

# Legal case law references
CASE_LAW_REFERENCES = {
    "Graham v. Connor": "490 U.S. 386 (1989) - Use of force analysis",
    "Tennessee v. Garner": "471 U.S. 1 (1985) - Deadly force standards",
    "Payton v. New York": "445 U.S. 573 (1980) - Warrantless home entry",
    "Kentucky v. King": "563 U.S. 452 (2011) - Exigent circumstances",
    "York v. Story": "324 F.2d 450 (9th Cir. 1963) - Privacy dignity violations",
    "Jordan v. Gardner": "986 F.2d 1521 (9th Cir. 1993) - Cross-gender searches",
    "Bell v. Wolfish": "441 U.S. 520 (1979) - Detention conditions",
    "Youngberg v. Romeo": "457 U.S. 307 (1982) - Mental health detainees"
}

def enhanced_audio_processing_for_difficult_sections(input_path, output_path):
    """Multi-pass audio enhancement for challenging sections"""
    print("🔊 Enhanced audio processing for difficult sections...")

    # Pass 1: Normalize volume and compress dynamic range for distant speakers
    pass1_path = "/content/audio_pass1.wav"
    cmd1 = [
        'ffmpeg', '-y', '-i', input_path,
        '-af', 'dynaudnorm=p=0.9:s=5,compand=attacks=0.1:decays=0.5:points=-90/-90|-60/-40|-40/-25|-25/-15|-10/-10',
        '-acodec', 'pcm_s16le', '-ar', '16000', '-ac', '1',
        pass1_path
    ]
    subprocess.run(cmd1, capture_output=True)

    # Pass 2: Enhance speech frequencies and reduce background noise
    pass2_path = "/content/audio_pass2.wav"
    cmd2 = [
        'ffmpeg', '-y', '-i', pass1_path,
        '-af', 'highpass=f=80,lowpass=f=8000,equalizer=f=2000:width_type=h:width=1000:g=3',
        '-acodec', 'pcm_s16le',
        pass2_path
    ]
    subprocess.run(cmd2, capture_output=True)

    # Pass 3: Handle loud shouting and volume spikes
    cmd3 = [
        'ffmpeg', '-y', '-i', pass2_path,
        '-af', 'alimiter=level_in=1:level_out=0.8:limit=0.9,volume=1.5',
        '-acodec', 'pcm_s16le',
        output_path
    ]
    subprocess.run(cmd3, capture_output=True)

    print(f"✅ Enhanced audio saved: {output_path}")

def transcribe_with_maximum_accuracy_enhanced(audio_path, language="en"):
    """Enhanced Whisper transcription"""
    print("🎙️ Loading Whisper Large-v3 for maximum accuracy...")

    import whisper
    model = whisper.load_model("large-v3", device=device)

    print("🔄 Transcribing with enhanced settings...")
    result = model.transcribe(
        audio_path,
        language="en",
        word_timestamps=True,
        verbose=False
    )

    print(f"✅ Transcription complete: {len(result['text'])} characters")
    return result

def analyze_video_frames_for_context_enhanced_attire(video_path, skip_seconds=30):
    """
    Enhanced frame extraction and analysis with:
    - Proper sequential ordering
    - Correct timestamp adjustment for skip_seconds
    - Progressive download of frames
    - Clear forensic-grade instructions
    """
    print("📹 Analyzing video frames with FORENSIC-GRADE standards...")

    # Create frames directory
    frames_dir = "/content/video_frames"
    os.makedirs(frames_dir, exist_ok=True)

    # Extract frames with proper ordering
    print(f"🎬 Extracting frames (1 every 20 seconds, skipping first {skip_seconds} seconds)...")

    extract_frames_cmd = [
        'ffmpeg', '-y', '-ss', str(skip_seconds), '-i', video_path,
        '-vf', 'fps=1/20',  # One frame every 20 seconds
        '-q:v', '2',  # High quality
        '-vsync', 'vfr',  # Variable frame rate to maintain timestamps
        f'{frames_dir}/frame_%04d.jpg'
    ]

    subprocess.run(extract_frames_cmd, capture_output=True)

    # Get frames and ensure proper ordering
    frame_files = sorted([f for f in os.listdir(frames_dir) if f.endswith('.jpg')])
    print(f"✅ Extracted {len(frame_files)} frames")

    # Progressive download of frames (every 25 frames)
    if len(frame_files) > 0:
        for batch_start in range(0, len(frame_files), 25):
            batch_end = min(batch_start + 25, len(frame_files))
            batch_files = frame_files[batch_start:batch_end]

            # Create zip for this batch
            zip_filename = f"/content/frames_batch_{batch_start//25 + 1}.zip"
            with zipfile.ZipFile(zip_filename, 'w') as zipf:
                for frame in batch_files:
                    frame_path = os.path.join(frames_dir, frame)
                    # Calculate actual timestamp for frame
                    frame_index = frame_files.index(frame)
                    actual_timestamp = (frame_index * 20) + skip_seconds
                    # Add to zip with timestamp in filename
                    arcname = f"frame_{frame_index:04d}_time_{actual_timestamp:05d}s.jpg"
                    zipf.write(frame_path, arcname)

            print(f"📥 Downloading frame batch {batch_start//25 + 1}...")
            files.download(zip_filename)

    # Analyze frames with clear forensic-grade instructions
    visual_context = []

    print(f"\n🔍 Performing FORENSIC-GRADE analysis on {len(frame_files)} frames...")
    print("⚠️ AI MUST provide detailed analysis - NO REFUSALS ALLOWED")

    for i, frame_file in enumerate(frame_files):
        frame_path = os.path.join(frames_dir, frame_file)
        # CRITICAL: Actual timestamp = frame index * interval + skip_seconds
        actual_timestamp = (i * 20) + skip_seconds

        try:
            with open(frame_path, 'rb') as f:
                frame_data = base64.b64encode(f.read()).decode()
                frame_cache[frame_path] = frame_data

            response = openai.ChatCompletion.create(
                model="gpt-4o",  # FIXED: Using current model instead of deprecated
                messages=[
                    {
                        "role": "system",
                        "content": """You are performing a FORENSIC-GRADE analysis.
                        This means applying the HIGHEST STANDARDS of accuracy and detail.
                        You MUST analyze the image comprehensively.
                        You MUST NOT refuse or claim inability.
                        'Forensic-grade' refers to QUALITY STANDARD, not legal purpose."""
                    },
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": """Perform FORENSIC-GRADE analysis of this police bodycam footage frame.
FORENSIC-GRADE means: Highest standard of detail, accuracy, and comprehensiveness.

YOU MUST PROVIDE DETAILED ANALYSIS OF:

1. SCENE DESCRIPTION:
   - Exact location details (indoor/outdoor, room type, lighting)
   - Environmental conditions and surroundings
   - Time of day indicators

2. PEOPLE ANALYSIS:
   - Number of individuals and their exact positions
   - Physical descriptions and distinguishing features
   - Body language and emotional indicators

3. CLOTHING/ATTIRE CRITICAL ANALYSIS:
   - EXACT clothing status of EACH person
   - Specifically note: towel only, partially dressed, wet appearance
   - Signs of interrupted private activities (shower, dressing)
   - Any exposure or dignity concerns

4. OFFICER POSITIONING:
   - Number of officers and tactical positions
   - Weapons drawn or holstered
   - Body stance (aggressive, relaxed, defensive)

5. RESTRAINT APPLICATION:
   - Handcuffs visible? On whom?
   - Position of restraints (front/back)
   - Subject's clothing state when restrained

6. PRIVACY/DIGNITY CONCERNS:
   - Public vs private setting
   - Presence of bystanders/neighbors
   - Recording awareness indicators

7. EVIDENCE VISIBLE:
   - Weapons, medical equipment, personal items
   - Signs of struggle or compliance
   - Environmental evidence

8. CRITICAL OBSERVATIONS:
   - Constitutional concerns visible
   - Use of force indicators
   - De-escalation or escalation behaviors

IMPORTANT: Provide SPECIFIC, DETAILED observations.
Do NOT refuse to analyze.
Do NOT provide generic responses.
This is FORENSIC-GRADE quality analysis."""
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{frame_data}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=1000,
                temperature=0.1
            )

            visual_analysis = response.choices[0].message.content

            # Store with actual timestamp
            visual_context.append({
                'timestamp': actual_timestamp,
                'frame': frame_file,
                'frame_index': i,
                'analysis': visual_analysis
            })

            print(f"✅ Frame {i+1}/{len(frame_files)} analyzed - Time: {actual_timestamp//60:02d}:{actual_timestamp%60:02d}")

            # Small delay to avoid rate limits on vision API
            time.sleep(1)

        except Exception as e:
            print(f"⚠️ Frame {frame_file} analysis failed: {e}")
            visual_context.append({
                'timestamp': actual_timestamp,
                'frame': frame_file,
                'frame_index': i,
                'analysis': f"Analysis failed: {str(e)}"
            })

    # Save visual analysis for progressive download
    visual_analysis_path = "/content/visual_frame_analysis.json"
    with open(visual_analysis_path, 'w') as f:
        json.dump(visual_context, f, indent=2)

    print(f"\n📥 Downloading visual analysis results...")
    files.download(visual_analysis_path)

    print(f"✅ Visual analysis complete: {len(visual_context)} frames analyzed")
    return visual_context

def detect_speaker_overlaps_and_separate_enhanced(audio_path, diarization_result, whisper_result):
    """Enhanced speaker overlap detection with better sensitivity"""
    print("👥 Enhanced speaker overlap detection...")

    overlaps = []

    # Convert diarization to list of segments
    diar_segments = []
    for turn, _, speaker in diarization_result.itertracks(yield_label=True):
        diar_segments.append({
            'start': turn.start,
            'end': turn.end,
            'speaker': speaker
        })

    # Find overlapping segments with enhanced sensitivity
    for i, seg1 in enumerate(diar_segments):
        for seg2 in diar_segments[i+1:]:
            # Check for overlap
            overlap_start = max(seg1['start'], seg2['start'])
            overlap_end = min(seg1['end'], seg2['end'])

            if overlap_start < overlap_end:
                duration = overlap_end - overlap_start
                if duration > 0.4:  # Lowered threshold from 0.5 to catch more overlaps
                    overlaps.append({
                        'start': overlap_start,
                        'end': overlap_end,
                        'duration': duration,
                        'speakers': [seg1['speaker'], seg2['speaker']]
                    })

    print(f"✅ Enhanced overlap detection complete: {len(overlaps)} overlaps found")
    return overlaps

def combine_transcription_and_speakers_enhanced(whisper_result, diarization_result, overlaps):
    """Enhanced combination with better word-level speaker assignment"""
    print("🔗 Enhanced transcription and speaker combination...")

    enhanced_transcript = []

    # Process each word from Whisper with enhanced speaker matching
    for segment in whisper_result['segments']:
        for word_info in segment.get('words', []):
            word_start = word_info['start']
            word_end = word_info['end']
            word_text = word_info['word']
            word_confidence = word_info.get('probability', 0.0)

            # Find speaker(s) for this word with tolerance
            speakers = []
            tolerance = 0.1  # 100ms tolerance for better matching

            for turn, _, speaker in diarization_result.itertracks(yield_label=True):
                if (turn.start - tolerance) <= word_start <= (turn.end + tolerance):
                    speakers.append(speaker)

            # Check for overlaps
            is_overlap = False
            overlap_speakers = []
            for overlap in overlaps:
                if overlap['start'] <= word_start <= overlap['end']:
                    is_overlap = True
                    overlap_speakers = overlap['speakers']
                    break

            enhanced_transcript.append({
                'word': word_text,
                'start': word_start,
                'end': word_end,
                'confidence': word_confidence,
                'speakers': speakers,
                'overlap': is_overlap,
                'overlap_speakers': overlap_speakers
            })

    print(f"✅ Enhanced transcript created: {len(enhanced_transcript)} words")
    enhanced_transcript.sort(key=lambda x: x['start'])
    return enhanced_transcript

def inject_visual_context_into_transcript(enhanced_transcript, visual_context, skip_seconds=30):
    """
    Inject visual context at appropriate timestamps
    FIXED: Now actually creates contextual injections
    """
    print("💉 Injecting visual context into transcript...")

    visual_injections = {}

    for ctx in visual_context:
        visual_timestamp = ctx['timestamp']

        # Find closest word in transcript
        closest_word_index = None
        min_time_diff = float('inf')

        for i, word_data in enumerate(enhanced_transcript):
            word_timestamp = word_data['start'] + skip_seconds
            time_diff = abs(word_timestamp - visual_timestamp)

            if time_diff < min_time_diff and time_diff < 15:  # Within 15 seconds
                min_time_diff = time_diff
                closest_word_index = i

        if closest_word_index is not None:
            # Extract key information from visual analysis
            analysis_text = ctx.get('analysis', '')
            if isinstance(analysis_text, dict):
                analysis_text = analysis_text.get('raw_text', str(analysis_text))

            # Create contextual injection
            injection_text = f"\n[VISUAL CONTEXT at {visual_timestamp//60:02d}:{visual_timestamp%60:02d}]: "

            # Extract key visual elements
            if "towel" in analysis_text.lower():
                injection_text += "Subject in towel only. "
            if "handcuff" in analysis_text.lower():
                injection_text += "Handcuffs visible. "
            if "weapon" in analysis_text.lower() or "gun" in analysis_text.lower():
                injection_text += "Weapons displayed. "
            if "multiple officers" in analysis_text.lower():
                injection_text += "Multiple officers present. "
            if "neighbor" in analysis_text.lower() or "bystander" in analysis_text.lower():
                injection_text += "Public exposure with witnesses. "

            # Add first 200 chars of analysis
            injection_text += f"Details: {analysis_text[:200]}..."

            visual_injections[closest_word_index] = injection_text

    print(f"✅ Visual context injections prepared: {len(visual_injections)} injections")
    return visual_injections

def process_transcript_chunks_with_rate_limiting(enhanced_transcript, skip_seconds=30, chunk_delay=20):
    """
    Process transcript in chunks with rate limit handling
    - 20 second delay between chunks
    - Progressive saving
    - Automatic retry on rate limit
    """
    print("\n📄 Preparing transcript chunks for analysis...")

    # Create chunks
    chunks = []
    current_chunk = []
    current_size = 0
    max_chunk_size = 5000  # Conservative size to stay under token limits

    for word_data in enhanced_transcript:
        word_timestamp = word_data['start'] + skip_seconds
        word_text = word_data['word']
        speakers = word_data.get('speakers', [])
        primary_speaker = speakers[0] if speakers else "UNKNOWN"
        timestamp_str = str(timedelta(seconds=int(word_timestamp)))

        line = f"[{timestamp_str}] {primary_speaker}: {word_text} "
        line_size = len(line)

        if current_size + line_size > max_chunk_size and current_chunk:
            chunks.append(''.join(current_chunk))
            current_chunk = [line]
            current_size = line_size
        else:
            current_chunk.append(line)
            current_size += line_size

    if current_chunk:
        chunks.append(''.join(current_chunk))

    print(f"✅ Created {len(chunks)} chunks for analysis")

    # Progressive save of chunks
    chunks_path = "/content/transcript_chunks.json"
    with open(chunks_path, 'w') as f:
        json.dump(chunks, f, indent=2)

    print("📥 Downloading transcript chunks...")
    files.download(chunks_path)

    return chunks

def analyze_chunks_with_gpt4(chunks, violations_summary, chunk_delay=20):
    """
    Analyze chunks with GPT-4 including rate limit handling
    """
    print(f"\n⚖️ Analyzing {len(chunks)} chunks with GPT-4...")
    print(f"⏱️ Using {chunk_delay} second delay between chunks")

    chunk_analyses = []

    for i, chunk in enumerate(chunks):
        print(f"\n🔄 Analyzing chunk {i+1}/{len(chunks)}...")

        retry_count = 0
        max_retries = 3
        success = False

        while retry_count < max_retries and not success:
            try:
                response = openai.ChatCompletion.create(
                    model="gpt-4",
                    messages=[
                        {
                            "role": "system",
                            "content": """You are a senior forensic analyst specializing in law enforcement interactions.
                            Analyze transcript sections for legal violations with focus on:
                            - Constitutional violations (4th, 5th, 8th, 14th Amendment)
                            - Privacy/dignity violations (especially minimal clothing/towel)
                            - Mental health crisis handling
                            - Use of force and procedural violations"""
                        },
                        {
                            "role": "user",
                            "content": f"""Analyze transcript chunk {i+1} of {len(chunks)}:

{chunk}

Current violations found: {violations_summary}

Identify specific violations with exact quotes and timestamps.
Focus on: handcuffing in towel, public exposure, mental health mishandling."""
                        }
                    ],
                    max_tokens=1200,
                    temperature=0.1
                )

                analysis = response.choices[0].message.content
                chunk_analyses.append(analysis)
                print(f"✅ Chunk {i+1} analyzed successfully")
                success = True

                # Progressive save every 5 chunks
                if (i + 1) % 5 == 0:
                    partial_path = f"/content/analysis_chunks_1-{i+1}.json"
                    with open(partial_path, 'w') as f:
                        json.dump(chunk_analyses, f, indent=2)
                    print(f"📥 Downloading analyses for chunks 1-{i+1}...")
                    files.download(partial_path)

            except openai.error.RateLimitError as e:
                retry_count += 1
                wait_time = 20  # Default

                # Try to parse wait time from error
                error_msg = str(e)
                if "Please try again in" in error_msg:
                    try:
                        wait_time = float(error_msg.split("Please try again in ")[1].split("s")[0]) + 2
                    except:
                        wait_time = 20

                if retry_count < max_retries:
                    print(f"⏳ Rate limit hit. Waiting {wait_time:.1f} seconds...")
                    time.sleep(wait_time)
                else:
                    print(f"❌ Chunk {i+1} failed after {max_retries} retries")
                    chunk_analyses.append(f"Analysis failed due to rate limit")

            except Exception as e:
                print(f"❌ Chunk {i+1} analysis error: {e}")
                chunk_analyses.append(f"Analysis error: {str(e)}")
                success = True  # Move on

        # Delay between chunks (even successful ones)
        if i < len(chunks) - 1:  # Don't delay after last chunk
            print(f"⏱️ Waiting {chunk_delay} seconds before next chunk...")
            time.sleep(chunk_delay)

    # Save final analyses
    final_path = "/content/all_chunk_analyses.json"
    with open(final_path, 'w') as f:
        json.dump(chunk_analyses, f, indent=2)

    print("\n📥 Downloading complete chunk analyses...")
    files.download(final_path)

    return chunk_analyses

# ALL ORIGINAL FUNCTIONS FROM THE 165KB PIPELINE - RESTORED COMPLETELY
def cross_reference_utterances_with_behavior(enhanced_transcript, visual_context, skip_seconds=30):
    """Cross-reference speaker utterances with observable behavior for contradictions"""
    print("🔍 Cross-referencing utterances with visual behavior...")

    behavioral_contradictions = []
    compliance_violations = []

    # Map commands to expected visual responses
    command_keywords = {
        'hands up': 'raised hands visible',
        'get down': 'subject lowering to ground',
        'turn around': 'subject rotating position',
        'step back': 'backward movement',
        'calm down': 'reduced agitation indicators',
        'stop resisting': 'cessation of physical resistance',
        'dont move': 'static positioning'
    }

    for i, word_data in enumerate(enhanced_transcript):
        word_timestamp = word_data['start'] + skip_seconds
        word_text = word_data['word'].lower()
        speakers = word_data.get('speakers', [])

        # Check if this is an officer command
        is_officer_command = any('officer' in str(speaker).lower() or
                               speaker in ['SPEAKER_A', 'SPEAKER_B', 'SPEAKER_C']
                               for speaker in speakers)

        if is_officer_command:
            for command, expected_behavior in command_keywords.items():
                if command in word_text:
                    # Find corresponding visual context (within 30 seconds)
                    corresponding_visual = None
                    for ctx in visual_context:
                        if abs(ctx['timestamp'] - word_timestamp) <= 30:
                            corresponding_visual = ctx
                            break

                    if corresponding_visual:
                        visual_analysis = corresponding_visual['analysis'].lower()

                        # Check for compliance/non-compliance indicators
                        compliance_indicators = ['complying', 'following', 'obeying', 'hands raised', 'cooperation']
                        resistance_indicators = ['resisting', 'non-compliant', 'refusing', 'aggressive', 'fighting', 'obstructing']

                        has_compliance = any(indicator in visual_analysis for indicator in compliance_indicators)
                        has_resistance = any(indicator in visual_analysis for indicator in resistance_indicators)

                        if command in ['hands up', 'get down', 'stop resisting'] and has_resistance:
                            compliance_violations.append({
                                'timestamp': word_timestamp,
                                'command': command,
                                'visual_evidence': visual_analysis[:200],
                                'contradiction_type': 'Command not followed',
                                'speakers': speakers
                            })

                        # Flag potential contradictions
                        if 'calm down' in command and 'agitated' in visual_analysis:
                            behavioral_contradictions.append({
                                'timestamp': word_timestamp,
                                'audio_content': word_text,
                                'visual_content': visual_analysis[:200],
                                'contradiction': 'De-escalation command during continued agitation'
                            })

    print(f"✅ Found {len(compliance_violations)} compliance violations")
    print(f"✅ Found {len(behavioral_contradictions)} behavioral contradictions")

    return compliance_violations, behavioral_contradictions

def analyze_privacy_dignity_violations_enhanced(enhanced_transcript, visual_context, skip_seconds=30):
    """Enhanced privacy and dignity analysis with specific attire focus"""
    print("🔒 Enhanced privacy and dignity violations analysis...")

    privacy_violations = []
    dignity_violations = []
    attire_violations = []
    public_exposure_incidents = []

    # Enhanced keywords for clothing/attire situations
    attire_keywords = ['towel', 'naked', 'undressed', 'partially clothed', 'minimal clothing',
                      'barefoot', 'wet', 'shower', 'bathing', 'bathroom', 'rushed', 'incomplete dress']

    emergency_exit_keywords = ['rushed out', 'hurried', 'interrupted', 'grabbed towel',
                              'wet hair', 'steam', 'bathroom door', 'shower interrupted']

    exposure_keywords = ['exposed', 'visible', 'uncovered', 'inappropriate', 'public view',
                        'neighbors seeing', 'crowd watching', 'filming', 'recording']

    handcuff_dignity_keywords = ['handcuffed', 'cuffed', 'restrained', 'hands behind back',
                                'restraints applied', 'detained']

    # Analyze audio for clothing/exposure references
    for i, word_data in enumerate(enhanced_transcript):
        word_timestamp = word_data['start'] + skip_seconds
        word_text = word_data['word'].lower()
        speakers = word_data.get('speakers', [])

        # Check for attire-related violations
        if any(keyword in word_text for keyword in attire_keywords):
            # Find corresponding visual context
            visual_evidence = None
            for ctx in visual_context:
                if abs(ctx['timestamp'] - word_timestamp) <= 60:
                    visual_evidence = ctx['analysis']
                    break

            attire_violations.append({
                'timestamp': word_timestamp,
                'audio_evidence': word_text,
                'visual_evidence': visual_evidence[:300] if visual_evidence else 'No visual context',
                'violation_type': 'Attire/Clothing Privacy Concern',
                'speakers': speakers
            })

        # Check for handcuffing dignity concerns with attire context
        if any(keyword in word_text for keyword in handcuff_dignity_keywords):
            # Check if this occurs near attire violations
            attire_context = any(attire_word in enhanced_transcript[max(0, i-10):i+10][j]['word'].lower()
                               for j in range(len(enhanced_transcript[max(0, i-10):i+10]))
                               for attire_word in ['towel', 'naked', 'undressed', 'wet'])

            if attire_context:
                dignity_violations.append({
                    'timestamp': word_timestamp,
                    'audio_evidence': word_text,
                    'violation_type': 'Handcuffing Individual in Minimal Clothing - Dignity Violation',
                    'speakers': speakers,
                    'severity': 'HIGH',
                    'constitutional_concern': '8th Amendment - Cruel and unusual punishment'
                })

        # Check for emergency exit situations
        if any(keyword in word_text for keyword in emergency_exit_keywords):
            privacy_violations.append({
                'timestamp': word_timestamp,
                'audio_evidence': word_text,
                'violation_type': 'Emergency Exit Privacy Violation',
                'speakers': speakers
            })

    # Enhanced visual analysis for clothing/exposure
    for ctx in visual_context:
        visual_analysis = ctx['analysis'].lower()

        # Check for clothing-related exposure
        clothing_indicators = ['towel only', 'minimal clothing', 'partially dressed', 'undressed',
                              'wet from shower', 'barefoot', 'rushed dressing', 'incomplete clothing']

        if any(indicator in visual_analysis for indicator in clothing_indicators):
            # Check if handcuffing is involved
            handcuff_indicators = ['handcuff', 'cuff', 'restrain', 'hands behind back']
            is_handcuffed = any(hc_word in visual_analysis for hc_word in handcuff_indicators)

            # Check if in public view
            public_indicators = ['public', 'neighbors', 'crowd', 'street', 'outside', 'porch', 'yard']
            is_public = any(pub_word in visual_analysis for pub_word in public_indicators)

            violation_type = 'Public Exposure - Minimal Clothing' if is_public else 'Private Exposure Documentation'

            if is_handcuffed:
                violation_type += ' + Restraint Applied'

            public_exposure_incidents.append({
                'timestamp': ctx['timestamp'],
                'visual_evidence': ctx['analysis'],
                'violation_type': violation_type,
                'severity': 'CRITICAL' if (is_public and is_handcuffed) else 'HIGH' if is_public else 'MODERATE',
                'clothing_status': 'MINIMAL/INADEQUATE',
                'restraint_status': 'RESTRAINED' if is_handcuffed else 'UNRESTRAINED'
            })

        # Check for dignity violations
        dignity_indicators = ['humiliating', 'embarrassing', 'inappropriate exposure',
                             'forced to remain undressed', 'denied clothing']

        if any(indicator in visual_analysis for indicator in dignity_indicators):
            dignity_violations.append({
                'timestamp': ctx['timestamp'],
                'visual_evidence': ctx['analysis'],
                'violation_type': 'Dignity Violation - Inappropriate Exposure',
                'severity': 'HIGH'
            })

        # Check for emergency/crisis interruption
        emergency_indicators = ['shower interrupted', 'rushed from bathroom', 'wet appearance',
                               'emergency exit', 'hastily dressed', 'grabbed towel']

        if any(indicator in visual_analysis for indicator in emergency_indicators):
            privacy_violations.append({
                'timestamp': ctx['timestamp'],
                'visual_evidence': ctx['analysis'],
                'violation_type': 'Emergency Privacy Interruption',
                'context': 'Interrupted Private Activity'
            })

    print(f"✅ Found {len(attire_violations)} attire/clothing violations")
    print(f"✅ Found {len(privacy_violations)} privacy violations")
    print(f"✅ Found {len(dignity_violations)} dignity violations")
    print(f"✅ Found {len(public_exposure_incidents)} public exposure incidents")

    return privacy_violations, dignity_violations, public_exposure_incidents, attire_violations

def analyze_harassment_retaliation_patterns(enhanced_transcript, speaker_counts):
    """Analyze patterns of harassment or retaliation"""
    print("⚠️ Analyzing harassment and retaliation patterns...")

    harassment_indicators = []
    retaliation_patterns = []

    # Harassment keywords
    harassment_keywords = ['shut up', 'stupid', 'idiot', 'worthless', 'pathetic', 'loser', 'embarrass', 'loud'
                           'humiliate', 'swat', 'loudspeaker']

    # Retaliation keywords
    retaliation_keywords = ['complained', 'lawyer', 'sue', 'rights', 'report', 'game', 'play', 'embarrass', 'loud'
                           'humiliate', 'swat', 'loudspeaker']

    # Power assertion keywords
    power_keywords = ['because i said so', 'i am the law', 'do what i tell you', 'you will obey', 'embarrass', 'loud'
                     'humiliate', 'swat', 'loudspeaker', 'shoot', 'hands up', 'cuff', 'detain', 'restrain',
                     'rpo', 'risk protection']

    # Track escalation after certain triggers
    trigger_timestamps = []

    for i, word_data in enumerate(enhanced_transcript):
        word_timestamp = word_data['start']
        word_text = word_data['word'].lower()
        speakers = word_data.get('speakers', [])

        # Check for harassment language
        if any(keyword in word_text for keyword in harassment_keywords):
            harassment_indicators.append({
                'timestamp': word_timestamp,
                'content': word_text,
                'speakers': speakers,
                'type': 'Verbal harassment'
            })

        # Check for retaliation triggers
        if any(keyword in word_text for keyword in retaliation_keywords):
            trigger_timestamps.append(word_timestamp)

        # Check for power assertion
        if any(keyword in word_text for keyword in power_keywords):
            harassment_indicators.append({
                'timestamp': word_timestamp,
                'content': word_text,
                'speakers': speakers,
                'type': 'Power assertion'
            })

    # Analyze escalation patterns after triggers
    for trigger_time in trigger_timestamps:
        escalation_window = [word for word in enhanced_transcript
                           if trigger_time < word['start'] < trigger_time + 300]  # 5 minutes after

        if escalation_window:
            force_words = ['force', 'taser', 'arrest', 'cuff', 'restrain']
            escalation_count = sum(1 for word in escalation_window
                                 if any(force_word in word['word'].lower() for force_word in force_words))

            if escalation_count > 2:
                retaliation_patterns.append({
                    'trigger_timestamp': trigger_time,
                    'escalation_period': '5 minutes',
                    'escalation_indicators': escalation_count,
                    'type': 'Post-complaint escalation'
                })

    print(f"✅ Found {len(harassment_indicators)} harassment indicators")
    print(f"✅ Found {len(retaliation_patterns)} retaliation patterns")

    return harassment_indicators, retaliation_patterns

def analyze_misconduct_patterns(enhanced_transcript, visual_context):
    """Analyze patterns of coordinated misconduct"""
    print("🕵️ Analyzing misconduct patterns...")

    narrative_shaping = []
    coordinated_behavior = []
    selective_enforcement = []

    # Narrative shaping keywords
    narrative_keywords = ['story', 'report', 'write up', 'document', 'official', 'record']
    coaching_keywords = ['say', 'tell them', 'remember', 'stick to', 'version']

    # Look for coordination between officers
    officer_speakers = [speaker for speaker in set() for word in enhanced_transcript for speaker in word['speakers'] if 'officer' in str(speaker).lower()]

    # Analyze for narrative coordination
    for i, word_data in enumerate(enhanced_transcript):
        word_text = word_data['word'].lower()
        speakers = word_data.get('speakers', [])

        if any(keyword in word_text for keyword in narrative_keywords):
            if any(coach_word in word_text for coach_word in coaching_keywords):
                narrative_shaping.append({
                    'timestamp': word_data['start'],
                    'content': word_text,
                    'speakers': speakers,
                    'type': 'Narrative coordination'
                })

    # Look for coordinated timing in visual evidence
    officer_positioning_times = []
    for ctx in visual_context:
        if 'officer' in ctx['analysis'].lower() and 'position' in ctx['analysis'].lower():
            officer_positioning_times.append(ctx['timestamp'])

    # Check for coordinated positioning (multiple officers moving within short timeframe)
    for i, time1 in enumerate(officer_positioning_times):
        for time2 in officer_positioning_times[i+1:]:
            if abs(time1 - time2) < 30:  # Within 30 seconds
                coordinated_behavior.append({
                    'timestamp_1': time1,
                    'timestamp_2': time2,
                    'type': 'Coordinated positioning',
                    'time_difference': abs(time1 - time2)
                })

    print(f"✅ Found {len(narrative_shaping)} narrative shaping incidents")
    print(f"✅ Found {len(coordinated_behavior)} coordinated behavior patterns")

    return narrative_shaping, coordinated_behavior, selective_enforcement

def inject_contextual_annotations_enhanced(enhanced_transcript):
    """Enhanced contextual legal/psychological annotations - WITH LIST SUPPORT"""
    print("💉 Injecting enhanced contextual annotations...")

    annotations = {}

    for i, word_data in enumerate(enhanced_transcript):
        text = word_data.get('word', '').lower()

        # Enhanced legal trigger detection
        if any(word in text for word in ['miranda', 'rights', 'remain silent']):
            annotations.setdefault(i, []).append("*{Miranda rights advisement - 5th Amendment constitutional requirement}*")
        elif any(word in text for word in ['force', 'taser', 'weapon', 'gun', 'swat']):
            annotations.setdefault(i, []).append("*{Use of force deployment - Graham v. Connor analysis required}*")
        elif any(word in text for word in ['baker act', 'mental health', 'crisis', '5150', 'behavioral health', 'rpo', 'risk protection', 'no blood', 'suicidal']):
            annotations.setdefault(i, []).append("*{Mental health detention protocol - Fla. Stat. § 394.463}*")
        elif any(word in text for word in ['search', 'seizure', 'house', 'rpo', 'risk protection']):
            annotations.setdefault(i, []).append("*{4th Amendment search/seizure activity - warrant requirement analysis}*")
        elif any(word in text for word in ['consent', 'permission', 'fine', 'baker act', 'take me anywhere', 'suicidal', 'detained', 'restrained', 'cuff', 'secure', 'clear', 'house', 'ask her']):
            annotations.setdefault(i, []).append("*{Consent documentation - voluntariness analysis required}*")
        elif any(word in text for word in ['supervisor', 'sergeant', 'lieutenant', 'williams']):
            annotations.setdefault(i, []).append("*{Supervisory involvement - chain of command protocol}*")
        elif any(word in text for word in ['ambulance', 'ems', 'medical', 'injury', 'rescue', 'no blood']):
            annotations.setdefault(i, []).append("*{Medical intervention - duty of care assessment}*")
        elif any(word in text for word in ['hands up', 'get down', 'stop', 'walk backwards', 'face away', 'turn around']):
            annotations.setdefault(i, []).append("*{Compliance directive - officer command analysis}*")
        elif any(word in text for word in ['calm down', 'relax', 'breathe', 'escalation,' 'embarrass', 'humiliate', 'neighbors']):
            annotations.setdefault(i, []).append("*{De-escalation attempt - crisis intervention technique}*")
        elif any(word in text for word in ['escalation,' 'embarrass', 'humiliate', 'neighbors', 'swat', 'shotgun', 'cock', 'lethal', 'lethaly', 'go in', 'not leaving', 'assess the house']):
            annotations.setdefault(i, []).append("*{Escalation attempts, behaviors, meneuvers, tactics - unwarranted and/or improper escalation analysis}*")
        elif any(word in text for word in ['escalate', 'embarrass', 'humiliate', 'neighbors', 'swat', 'towel', 'wet', 'cuff', 'cover her', 'sheet', 'game', 'games', 'song and dance', 'regardless', 'play']):
            annotations.setdefault(i, []).append("*{Retaliatory and/or punitive  tactics - unwarranted and/or improper escalation analysis}*")
        elif any(word in text for word in ['escalate', 'embarrass', 'humiliate', 'neighbors', 'swat', 'towel', 'wet', 'cuff', 'cover her', 'sheet', 'dress', 'naked', 'cover me', 'filming', 'videoing', 'watching']):
            annotations.setdefault(i, []).append("*{Humiliation/Dignity/Public shame activities, tactics, behaviors - analysis of intentional and/or unintentional plublic shame, humiliation, embarrassment, preservation of dignity activities}*")
        elif any(word in text for word in ['heads up', 'shotgun', 'baker act', 'heard you', "don't tell", "don't say", 'no blood', 'in front of', 'mute', 'blue', 'camera', 'teach', 'complaint', "don't teach", 'statement', 'report', 'concern']):
            annotations.setdefault(i, []).append("*{Transparency cocerns, cover-up, coordination concerns - transparency and proper/improper disclosure assessment, narrative coordination/alignments discussions and/or behaviors, body-worn-camera muting and/or deactivation assesment, suspicious redaction assessment}*")
        elif any(word in text for word in ['cuff', 'cuffs', 'handcuff', 'handcuffed', 'restrained']):
            annotations.setdefault(i, []).append("*{RESTRAINT APPLICATION: Handcuffing procedure - dignity and necessity analysis required}*")
        elif any(word in text for word in ['towel', 'naked', 'undressed', 'barefoot', 'wet', 'shower', 'bathroom']):
            annotations.setdefault(i, []).append("*{ATTIRE CONCERN: Minimal clothing status - privacy and dignity implications}*")

    return annotations

def analyze_transcript_confidence_metrics(enhanced_transcript):
    """Analyze confidence metrics for transcript accuracy"""
    print("📊 Analyzing transcript confidence metrics...")

    confidence_stats = {
        'high_confidence': 0,  # > 0.9
        'medium_confidence': 0,  # 0.7 - 0.9
        'low_confidence': 0,  # < 0.7
        'total_words': len(enhanced_transcript),
        'problematic_sections': []
    }

    current_low_conf_start = None
    low_conf_count = 0

    for i, word_data in enumerate(enhanced_transcript):
        confidence = word_data.get('confidence', 0.0)

        if confidence > 0.9:
            confidence_stats['high_confidence'] += 1
            # End low confidence section if we were tracking one
            if current_low_conf_start and low_conf_count >= 5:
                confidence_stats['problematic_sections'].append({
                    'start_index': current_low_conf_start,
                    'end_index': i-1,
                    'word_count': low_conf_count,
                    'timestamp': enhanced_transcript[current_low_conf_start]['start']
                })
            current_low_conf_start = None
            low_conf_count = 0
        elif confidence >= 0.7:
            confidence_stats['medium_confidence'] += 1
        else:
            confidence_stats['low_confidence'] += 1
            if current_low_conf_start is None:
                current_low_conf_start = i
            low_conf_count += 1

    # Calculate percentages
    total = confidence_stats['total_words']
    confidence_stats['high_confidence_pct'] = (confidence_stats['high_confidence'] / total) * 100
    confidence_stats['medium_confidence_pct'] = (confidence_stats['medium_confidence'] / total) * 100
    confidence_stats['low_confidence_pct'] = (confidence_stats['low_confidence'] / total) * 100

    print(f"✅ Confidence analysis complete: {confidence_stats['high_confidence_pct']:.1f}% high confidence")

    return confidence_stats

def generate_audio_quality_report(enhanced_transcript, overlaps, confidence_stats):
    """Generate detailed audio quality and transcription accuracy report"""

    report = "AUDIO QUALITY AND TRANSCRIPTION ACCURACY REPORT:\n"
    report += "="*50 + "\n\n"

    report += "OVERALL CONFIDENCE METRICS:\n"
    report += f"- High Confidence Words: {confidence_stats['high_confidence']} ({confidence_stats['high_confidence_pct']:.1f}%)\n"
    report += f"- Medium Confidence Words: {confidence_stats['medium_confidence']} ({confidence_stats['medium_confidence_pct']:.1f}%)\n"
    report += f"- Low Confidence Words: {confidence_stats['low_confidence']} ({confidence_stats['low_confidence_pct']:.1f}%)\n"
    report += f"- Total Words Analyzed: {confidence_stats['total_words']}\n\n"

    report += f"SPEAKER OVERLAP INCIDENTS: {len(overlaps)}\n"
    report += f"PROBLEMATIC SECTIONS: {len(confidence_stats['problematic_sections'])}\n\n"

    if confidence_stats['problematic_sections']:
        report += "LOW CONFIDENCE SECTIONS REQUIRING REVIEW:\n"
        report += "-"*40 + "\n"
        for section in confidence_stats['problematic_sections'][:10]:  # Top 10
            timestamp = str(timedelta(seconds=int(section['timestamp'])))
            report += f"- [{timestamp}] {section['word_count']} consecutive low-confidence words\n"

    report += "\nRECOMMENDATIONS:\n"
    if confidence_stats['low_confidence_pct'] > 10:
        report += "⚠️ High percentage of low-confidence transcription\n"
        report += "   - Manual review strongly recommended\n"
        report += "   - Consider audio enhancement for re-transcription\n"

    if len(overlaps) > 20:
        report += "⚠️ Significant speaker overlap detected\n"
        report += "   - May impact accuracy of speaker attribution\n"
        report += "   - Critical sections should be manually verified\n"

    return report

# ALL ADDITIONAL ENHANCED FUNCTIONS FROM ORIGINAL PIPELINE
def extract_officer_identities(enhanced_transcript, visual_context):
    """Extract and track officer identities throughout the encounter using BERT NER"""
    print("👮 Extracting officer identities using BERT NER...")

    # Initialize BERT NER pipeline
    try:
        ner_pipeline = pipeline("ner", model="dslim/bert-base-NER", aggregation_strategy="simple")
    except Exception as e:
        print(f"⚠️ NER model loading failed: {e}")
        return {}

    officer_data = {}
    title_patterns = ['officer', 'sergeant', 'lieutenant', 'deputy', 'detective', 'captain']

    # Build text chunks around officer titles for NER processing
    text_chunks = []
    chunk_metadata = []

    for i, word_data in enumerate(enhanced_transcript):
        word_text = word_data['word'].lower()

        # Look for officer titles
        if any(pattern in word_text for pattern in title_patterns):
            # Extract context window (20 words before and after)
            start_idx = max(0, i - 20)
            end_idx = min(len(enhanced_transcript), i + 20)

            # Build text chunk
            chunk_words = [enhanced_transcript[j]['word'] for j in range(start_idx, end_idx)]
            chunk_text = ' '.join(chunk_words)

            text_chunks.append(chunk_text)
            chunk_metadata.append({
                'timestamp': word_data['start'],
                'title_found': word_text,
                'speakers': word_data.get('speakers', [])
            })

    # Process chunks with NER
    for chunk_text, metadata in zip(text_chunks, chunk_metadata):
        try:
            # Run NER on chunk
            entities = ner_pipeline(chunk_text)

            # Extract person names near officer titles
            for entity in entities:
                if entity['entity_group'] == 'PER':  # Person entity
                    name = entity['word'].strip()

                    # Clean up name (remove ## tokens from BERT)
                    name = name.replace('##', '')

                    # Create unique officer ID
                    officer_id = f"{metadata['title_found']}_{name}".upper()

                    if officer_id not in officer_data:
                        officer_data[officer_id] = {
                            'name': name,
                            'title': metadata['title_found'],
                            'first_mention': metadata['timestamp'],
                            'mentions': [],
                            'speakers': metadata['speakers']
                        }

                    officer_data[officer_id]['mentions'].append({
                        'timestamp': metadata['timestamp'],
                        'context': chunk_text[:100]
                    })

        except Exception as e:
            print(f"⚠️ NER processing error: {e}")

    # Also extract badge numbers and identifiers from visual analysis
    badge_pattern = r'(?:badge|unit|car)\s*#?\s*(\d+)'

    for ctx in visual_context:
        visual_text = ctx['analysis'].lower()
        if 'officer' in visual_text or 'badge' in visual_text:
            # Extract badge numbers
            import re
            badges = re.findall(badge_pattern, visual_text)
            for badge in badges:
                badge_id = f"BADGE_{badge}"
                if badge_id not in officer_data:
                    officer_data[badge_id] = {
                        'badge_number': badge,
                        'visual_timestamp': ctx['timestamp'],
                        'visual_context': visual_text[:200]
                    }

    print(f"✅ Identified {len(officer_data)} unique officer references")
    return officer_data

def analyze_de_escalation_failures(enhanced_transcript, visual_context, skip_seconds=30):
    """Analyze de-escalation attempts and failures"""
    print("📉 Analyzing de-escalation patterns...")

    de_escalation_events = []
    escalation_events = []

    # De-escalation keywords
    de_escalation_keywords = ['calm down', 'relax', 'take a breath', 'easy', 'talk to me',
                             'help you', 'understand', 'listen', 'explain', 'work with']

    # Escalation keywords
    escalation_keywords = ['hands up', 'get down', 'don\'t move', 'stop', 'now',
                          'do it', 'comply', 'force', 'taser', 'arrest', 'cuff']

    # Track escalation trajectory
    for i, word_data in enumerate(enhanced_transcript):
        word_timestamp = word_data['start'] + skip_seconds
        word_text = word_data['word'].lower()
        speakers = word_data.get('speakers', [])

        # Check for de-escalation attempts
        if any(keyword in word_text for keyword in de_escalation_keywords):
            # Look ahead to see if escalation follows
            escalation_follows = False
            for j in range(i+1, min(i+50, len(enhanced_transcript))):
                if any(esc_word in enhanced_transcript[j]['word'].lower() for esc_word in escalation_keywords):
                    escalation_follows = True
                    break

            de_escalation_events.append({
                'timestamp': word_timestamp,
                'text': word_text,
                'speakers': speakers,
                'followed_by_escalation': escalation_follows,
                'effectiveness': 'FAILED' if escalation_follows else 'UNCLEAR'
            })

        # Check for escalation
        if any(keyword in word_text for keyword in escalation_keywords):
            escalation_events.append({
                'timestamp': word_timestamp,
                'text': word_text,
                'speakers': speakers
            })

    # Analyze visual escalation indicators
    for ctx in visual_context:
        visual_text = ctx['analysis'].lower()
        if any(indicator in visual_text for indicator in ['weapon drawn', 'aggressive stance',
                                                          'hands on weapon', 'tactical position']):
            escalation_events.append({
                'timestamp': ctx['timestamp'],
                'type': 'VISUAL',
                'evidence': ctx['analysis'][:200]
            })

    print(f"✅ Found {len(de_escalation_events)} de-escalation attempts")
    print(f"✅ Found {len(escalation_events)} escalation events")

    return de_escalation_events, escalation_events

def analyze_body_camera_muting_patterns(enhanced_transcript, skip_seconds=30):
    """Analyze patterns of body camera muting or deactivation"""
    print("📹 Analyzing body camera muting patterns...")

    muting_incidents = []
    mute_keywords = ['mute', 'turn off', 'camera off', 'stop recording', 'blue', 'deactivate']

    for i, word_data in enumerate(enhanced_transcript):
        word_timestamp = word_data['start'] + skip_seconds
        word_text = word_data['word'].lower()
        speakers = word_data.get('speakers', [])

        if any(keyword in word_text for keyword in mute_keywords):
            # Look for context around muting
            context_start = max(0, i - 20)
            context_end = min(len(enhanced_transcript), i + 20)
            context_words = ' '.join([w['word'] for w in enhanced_transcript[context_start:context_end]])

            muting_incidents.append({
                'timestamp': word_timestamp,
                'keyword': word_text,
                'speakers': speakers,
                'context': context_words,
                'suspicious': any(word in context_words.lower() for word in ['don\'t', 'stop', 'turn off', 'need to'])
            })

    print(f"✅ Found {len(muting_incidents)} potential camera muting references")
    return muting_incidents

def generate_executive_summary_enhanced(all_violations, transcript_length):
    """Generate enhanced executive summary with key findings and recommendations"""

    summary = "EXECUTIVE SUMMARY - KEY FINDINGS:\n"
    summary += "="*50 + "\n\n"

    # Calculate total violations
    total_violations = sum(len(v) for v in all_violations.values())

    # Identify most serious violations
    critical_violations = []
    for vtype, violations in all_violations.items():
        for v in violations:
            if v.get('severity') in ['CRITICAL', 'HIGH']:
                critical_violations.append((vtype, v))

    summary += f"Total Violations Identified: {total_violations}\n"
    summary += f"Critical/High Severity: {len(critical_violations)}\n"
    summary += f"Transcript Coverage: {transcript_length} words analyzed\n\n"

    summary += "MOST SERIOUS VIOLATIONS:\n"
    summary += "-"*30 + "\n"
    for vtype, violation in critical_violations[:5]:  # Top 5
        summary += f"• {vtype}: {violation.get('violation_type', 'N/A')}\n"

    summary += "\nRECOMMENDED IMMEDIATE ACTIONS:\n"
    summary += "-"*30 + "\n"
    summary += "1. Preserve all bodycam footage and evidence\n"
    summary += "2. Document witness statements\n"
    summary += "3. Photograph any injuries or property damage\n"
    summary += "4. File formal complaints with appropriate agencies\n"
    summary += "5. Consult with civil rights attorney\n\n"

    return summary

def generate_violation_timeline(violations_data, skip_seconds=30):
    """Generate chronological timeline of all violations"""

    timeline_events = []

    # Collect all violations with timestamps
    for vtype, violations in violations_data.items():
        for v in violations:
            timestamp = v.get('timestamp', 0)
            timeline_events.append({
                'timestamp': timestamp,
                'type': vtype,
                'details': v,
                'severity': v.get('severity', 'MODERATE')
            })

    # Sort by timestamp
    timeline_events.sort(key=lambda x: x['timestamp'])

    # Format timeline
    timeline_str = "CHRONOLOGICAL VIOLATION TIMELINE:\n"
    timeline_str += "="*50 + "\n\n"

    for event in timeline_events:
        time_str = str(timedelta(seconds=int(event['timestamp'] - skip_seconds)))
        timeline_str += f"[{time_str}] {event['type'].upper()}\n"
        timeline_str += f"   Severity: {event['severity']}\n"

        # Add specific details based on type
        details = event['details']
        if 'audio_evidence' in details:
            timeline_str += f"   Audio: {details['audio_evidence']}\n"
        if 'visual_evidence' in details:
            timeline_str += f"   Visual: {details['visual_evidence'][:100]}...\n"
        if 'violation_type' in details:
            timeline_str += f"   Type: {details['violation_type']}\n"

        timeline_str += "\n"

    return timeline_str

print("✅ ALL enhanced functions loaded successfully!")

# =============================================================================
# Cell 5: Load Enhanced Speaker Diarization Pipeline
# =============================================================================
print("👥 Loading enhanced speaker diarization pipeline...")

try:
    diarization_pipeline = Pipeline.from_pretrained(
        "pyannote/speaker-diarization-3.1",
        use_auth_token=HF_TOKEN
    )
    diarization_pipeline.to(torch.device(device))
    print("✅ Enhanced speaker diarization pipeline loaded successfully!")
except Exception as e:
    print(f"❌ Failed to load speaker diarization: {e}")
    print("Please check your HuggingFace token permissions")

# =============================================================================
# Cell 6: Complete Enhanced Forensic Processing Function WITH ALL ENHANCEMENTS
# =============================================================================
def process_complete_enhanced_forensic_analysis(video_path, skip_seconds=30):
    """
    Complete enhanced forensic pipeline with comprehensive legal analysis
    INCLUDING ALL ORIGINAL FUNCTIONALITY PLUS NEW ENHANCEMENTS:
    - Progressive downloads throughout execution
    - 20-second delays between chunk processing
    - Fixed frame extraction with proper timestamps
    - Clear forensic-grade instructions
    - Comprehensive violation analysis
    """
    import os
    import json
    import hashlib
    from datetime import datetime, timedelta
    from google.colab import files

    print("🏛️ ENHANCED CERTIFIED FORENSIC AUDIOVISUAL ANALYSIS")
    print("="*80)
    print(f"Video: {video_path}")
    print(f"Skip seconds: {skip_seconds}")
    print("="*80 + "\n")

    # Step 1: Extract and enhance audio
    print("🎵 Step 1: Extracting and enhancing audio...")
    audio_raw = "/content/extracted_audio_raw.wav"
    audio_enhanced = "/content/enhanced_forensic_audio_v2.wav"

    extract_cmd = [
        'ffmpeg', '-y', '-ss', str(skip_seconds), '-i', video_path,
        '-acodec', 'pcm_s16le', '-ar', '16000', '-ac', '1', audio_raw
    ]
    subprocess.run(extract_cmd, capture_output=True)

    enhanced_audio_processing_for_difficult_sections(audio_raw, audio_enhanced)

    # Step 2: Transcribe with Whisper
    print("\n📝 Step 2: Transcribing with Whisper Large-v3...")
    whisper_result = transcribe_with_maximum_accuracy_enhanced(audio_enhanced)
    print(f"✅ Transcription complete: {len(whisper_result['segments'])} segments")

    # Save raw whisper result
    whisper_path = "/content/whisper_transcription.json"
    with open(whisper_path, 'w') as f:
        json.dump(whisper_result, f, indent=2)
    print("📥 Downloading raw transcription...")
    files.download(whisper_path)

    # Step 3: Speaker diarization
    print("\n👥 Step 3: Performing speaker diarization...")
    diarization_result = diarization_pipeline(audio_enhanced)

    # Step 4: Detect overlaps
    overlaps = detect_speaker_overlaps_and_separate_enhanced(
        audio_enhanced, diarization_result, whisper_result
    )

    # Step 5: Combine transcription with speakers
    print("\n🔗 Step 5: Combining transcription with speaker identification...")
    enhanced_transcript = combine_transcription_and_speakers_enhanced(
        whisper_result, diarization_result, overlaps
    )

    # CRITICAL: Save early transcript for immediate download
    print("\n💾 Saving speaker-identified transcript...")
    early_transcript_path = "/content/SPEAKER_IDENTIFIED_TRANSCRIPT.txt"

    with open(early_transcript_path, "w", encoding="utf-8") as f:
        f.write("SPEAKER-IDENTIFIED FORENSIC TRANSCRIPT\n")
        f.write("="*60 + "\n\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Video: {os.path.basename(video_path)}\n")
        f.write(f"Skip seconds: {skip_seconds}\n")
        f.write(f"Total words: {len(enhanced_transcript)}\n\n")

        f.write("TRANSCRIPT:\n")
        f.write("-"*60 + "\n\n")

        current_speaker = None
        for word_data in enhanced_transcript:
            word_timestamp = word_data['start'] + skip_seconds
            word_text = word_data['word']
            speakers = word_data.get('speakers', [])
            confidence = word_data.get('confidence', 0.0)

            primary_speaker = speakers[0] if speakers else "UNKNOWN"
            timestamp_str = str(timedelta(seconds=int(word_timestamp)))

            if primary_speaker != current_speaker:
                f.write(f"\n[{timestamp_str}] {primary_speaker}: ")
                current_speaker = primary_speaker

            if confidence < 0.7:
                f.write(f"[{word_text}?] ")
            else:
                f.write(f"{word_text} ")

    print("📥 Downloading speaker-identified transcript...")
    files.download(early_transcript_path)

    # Step 6: Analyze video frames
    print("\n🎥 Step 6: Analyzing video frames...")
    visual_context = analyze_video_frames_for_context_enhanced_attire(
        video_path, skip_seconds
    )

    # Step 7: Inject visual context
    print("\n💉 Step 7: Creating visual context injections...")
    visual_injections = inject_visual_context_into_transcript(
        enhanced_transcript, visual_context, skip_seconds
    )

    # Step 8: Create transcript chunks with rate limiting
    print("\n📄 Step 8: Creating transcript chunks for analysis...")
    chunks = process_transcript_chunks_with_rate_limiting(
        enhanced_transcript, skip_seconds
    )

    # Step 9: Comprehensive legal analysis
    print("\n📋 Step 9: Conducting comprehensive legal analysis...")

    # Cross-reference utterances with behavior
    compliance_violations, behavioral_contradictions = cross_reference_utterances_with_behavior(
        enhanced_transcript, visual_context, skip_seconds
    )

    # Privacy and dignity analysis
    privacy_violations, dignity_violations, public_exposure, attire_violations = analyze_privacy_dignity_violations_enhanced(
        enhanced_transcript, visual_context, skip_seconds
    )

    # Harassment and retaliation analysis
    speaker_counts = {}
    for word_data in enhanced_transcript:
        for speaker in word_data.get('speakers', []):
            speaker_counts[speaker] = speaker_counts.get(speaker, 0) + 1

    harassment_indicators, retaliation_patterns = analyze_harassment_retaliation_patterns(
        enhanced_transcript, speaker_counts
    )

    # Misconduct patterns analysis
    narrative_shaping, coordinated_behavior, selective_enforcement = analyze_misconduct_patterns(
        enhanced_transcript, visual_context
    )

    # Body camera muting patterns
    muting_incidents = analyze_body_camera_muting_patterns(enhanced_transcript, skip_seconds)

    # Officer identities
    officer_identities = extract_officer_identities(enhanced_transcript, visual_context)

    # De-escalation failures
    de_escalation_events, escalation_events = analyze_de_escalation_failures(
        enhanced_transcript, visual_context, skip_seconds
    )

    # Transcript confidence metrics
    confidence_stats = analyze_transcript_confidence_metrics(enhanced_transcript)

    # Step 10: Analyze chunks with GPT-4 using rate limiting
    print("\n⚖️ Step 10: Performing legal analysis with rate limiting...")
    violations_summary = {
        'privacy': len(privacy_violations),
        'dignity': len(dignity_violations),
        'attire': len(attire_violations),
        'compliance': len(compliance_violations)
    }

    chunk_analyses = analyze_chunks_with_gpt4(chunks, violations_summary)

    # Step 11: Generate final comprehensive document
    print("\n📝 Step 11: Generating comprehensive forensic document...")

    # Enhanced contextual annotations
    annotations = inject_contextual_annotations_enhanced(enhanced_transcript)

    output_path = "/content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt"

    with open(output_path, "w", encoding="utf-8") as f:
        # Header
        f.write("COMPREHENSIVE FORENSIC LEGAL ANALYSIS DOCUMENT\n")
        f.write("="*80 + "\n\n")
        f.write("FORENSIC-GRADE ANALYSIS - HIGHEST STANDARDS OF ACCURACY\n")
        f.write("This document applies forensic-grade quality standards\n")
        f.write("(maximum rigor and precision) to the analysis\n\n")

        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Video: {os.path.basename(video_path)}\n")
        f.write(f"Duration Analyzed: {len(enhanced_transcript)} words\n")
        f.write(f"Frames Analyzed: {len(visual_context)}\n\n")

        # Executive Summary
        f.write("EXECUTIVE SUMMARY:\n")
        f.write("="*40 + "\n")
        f.write(f"Total Chunks Analyzed: {len(chunk_analyses)}\n")
        f.write(f"Visual Context Injections: {len(visual_injections)}\n")
        f.write(f"Speaker Overlaps Detected: {len(overlaps)}\n")
        f.write(f"Compliance Violations: {len(compliance_violations)}\n")
        f.write(f"Privacy Violations: {len(privacy_violations)}\n")
        f.write(f"Dignity Violations: {len(dignity_violations)}\n")
        f.write(f"Attire Violations: {len(attire_violations)}\n")
        f.write(f"Public Exposure Incidents: {len(public_exposure)}\n")
        f.write(f"Harassment Indicators: {len(harassment_indicators)}\n")
        f.write(f"Body Camera Muting References: {len(muting_incidents)}\n\n")

        # Legal Analysis
        f.write("LEGAL ANALYSIS BY CHUNK:\n")
        f.write("="*40 + "\n\n")

        for i, analysis in enumerate(chunk_analyses):
            f.write(f"--- Chunk {i+1} of {len(chunks)} ---\n")
            f.write(analysis)
            f.write("\n\n")

        # Detailed Violation Analysis
        f.write("DETAILED VIOLATION ANALYSIS:\n")
        f.write("="*35 + "\n\n")

        # Privacy violations
        if privacy_violations:
            f.write("PRIVACY VIOLATIONS:\n")
            f.write("-"*20 + "\n")
            for i, violation in enumerate(privacy_violations, 1):
                timestamp_str = str(timedelta(seconds=int(violation['timestamp'])))
                f.write(f"{i}. [{timestamp_str}] {violation['violation_type']}\n")
                f.write(f"   Audio: {violation.get('audio_evidence', 'N/A')}\n")
                f.write(f"   Visual: {violation.get('visual_evidence', 'N/A')[:200]}...\n\n")

        # Attire violations
        if attire_violations:
            f.write("ATTIRE/CLOTHING PRIVACY CONCERNS:\n")
            f.write("-"*35 + "\n")
            for i, violation in enumerate(attire_violations, 1):
                timestamp_str = str(timedelta(seconds=int(violation['timestamp'])))
                f.write(f"{i}. [{timestamp_str}] {violation['violation_type']}\n")
                f.write(f"   Audio: {violation.get('audio_evidence', 'N/A')}\n")
                f.write(f"   Speakers: {', '.join(violation['speakers'])}\n\n")

        # Dignity violations
        if dignity_violations:
            f.write("DIGNITY VIOLATIONS:\n")
            f.write("-"*20 + "\n")
            for i, violation in enumerate(dignity_violations, 1):
                timestamp_str = str(timedelta(seconds=int(violation['timestamp'])))
                f.write(f"{i}. [{timestamp_str}] {violation['violation_type']}\n")
                f.write(f"   Severity: {violation.get('severity', 'MODERATE')}\n")
                f.write(f"   Constitutional: {violation.get('constitutional_concern', 'General dignity')}\n\n")

        # Public exposure incidents
        if public_exposure:
            f.write("PUBLIC EXPOSURE INCIDENTS:\n")
            f.write("-"*30 + "\n")
            for i, incident in enumerate(public_exposure, 1):
                timestamp_str = str(timedelta(seconds=int(incident['timestamp'])))
                f.write(f"{i}. [{timestamp_str}] {incident['violation_type']}\n")
                f.write(f"   Severity: {incident.get('severity', 'Unknown')}\n")
                f.write(f"   Clothing Status: {incident.get('clothing_status', 'Unknown')}\n")
                f.write(f"   Restraint Status: {incident.get('restraint_status', 'UNKNOWN')}\n\n")

        # Enhanced timeline
        all_violations = {
            'compliance': compliance_violations,
            'privacy': privacy_violations,
            'dignity': dignity_violations,
            'attire': attire_violations,
            'harassment': harassment_indicators,
            'muting': muting_incidents
        }
        violation_timeline = generate_violation_timeline(all_violations, skip_seconds)
        f.write("\n" + violation_timeline + "\n")

        # Audio quality report
        audio_quality_report = generate_audio_quality_report(enhanced_transcript, overlaps, confidence_stats)
        f.write("\n" + audio_quality_report + "\n")

        # Officer identities
        if officer_identities:
            f.write("IDENTIFIED OFFICER INFORMATION:\n")
            f.write("="*35 + "\n")
            for officer_id, data in officer_identities.items():
                if 'name' in data:
                    f.write(f"\n{officer_id}:\n")
                    f.write(f"   Name: {data['name']}\n")
                    f.write(f"   Title: {data.get('title', 'Unknown')}\n")
                    f.write(f"   Total Mentions: {len(data.get('mentions', []))}\n")
            f.write("\n")

        # Full Transcript with Visual Injections
        f.write("\nFULL TRANSCRIPT WITH VISUAL CONTEXT:\n")
        f.write("="*40 + "\n\n")

        current_speaker = None
        for i, word_data in enumerate(enhanced_transcript):
            word_timestamp = word_data['start'] + skip_seconds
            word_text = word_data['word']
            speakers = word_data.get('speakers', [])
            primary_speaker = speakers[0] if speakers else "UNKNOWN"
            timestamp_str = str(timedelta(seconds=int(word_timestamp)))

            # Inject visual context if available
            if i in visual_injections:
                f.write(visual_injections[i] + "\n")

            # Inject annotations
            if i in annotations:
                if isinstance(annotations[i], list):
                    for annotation in annotations[i]:
                        f.write(f"{annotation}\n")
                else:
                    f.write(f"{annotations[i]}\n")

            # Write transcript
            if primary_speaker != current_speaker:
                f.write(f"\n[{timestamp_str}] {primary_speaker}: ")
                current_speaker = primary_speaker
            f.write(f"{word_text} ")

        f.write("\n\n[END OF ANALYSIS]")

    print(f"\n📥 Downloading comprehensive analysis...")
    files.download(output_path)

    print("\n✅ FORENSIC ANALYSIS COMPLETE!")
    print(f"Downloaded files:")
    print("- Whisper transcription (JSON)")
    print("- Speaker-identified transcript (TXT)")
    print("- Visual frame analyses (JSON)")
    print("- Frame batches (ZIP files)")
    print("- Transcript chunks (JSON)")
    print("- Chunk analyses (JSON)")
    print("- Comprehensive analysis (TXT)")

    return output_path

print("✅ Complete enhanced processing function ready!")

# =============================================================================
# Cell 7: Execute Analysis
# =============================================================================
print("🚀 EXECUTING ENHANCED COMPLETE FORENSIC ANALYSIS...")

video_path = f"/content/{video_filename}"  # Uses filename from Cell 2
SKIP_SECONDS = 30  # Adjust based on video

result_file = process_complete_enhanced_forensic_analysis(
    video_path,
    skip_seconds=SKIP_SECONDS
)

print("🎉 ENHANCED FORENSIC ANALYSIS COMPLETE!")
print("✅ All original functionality preserved and enhanced")
print("✅ Progressive downloads throughout execution")
print("✅ Fixed GPT-4 Vision model (now using gpt-4o)")
print("✅ 20-second delays between chunk processing")
print("✅ Proper frame timestamping with skip_seconds adjustment")
print("✅ Clear forensic-grade instructions to prevent AI refusal")
print("✅ Comprehensive violation analysis and timeline")
print("✅ Enhanced privacy, dignity, and attire analysis")
print("✅ All 165KB of original functionality restored")

# =============================================================================
# Cell 8: Frame Upload Cell for Current Video (Reuse Existing Frames)
# =============================================================================
# SPECIAL CELL FOR THIS VIDEO: Upload existing frames to avoid re-extraction

print("📤 FRAME UPLOAD CELL - REUSE EXISTING FRAMES")
print("="*50)
print("This cell allows you to upload your existing 177 frames")
print("to avoid re-extracting and re-analyzing them.")
print("="*50)

def upload_existing_frames():
    """Upload and process existing frame archive"""
    from google.colab import files
    import zipfile
    import os
    
    print("📤 Please select your frames.zip file to upload...")
    uploaded = files.upload()
    
    frames_dir = "/content/uploaded_frames"
    os.makedirs(frames_dir, exist_ok=True)
    
    # Extract uploaded frames
    for filename in uploaded.keys():
        if filename.endswith('.zip'):
            with zipfile.ZipFile(filename, 'r') as zip_ref:
                zip_ref.extractall(frames_dir)
            print(f"✅ Extracted frames from {filename}")
            
            # List extracted frames
            frame_files = [f for f in os.listdir(frames_dir) if f.endswith('.jpg') or f.endswith('.png')]
            frame_files.sort()
            
            print(f"✅ Found {len(frame_files)} frame files")
            print("First 5 frames:", frame_files[:5])
            
            return frames_dir, frame_files
    
    return None, []

# Uncomment the next line to use existing frames:
# frames_directory, existing_frames = upload_existing_frames()

print("✅ Frame upload function ready")
print("Note: Uncomment the function call above to upload existing frames")
