# OPTION C: CO<PERSON>LE<PERSON> GUIDE FOR EXTERNAL ANALYSIS
# ==============================================

import os
from datetime import datetime
from google.colab import files

def export_chunks_for_external_analysis():
    """
    Export remaining 17 chunks with instructions and analysis templates
    """
    print("📤 PREPARING CHUNKS FOR EXTERNAL ANALYSIS...\n")
    
    # Check if transcript chunks exist
    if 'transcript_chunks' not in globals():
        print("❌ No transcript chunks found in memory!")
        print("Attempting to recreate chunks from enhanced_transcript...")
        
        if 'enhanced_transcript' not in globals():
            print("❌ No transcript data available. Cannot proceed.")
            return
        
        # Recreate chunks
        global transcript_chunks
        transcript_chunks = []
        current_chunk = []
        current_size = 0
        max_size = 6000
        
        for word_data in enhanced_transcript:
            word_timestamp = word_data['start'] + 30  # assuming skip_seconds=30
            word_text = word_data['word']
            speakers = word_data.get('speakers', [])
            primary_speaker = speakers[0] if speakers else "UNKNOWN"
            
            from datetime import timedelta
            timestamp_str = str(timedelta(seconds=int(word_timestamp)))
            line = f"[{timestamp_str}] {primary_speaker}: {word_text} "
            line_size = len(line)
            
            if current_size + line_size > max_size and current_chunk:
                transcript_chunks.append(''.join(current_chunk))
                current_chunk = [line]
                current_size = line_size
            else:
                current_chunk.append(line)
                current_size += line_size
        
        if current_chunk:
            transcript_chunks.append(''.join(current_chunk))
        
        print(f"✅ Recreated {len(transcript_chunks)} chunks")
    
    # Create comprehensive export file
    output_path = "/content/CHUNKS_7-23_FOR_EXTERNAL_ANALYSIS.txt"
    
    with open(output_path, "w", encoding="utf-8") as f:
        # Header with instructions
        f.write("TRANSCRIPT CHUNKS 7-23 FOR EXTERNAL LEGAL ANALYSIS\n")
        f.write("="*70 + "\n\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total chunks to analyze: {min(17, len(transcript_chunks)-6)}\n")
        f.write(f"Video duration: ~59 minutes\n")
        f.write(f"Coverage: Chunks 7-23 cover approximately minutes 15-59\n\n")
        
        f.write("BACKGROUND CONTEXT:\n")
        f.write("-"*50 + "\n")
        f.write("- Police body camera footage from mental health response\n")
        f.write("- Subject reportedly in minimal clothing (towel) when detained\n")
        f.write("- Baker Act (involuntary mental health) situation\n")
        f.write("- Multiple officers present\n")
        f.write("- Analysis needed for constitutional violations, dignity concerns\n\n")
        
        f.write("INSTRUCTIONS FOR EXTERNAL ANALYSIS:\n")
        f.write("-"*50 + "\n")
        f.write("1. Copy each chunk below into ChatGPT, Claude, or similar AI\n")
        f.write("2. Use the provided analysis prompt for each chunk\n")
        f.write("3. Save each chunk's analysis with its chunk number\n")
        f.write("4. Compile all analyses into final document\n\n")
        
        f.write("RECOMMENDED ANALYSIS PROMPT FOR EACH CHUNK:\n")
        f.write("-"*50 + "\n")
        f.write('"""\n')
        f.write("You are a forensic legal analyst reviewing police body camera transcript.\n")
        f.write("Analyze this transcript section for:\n\n")
        f.write("1. CONSTITUTIONAL VIOLATIONS:\n")
        f.write("   - 4th Amendment (unreasonable search/seizure)\n")
        f.write("   - 5th Amendment (Miranda, self-incrimination)\n")
        f.write("   - 8th Amendment (cruel treatment, dignity)\n")
        f.write("   - 14th Amendment (due process)\n\n")
        f.write("2. SPECIFIC CONCERNS:\n")
        f.write("   - Handcuffing person in towel/minimal clothing\n")
        f.write("   - Public exposure and dignity violations\n")
        f.write("   - Mental health crisis handling\n")
        f.write("   - Use of force on cooperative subject\n")
        f.write("   - Baker Act procedural compliance\n\n")
        f.write("3. IDENTIFY:\n")
        f.write("   - Exact quotes showing violations\n")
        f.write("   - Timestamps of concerning events\n")
        f.write("   - Officer statements showing intent\n")
        f.write("   - Evidence of retaliation or punishment\n\n")
        f.write("Provide specific timestamps and quotes for any violations found.\n")
        f.write('"""\n\n')
        
        # Export each remaining chunk
        start_chunk = 6  # Start from chunk 7 (0-indexed)
        end_chunk = min(23, len(transcript_chunks))
        
        for i in range(start_chunk, end_chunk):
            f.write(f"\n{'='*70}\n")
            f.write(f"CHUNK {i+1} of 23\n")
            f.write(f"Approximate time coverage: {15 + (i-6)*2} - {17 + (i-6)*2} minutes\n")
            f.write(f"{'='*70}\n\n")
            
            # Add chunk content
            if i < len(transcript_chunks):
                f.write(transcript_chunks[i])
            else:
                f.write("[Chunk data not available]")
            
            f.write("\n\n--- END OF CHUNK ---\n\n")
        
        # Add compilation template
        f.write("\n" + "="*70 + "\n")
        f.write("ANALYSIS COMPILATION TEMPLATE:\n")
        f.write("="*70 + "\n\n")
        f.write("After analyzing all chunks, compile findings as follows:\n\n")
        f.write("COMPREHENSIVE LEGAL ANALYSIS - CHUNKS 7-23\n")
        f.write("-"*40 + "\n\n")
        f.write("1. CONSTITUTIONAL VIOLATIONS FOUND:\n")
        f.write("   - 4th Amendment: [List violations with timestamps]\n")
        f.write("   - 5th Amendment: [List violations with timestamps]\n")
        f.write("   - 8th Amendment: [List violations with timestamps]\n")
        f.write("   - 14th Amendment: [List violations with timestamps]\n\n")
        f.write("2. DIGNITY AND PRIVACY VIOLATIONS:\n")
        f.write("   - [List all instances with timestamps]\n\n")
        f.write("3. PROCEDURAL VIOLATIONS:\n")
        f.write("   - [List Baker Act and policy violations]\n\n")
        f.write("4. USE OF FORCE CONCERNS:\n")
        f.write("   - [List all force applications with justification analysis]\n\n")
        f.write("5. KEY QUOTES AND EVIDENCE:\n")
        f.write("   - [List most damaging quotes with speakers and timestamps]\n\n")
        f.write("6. PATTERN ANALYSIS:\n")
        f.write("   - [Identify patterns of misconduct across chunks]\n\n")
    
    print(f"✅ Export file created with {end_chunk - start_chunk} chunks")
    print("📥 Downloading export file...")
    files.download(output_path)
    
    # Also create a simplified version for easier copying
    simple_path = "/content/CHUNKS_SIMPLE.txt"
    with open(simple_path, "w", encoding="utf-8") as f:
        f.write("SIMPLIFIED CHUNKS FOR QUICK COPYING\n")
        f.write("="*50 + "\n\n")
        
        for i in range(start_chunk, min(end_chunk, len(transcript_chunks))):
            f.write(f"\n--- CHUNK {i+1} ---\n\n")
            f.write(transcript_chunks[i])
            f.write("\n\n")
    
    files.download(simple_path)
    
    print("\n✅ EXPORT COMPLETE!")
    print("\n📋 YOU NOW HAVE:")
    print("1. CHUNKS_7-23_FOR_EXTERNAL_ANALYSIS.txt - Full guide with prompts")
    print("2. CHUNKS_SIMPLE.txt - Just the chunks for easy copying")
    print("\n🔍 NEXT STEPS:")
    print("1. Open the export file")
    print("2. Copy each chunk to your preferred AI tool")
    print("3. Use the provided analysis prompt")
    print("4. Save each analysis")
    print("5. Compile using the template at the end")
    
    return True


def create_quick_reference_guide():
    """
    Create a quick reference for what to look for in external analysis
    """
    guide_path = "/content/EXTERNAL_ANALYSIS_CHECKLIST.txt"
    
    with open(guide_path, "w", encoding="utf-8") as f:
        f.write("QUICK REFERENCE CHECKLIST FOR EXTERNAL ANALYSIS\n")
        f.write("="*50 + "\n\n")
        
        f.write("☐ PRIORITY RED FLAGS TO IDENTIFY:\n")
        f.write("  ☐ Subject says 'towel' or 'naked' or 'cover'\n")
        f.write("  ☐ Officers discuss 'cuffing' person in towel\n")
        f.write("  ☐ References to 'shower' or 'bathroom'\n")
        f.write("  ☐ Public exposure mentions\n")
        f.write("  ☐ Crowd/neighbor presence during minimal clothing\n\n")
        
        f.write("☐ CONSTITUTIONAL MARKERS:\n")
        f.write("  ☐ 'Miranda' or 'rights' not given\n")
        f.write("  ☐ 'Search' without consent/warrant\n")
        f.write("  ☐ Force used on cooperative subject\n")
        f.write("  ☐ Dignity violations during detention\n\n")
        
        f.write("☐ BAKER ACT VIOLATIONS:\n")
        f.write("  ☐ No immediate danger established\n")
        f.write("  ☐ No attempt at voluntary compliance\n")
        f.write("  ☐ Improper transportation methods\n")
        f.write("  ☐ Excessive restraints for mental health\n\n")
        
        f.write("☐ CONCERNING OFFICER STATEMENTS:\n")
        f.write("  ☐ Threats or intimidation\n")
        f.write("  ☐ Retaliation mentions\n")
        f.write("  ☐ Cover-up discussions\n")
        f.write("  ☐ Camera muting references\n\n")
        
        f.write("☐ ESCALATION INDICATORS:\n")
        f.write("  ☐ SWAT or tactical mentions\n")
        f.write("  ☐ Weapon displays to cooperative subject\n")
        f.write("  ☐ Multiple officers for one person\n")
        f.write("  ☐ Failure to de-escalate\n\n")
    
    files.download(guide_path)
    print("✅ Quick reference checklist downloaded!")


# MAIN EXECUTION
print("🚀 STARTING OPTION C: EXTERNAL ANALYSIS EXPORT\n")

# Run the export
success = export_chunks_for_external_analysis()

if success:
    print("\n📋 Creating quick reference checklist...")
    create_quick_reference_guide()
    
    print("\n" + "="*70)
    print("✅ OPTION C EXPORT COMPLETE!")
    print("="*70)
    print("\nYou now have everything needed for external analysis:")
    print("- Full chunks with analysis prompts")
    print("- Simplified chunks for easy copying") 
    print("- Quick reference checklist")
    print("\nThe external analysis will likely produce BETTER results because:")
    print("- No rate limits")
    print("- More interactive analysis")
    print("- Ability to ask follow-up questions")
    print("- Can use multiple AI tools for comparison")