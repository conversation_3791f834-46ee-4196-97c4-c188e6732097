# RECOVERY CELL - Add this as Cell 8 or run separately
# =====================================================
# Run this cell to complete analysis of failed chunks

import time
import openai
from datetime import datetime, timedelta
from google.colab import files

def recover_failed_analysis():
    """
    Attempt to complete the analysis using the existing transcript and partial results
    """
    print("🔧 ATTEMPTING TO RECOVER AND COMPLETE ANALYSIS...")
    
    # Check what we have in memory
    if 'enhanced_transcript' not in globals():
        print("❌ No transcript found in memory. Please re-run the pipeline.")
        return
    
    print("✅ Found transcript in memory")
    
    # Try to complete a simplified analysis
    try:
        # Create a condensed summary of the transcript
        print("\n📝 Creating condensed analysis...")
        
        # Extract key sections with legal significance
        key_sections = []
        legal_keywords = ['miranda', 'rights', 'force', 'weapon', 'cuff', 'handcuff', 
                         'towel', 'naked', 'arrest', 'resist', 'comply', 'baker act',
                         'mental health', 'privacy', 'dignity', 'camera', 'mute']
        
        for i, word_data in enumerate(enhanced_transcript):
            word_text = word_data['word'].lower()
            if any(keyword in word_text for keyword in legal_keywords):
                # Get context
                start_idx = max(0, i - 10)
                end_idx = min(len(enhanced_transcript), i + 10)
                
                context_words = []
                for j in range(start_idx, end_idx):
                    context_words.append(enhanced_transcript[j]['word'])
                
                timestamp = word_data['start'] + (skip_seconds if 'skip_seconds' in globals() else 30)
                timestamp_str = str(timedelta(seconds=int(timestamp)))
                
                key_sections.append({
                    'timestamp': timestamp_str,
                    'keyword': word_text,
                    'context': ' '.join(context_words)
                })
        
        print(f"✅ Identified {len(key_sections)} key sections")
        
        # Create simplified analysis document
        output_path = "/content/SIMPLIFIED_LEGAL_ANALYSIS.txt"
        
        with open(output_path, "w", encoding="utf-8") as f:
            f.write("SIMPLIFIED LEGAL ANALYSIS - RATE LIMIT RECOVERY\n")
            f.write("="*60 + "\n\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("KEY LEGAL SECTIONS IDENTIFIED:\n")
            f.write("-"*40 + "\n\n")
            
            for section in key_sections[:50]:  # First 50 key sections
                f.write(f"[{section['timestamp']}] KEYWORD: {section['keyword']}\n")
                f.write(f"CONTEXT: {section['context']}\n\n")
            
            # Add violation summary if available
            if 'all_violations' in globals():
                f.write("\n" + "="*60 + "\n")
                f.write("VIOLATION SUMMARY FROM INITIAL ANALYSIS:\n")
                f.write("-"*40 + "\n\n")
                
                for vtype, violations in all_violations.items():
                    if violations:
                        f.write(f"\n{vtype.upper()}: {len(violations)} incidents\n")
                        for v in violations[:3]:  # First 3 examples
                            if 'timestamp' in v:
                                ts = str(timedelta(seconds=int(v['timestamp'])))
                                f.write(f"  - [{ts}] {v.get('violation_type', 'Violation')}\n")
            
            f.write("\n" + "="*60 + "\n")
            f.write("ANALYSIS NOTES:\n")
            f.write("- This is a simplified analysis due to API rate limits\n")
            f.write("- Full GPT-4 analysis was partially completed\n")
            f.write("- Key legal sections have been extracted for review\n")
            f.write("- Consider manual review of these sections\n")
        
        print("📥 Downloading simplified analysis...")
        files.download(output_path)
        print("✅ Simplified analysis complete!")
        
        # Try one more GPT-3.5 summary
        print("\n🤖 Attempting GPT-3.5 summary...")
        try:
            summary_text = f"Transcript has {len(key_sections)} legally significant sections. "
            summary_text += f"Key concerns include: {', '.join(set(s['keyword'] for s in key_sections[:20]))}"
            
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {
                        "role": "system",
                        "content": "You are a legal analyst. Based on these keywords from a police encounter, identify the main legal concerns."
                    },
                    {
                        "role": "user",
                        "content": summary_text
                    }
                ],
                max_tokens=500,
                temperature=0.1
            )
            
            with open("/content/GPT35_SUMMARY.txt", "w") as f:
                f.write("GPT-3.5 LEGAL SUMMARY\n")
                f.write("="*30 + "\n\n")
                f.write(response.choices[0].message.content)
            
            files.download("/content/GPT35_SUMMARY.txt")
            print("✅ GPT-3.5 summary complete!")
            
        except Exception as e:
            print(f"⚠️ GPT-3.5 summary also failed: {e}")
    
    except Exception as e:
        print(f"❌ Recovery failed: {e}")


# Option 2: Manual chunk processing with user control
def process_single_chunk_manually(chunk_number):
    """
    Process a single chunk manually when ready
    """
    if 'transcript_chunks' not in globals():
        print("❌ No chunks found. Please prepare chunks first.")
        return
    
    if chunk_number >= len(transcript_chunks):
        print(f"❌ Invalid chunk number. Total chunks: {len(transcript_chunks)}")
        return
    
    print(f"Processing chunk {chunk_number + 1}...")
    
    try:
        response = openai.ChatCompletion.create(
            model="gpt-4",
            messages=[
                {
                    "role": "system",
                    "content": "Analyze this police transcript section for legal violations."
                },
                {
                    "role": "user",
                    "content": transcript_chunks[chunk_number][:4000]
                }
            ],
            max_tokens=1000,
            temperature=0.1
        )
        
        print("✅ Analysis complete:")
        print(response.choices[0].message.content)
        
    except Exception as e:
        print(f"❌ Failed: {e}")


# Option 3: Export for external analysis
def export_for_external_analysis():
    """
    Export transcript chunks for analysis outside of Colab
    """
    if 'transcript_chunks' not in globals():
        print("❌ No chunks found")
        return
    
    output_path = "/content/TRANSCRIPT_CHUNKS_FOR_ANALYSIS.txt"
    
    with open(output_path, "w", encoding="utf-8") as f:
        f.write("TRANSCRIPT CHUNKS FOR EXTERNAL ANALYSIS\n")
        f.write("="*50 + "\n\n")
        
        for i, chunk in enumerate(transcript_chunks):
            f.write(f"\n{'='*50}\n")
            f.write(f"CHUNK {i+1} of {len(transcript_chunks)}\n")
            f.write(f"{'='*50}\n\n")
            f.write(chunk)
            f.write("\n\n")
    
    files.download(output_path)
    print(f"✅ Exported {len(transcript_chunks)} chunks for external analysis")


# MAIN RECOVERY FUNCTION
print("🚑 RECOVERY OPTIONS AVAILABLE:")
print("1. recover_failed_analysis() - Create simplified analysis from transcript")
print("2. process_single_chunk_manually(chunk_num) - Process one chunk at a time")
print("3. export_for_external_analysis() - Export chunks for external processing")

# Run the main recovery
recover_failed_analysis()