# COMPLETE FIXED MAIN PROCESSING FUNCTION
# =======================================
# This replaces your process_complete_enhanced_forensic_analysis function in Cell 6

def process_complete_enhanced_forensic_analysis_fixed(video_path, skip_seconds=30):
    """
    Fixed version with:
    - Token limit handling for GPT-4
    - Early transcript download
    - Frame analysis caching
    - Proper error handling
    """
    import os
    import json
    import hashlib
    from datetime import datetime, timedelta
    from google.colab import files
    
    print(f"\n{'='*60}")
    print(f"🎬 STARTING COMPLETE ENHANCED FORENSIC ANALYSIS")
    print(f"Video: {video_path}")
    print(f"Skip seconds: {skip_seconds}")
    print(f"{'='*60}\n")
    
    # Step 1: Extract and enhance audio
    print("🎵 Step 1: Extracting and enhancing audio...")
    raw_audio_path = "/content/extracted_audio_raw.wav"
    enhanced_audio_path = "/content/enhanced_forensic_audio_v2.wav"
    
    extract_audio_cmd = [
        'ffmpeg', '-y', '-ss', str(skip_seconds), '-i', video_path,
        '-vn', '-acodec', 'pcm_s16le', '-ar', '16000', '-ac', '1', raw_audio_path
    ]
    subprocess.run(extract_audio_cmd, capture_output=True)
    
    enhance_forensic_audio_multipass(raw_audio_path, enhanced_audio_path)
    
    # Step 2: Transcribe with Whisper
    print("\n📝 Step 2: Transcribing with Whisper Large-v3...")
    whisper_result = transcribe_with_whisper_large_v3(enhanced_audio_path)
    print(f"✅ Transcription complete: {len(whisper_result['segments'])} segments")
    
    # Step 3: Speaker diarization
    print("\n👥 Step 3: Performing speaker diarization...")
    diarization_result = perform_speaker_diarization_enhanced(enhanced_audio_path)
    
    # Step 4: Combine transcription with speakers
    print("\n🔗 Step 4: Combining transcription with speaker identification...")
    enhanced_transcript, overlaps = combine_transcription_and_speakers_enhanced(
        whisper_result, diarization_result
    )
    
    # CRITICAL FIX: SAVE EARLY TRANSCRIPT FOR IMMEDIATE DOWNLOAD
    print("\n💾 Saving early transcript for download...")
    early_path = "/content/EARLY_TRANSCRIPT_ONLY.txt"
    
    try:
        with open(early_path, "w", encoding="utf-8") as f:
            f.write("EARLY FORENSIC TRANSCRIPT - SPEAKER IDENTIFIED\n")
            f.write("="*60 + "\n\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Video: {os.path.basename(video_path)}\n")
            f.write(f"Skip seconds: {skip_seconds}\n")
            f.write(f"Total words: {len(enhanced_transcript)}\n")
            f.write(f"Total segments: {len(whisper_result['segments'])}\n\n")
            
            f.write("FULL TRANSCRIPT WITH SPEAKER IDENTIFICATION:\n")
            f.write("-"*60 + "\n\n")
            
            current_speaker = None
            
            for word_data in enhanced_transcript:
                word_timestamp = word_data['start'] + skip_seconds
                word_text = word_data['word']
                speakers = word_data.get('speakers', [])
                confidence = word_data.get('confidence', 0.0)
                
                primary_speaker = speakers[0] if speakers else "UNKNOWN"
                timestamp_str = str(timedelta(seconds=int(word_timestamp)))
                
                if primary_speaker != current_speaker:
                    f.write(f"\n[{timestamp_str}] {primary_speaker}: ")
                    current_speaker = primary_speaker
                
                # Add confidence indicator for low confidence words
                if confidence < 0.7:
                    f.write(f"[{word_text}?] ")
                else:
                    f.write(f"{word_text} ")
            
            f.write("\n\n[END OF TRANSCRIPT]")
        
        print("📥 Downloading early transcript now...")
        files.download(early_path)
        print("✅ Early transcript downloaded! Continuing with full analysis...\n")
        
    except Exception as e:
        print(f"⚠️ Early transcript save failed: {e}")
    
    # Step 5: Analyze video frames with CACHING
    print("\n🎥 Step 5: Analyzing video frames (with caching)...")
    
    # Create cache directory
    cache_dir = "/content/frame_analysis_cache"
    os.makedirs(cache_dir, exist_ok=True)
    
    # Generate cache key
    video_name = os.path.basename(video_path)
    cache_key = hashlib.md5(f"{video_name}_{skip_seconds}".encode()).hexdigest()
    cache_file = os.path.join(cache_dir, f"{cache_key}_analysis.json")
    
    # Check cache first
    if os.path.exists(cache_file):
        print("📂 Found cached frame analysis - loading...")
        with open(cache_file, 'r') as f:
            visual_context = json.load(f)
        print(f"✅ Loaded {len(visual_context)} cached frame analyses")
    else:
        print("🆕 No cache found - performing new frame analysis...")
        visual_context = analyze_video_frames_for_context_enhanced_attire(
            video_path, skip_seconds
        )
        # Save to cache
        with open(cache_file, 'w') as f:
            json.dump(visual_context, f, indent=2)
        print(f"💾 Saved {len(visual_context)} frame analyses to cache")
    
    # Step 6: Inject visual context
    print("\n💉 Step 6: Injecting visual context into transcript...")
    visual_injections = inject_visual_context_into_transcript(
        enhanced_transcript, visual_context, skip_seconds
    )
    
    # Step 7: Legal annotations
    print("\n⚖️ Step 7: Adding legal/contextual annotations...")
    legal_annotations = inject_contextual_annotations_enhanced(enhanced_transcript)
    
    # Step 8: Officer identification
    print("\n👮 Step 8: Extracting officer identities...")
    try:
        officer_identities = extract_officer_identities(enhanced_transcript, visual_context)
    except Exception as e:
        print(f"⚠️ Officer identification failed: {e}")
        officer_identities = []
    
    # Step 9: Confidence metrics
    print("\n📊 Step 9: Analyzing transcript confidence...")
    confidence_stats = analyze_transcript_confidence_metrics(enhanced_transcript)
    
    # Step 10: Audio quality report
    print("\n🔊 Step 10: Generating audio quality report...")
    audio_quality_report = generate_audio_quality_report(
        enhanced_transcript, overlaps, confidence_stats
    )
    
    # Step 11: CHUNKED Legal Analysis (FIX FOR TOKEN LIMIT)
    print("\n⚖️ Step 11: Performing chunked comprehensive legal analysis...")
    
    try:
        # Prepare transcript chunks for analysis
        transcript_chunks = []
        current_chunk = []
        current_chunk_size = 0
        max_chunk_size = 6000  # Conservative limit
        
        for word_data in enhanced_transcript:
            word_timestamp = word_data['start'] + skip_seconds
            word_text = word_data['word']
            speakers = word_data.get('speakers', [])
            primary_speaker = speakers[0] if speakers else "UNKNOWN"
            timestamp_str = str(timedelta(seconds=int(word_timestamp)))
            
            line = f"[{timestamp_str}] {primary_speaker}: {word_text} "
            line_size = len(line)
            
            if current_chunk_size + line_size > max_chunk_size and current_chunk:
                transcript_chunks.append(''.join(current_chunk))
                current_chunk = [line]
                current_chunk_size = line_size
            else:
                current_chunk.append(line)
                current_chunk_size += line_size
        
        if current_chunk:
            transcript_chunks.append(''.join(current_chunk))
        
        print(f"📄 Split transcript into {len(transcript_chunks)} chunks for analysis")
        
        # Analyze each chunk
        chunk_analyses = []
        
        for i, chunk in enumerate(transcript_chunks):
            print(f"🔄 Analyzing chunk {i+1}/{len(transcript_chunks)}...")
            
            try:
                response = openai.ChatCompletion.create(
                    model="gpt-4",
                    messages=[
                        {
                            "role": "system",
                            "content": """You are a senior forensic analyst specializing in law enforcement interactions.
                            Analyze this transcript section for legal violations, focusing on:
                            - Constitutional violations (4th, 5th, 8th, 14th Amendment)
                            - Privacy and dignity violations (especially regarding state of undress)
                            - Use of force concerns
                            - Mental health handling under Baker Act
                            - Procedural violations"""
                        },
                        {
                            "role": "user",
                            "content": f"""Analyze transcript section {i+1} of {len(transcript_chunks)}:

{chunk}

Identify specific violations with timestamps and quotes."""
                        }
                    ],
                    max_tokens=1500,  # Conservative per-chunk limit
                    temperature=0.1
                )
                
                chunk_analyses.append(response.choices[0].message.content)
                print(f"✅ Chunk {i+1} analyzed successfully")
                
            except Exception as e:
                print(f"❌ Chunk {i+1} analysis failed: {e}")
                chunk_analyses.append(f"Analysis failed for chunk {i+1}: {str(e)}")
        
        # Combine analyses
        comprehensive_analysis = "\n\n".join(chunk_analyses)
        
    except Exception as e:
        print(f"❌ Comprehensive analysis failed: {e}")
        comprehensive_analysis = f"Comprehensive analysis unavailable: {str(e)}"
    
    # Step 12: Generate final report
    print("\n📄 Step 12: Generating final comprehensive report...")
    
    output_path = "/content/COMPREHENSIVE_FORENSIC_LEGAL_ANALYSIS.txt"
    
    try:
        with open(output_path, "w", encoding="utf-8") as f:
            # Header
            f.write("COMPREHENSIVE FORENSIC LEGAL ANALYSIS\n")
            f.write("="*80 + "\n\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Video File: {os.path.basename(video_path)}\n")
            f.write(f"Skip Seconds: {skip_seconds}\n")
            f.write(f"Total Duration Analyzed: {len(enhanced_transcript)} words\n\n")
            
            # Audio quality report
            f.write(audio_quality_report)
            f.write("\n" + "="*80 + "\n\n")
            
            # Officer identities
            f.write("IDENTIFIED OFFICERS:\n")
            f.write("-"*40 + "\n")
            for officer in officer_identities:
                f.write(f"- {officer}\n")
            f.write("\n")
            
            # Legal analysis
            f.write("COMPREHENSIVE LEGAL ANALYSIS:\n")
            f.write("="*80 + "\n\n")
            f.write(comprehensive_analysis)
            f.write("\n\n")
            
            # Full transcript with annotations
            f.write("FULL ANNOTATED TRANSCRIPT:\n")
            f.write("="*80 + "\n\n")
            
            current_speaker = None
            
            for i, word_data in enumerate(enhanced_transcript):
                word_timestamp = word_data['start'] + skip_seconds
                word_text = word_data['word']
                speakers = word_data.get('speakers', [])
                confidence = word_data.get('confidence', 0.0)
                
                primary_speaker = speakers[0] if speakers else "UNKNOWN"
                timestamp_str = str(timedelta(seconds=int(word_timestamp)))
                
                # Speaker change
                if primary_speaker != current_speaker:
                    f.write(f"\n[{timestamp_str}] {primary_speaker}: ")
                    current_speaker = primary_speaker
                
                # Write word with confidence indicator
                if confidence < 0.7:
                    f.write(f"[{word_text}?] ")
                else:
                    f.write(f"{word_text} ")
                
                # Add visual injections
                if i in visual_injections:
                    f.write(f"\n   {visual_injections[i]}\n   ")
                
                # Add legal annotations
                if i in legal_annotations:
                    for annotation in legal_annotations[i]:
                        f.write(f"\n   {annotation}\n   ")
            
            f.write("\n\n[END OF ANALYSIS]")
        
        print(f"✅ Final report generated: {output_path}")
        print(f"📥 Downloading comprehensive analysis...")
        files.download(output_path)
        print("✅ Download complete!")
        
    except Exception as e:
        print(f"❌ Report generation failed: {e}")
    
    # Cleanup
    print("\n🧹 Cleaning up temporary files...")
    try:
        os.remove(raw_audio_path)
        os.remove(enhanced_audio_path)
        if os.path.exists(early_path):
            os.remove(early_path)
    except:
        pass
    
    print(f"\n{'='*60}")
    print("✅ FORENSIC ANALYSIS COMPLETE")
    print(f"{'='*60}\n")


# USAGE IN CELL 7:
# ===============
"""
# Cell 7: Process Video with Fixed Function
SKIP_SECONDS = 30  # Adjust as needed

# Use the fixed function
process_complete_enhanced_forensic_analysis_fixed(
    video_filename, 
    skip_seconds=SKIP_SECONDS
)
"""